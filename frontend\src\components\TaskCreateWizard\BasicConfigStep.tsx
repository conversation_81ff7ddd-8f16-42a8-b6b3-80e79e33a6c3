import React, { useState } from 'react';
import {
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  Button,
  Space,
  Card,
  Row,
  Col,
  Typography,
  Tag,
  Tooltip
} from 'antd';
import {
  InfoCircleOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';

import type { TaskConfig } from '../../types/taskCreate';
import { PRIORITY_OPTIONS } from '../../types/taskCreate';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

interface BasicConfigStepProps {
  basicConfig: {
    name: string;
    description: string;
    platform: string;
    priority: string;
    tags: string[];
  };
  taskConfig: TaskConfig;
  onConfigChange: (basic: any, task: TaskConfig) => void;
}

const BasicConfigStep: React.FC<BasicConfigStepProps> = ({
  basicConfig,
  taskConfig,
  onConfigChange
}) => {
  const [form] = Form.useForm();

  // 处理表单值变化，实时更新配置
  const handleFormChange = () => {
    // 获取当前表单值，不管验证是否通过都要更新状态
    const values = form.getFieldsValue();

    const updatedBasic = {
      name: values.name || '',
      description: values.description || '',
      platform: values.platform || 'mercadolibre',
      priority: values.priority || 'normal',
      tags: values.tags || []
    };

    const updatedTask = {
      ...taskConfig,
      platform: values.platform || 'mercadolibre',
      priority: values.priority || 'normal',
      retry_count: values.retry_count !== undefined && values.retry_count !== null ? values.retry_count : 3,
      timeout: values.timeout !== undefined && values.timeout !== null ? values.timeout : 300,
      concurrent_limit: values.concurrent_limit !== undefined && values.concurrent_limit !== null ? values.concurrent_limit : 5,
      batch_size: values.batch_size !== undefined && values.batch_size !== null ? values.batch_size : 10,
      enable_notifications: values.enable_notifications !== undefined ? values.enable_notifications : true
    };

    onConfigChange(updatedBasic, updatedTask);
  };

  return (
    <div>
      <Title level={4}>基础配置</Title>
      <Text type="secondary">设置任务的基本信息和执行参数</Text>

      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleFormChange}
        initialValues={{
          name: basicConfig.name,
          description: basicConfig.description,
          platform: basicConfig.platform,
          priority: basicConfig.priority,
          tags: basicConfig.tags,
          retry_count: taskConfig.retry_count,
          timeout: taskConfig.timeout,
          concurrent_limit: taskConfig.concurrent_limit,
          batch_size: taskConfig.batch_size,
          enable_notifications: taskConfig.enable_notifications
        }}
        style={{ marginTop: 24 }}
      >
        {/* 基本信息 */}
        <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="任务名称"
                rules={[
                  { required: true, message: '请输入任务名称' },
                  { max: 100, message: '任务名称不能超过100个字符' }
                ]}
              >
                <Input placeholder="请输入任务名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="platform"
                label="目标平台"
                rules={[{ required: true, message: '请选择目标平台' }]}
              >
                <Select placeholder="请选择目标平台">
                  <Option value="mercadolibre">MercadoLibre</Option>
                  <Option value="amazon">Amazon</Option>
                  <Option value="ebay">eBay</Option>
                  <Option value="aliexpress">AliExpress</Option>
                  <Option value="shopee">Shopee</Option>
                  <Option value="all">所有平台</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="任务描述"
          >
            <TextArea
              rows={3}
              placeholder="请输入任务描述（可选）"
              maxLength={500}
              showCount
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="priority"
                label="任务优先级"
                tooltip="优先级决定任务的执行顺序"
              >
                <Select placeholder="请选择优先级">
                  {PRIORITY_OPTIONS.map(option => (
                    <Option key={option.value} value={option.value}>
                      <Space>
                        <Tag color={option.color}>{option.label}</Tag>
                        <Text type="secondary">{option.description}</Text>
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="tags"
                label="任务标签"
                tooltip="用于分类和筛选任务"
              >
                <Select
                  mode="tags"
                  placeholder="请输入标签"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 执行参数 */}
        <Card title="执行参数" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                name="retry_count"
                label={
                  <Space>
                    重试次数
                    <Tooltip title="任务失败时的重试次数">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </Space>
                }
                rules={[
                  { type: 'number', min: 0, max: 10, message: '重试次数必须在0-10之间' }
                ]}
              >
                <InputNumber min={0} max={10} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="timeout"
                label={
                  <Space>
                    超时时间(秒)
                    <Tooltip title="单个URL的最大执行时间">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </Space>
                }
                rules={[
                  { type: 'number', min: 30, max: 3600, message: '超时时间必须在30-3600秒之间' }
                ]}
              >
                <InputNumber min={30} max={3600} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="concurrent_limit"
                label={
                  <Space>
                    并发限制
                    <Tooltip title="同时执行的最大任务数">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </Space>
                }
                rules={[
                  { type: 'number', min: 1, max: 50, message: '并发限制必须在1-50之间' }
                ]}
              >
                <InputNumber min={1} max={50} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="batch_size"
                label={
                  <Space>
                    批次大小
                    <Tooltip title="每批处理的URL数量">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </Space>
                }
                rules={[
                  { type: 'number', min: 1, max: 100, message: '批次大小必须在1-100之间' }
                ]}
              >
                <InputNumber min={1} max={100} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="enable_notifications"
            label="启用通知"
            valuePropName="checked"
            tooltip="任务完成或失败时发送通知"
          >
            <Switch />
          </Form.Item>
        </Card>
      </Form>
    </div>
  );
};

export default BasicConfigStep;
