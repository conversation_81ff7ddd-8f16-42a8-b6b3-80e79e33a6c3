"""
新架构：爬虫Worker API接口
提供Worker的CRUD操作和兼容性检查功能
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query

from ...schemas.crawler_worker import (
    CrawlerWorker, CrawlerWorkerCreate, CrawlerWorkerUpdate,
    CrawlerWorkerSummary, CrawlerWorkerDetail, CrawlerWorkerStats,
    WorkerCompatibilityCheck, WorkerGroupCompatibilityCheck,
    WorkerStatus, WorkerPriority
)
from ...services.crawler_worker_service import crawler_worker_service
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/crawler-workers", tags=["爬虫Worker管理"])


@router.post("/", response_model=CrawlerWorker, summary="创建Worker")
async def create_crawler_worker(worker_data: CrawlerWorkerCreate):
    """
    创建新的爬虫Worker
    
    - **worker_name**: Worker名称（必须唯一）
    - **crawler_config_id**: 爬取配置ID
    - **backend_config_id**: 后端配置ID
    - **allocated_concurrent**: 分配的并发数
    - **priority**: Worker优先级
    """
    try:
        worker = await crawler_worker_service.create_worker(worker_data)
        return worker
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create crawler worker: {e}")
        raise HTTPException(status_code=500, detail=f"创建Worker失败: {str(e)}")


@router.get("/", response_model=List[CrawlerWorkerSummary], summary="获取Worker列表")
async def list_crawler_workers(
    status: Optional[WorkerStatus] = Query(None, description="状态过滤"),
    priority: Optional[WorkerPriority] = Query(None, description="优先级过滤"),
    backend_config_id: Optional[str] = Query(None, description="后端配置ID过滤"),
    crawler_config_id: Optional[str] = Query(None, description="爬取配置ID过滤"),
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """
    获取爬虫Worker列表
    
    支持多种过滤条件和分页
    """
    try:
        workers = await crawler_worker_service.list_workers(
            status=status,
            priority=priority,
            backend_config_id=backend_config_id,
            crawler_config_id=crawler_config_id,
            limit=limit,
            offset=offset
        )
        return workers
    except Exception as e:
        logger.error(f"Failed to list crawler workers: {e}")
        raise HTTPException(status_code=500, detail=f"获取Worker列表失败: {str(e)}")


@router.get("/available", response_model=List[CrawlerWorkerSummary], summary="获取可用Worker")
async def get_available_workers():
    """
    获取所有可用的Worker
    
    只返回状态为活跃且有可用容量的Worker
    """
    try:
        workers = await crawler_worker_service.list_workers(
            status=WorkerStatus.ACTIVE,
            limit=1000
        )
        # 过滤出可用的Worker
        available_workers = [w for w in workers if w.is_available]
        return available_workers
    except Exception as e:
        logger.error(f"Failed to get available workers: {e}")
        raise HTTPException(status_code=500, detail=f"获取可用Worker失败: {str(e)}")


@router.get("/{worker_id}", response_model=CrawlerWorker, summary="获取Worker详情")
async def get_crawler_worker(worker_id: str):
    """
    根据ID获取Worker详情
    """
    try:
        worker = await crawler_worker_service.get_worker(worker_id)
        if not worker:
            raise HTTPException(status_code=404, detail="Worker不存在")
        return worker
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get crawler worker {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取Worker失败: {str(e)}")


@router.get("/{worker_id}/detail", response_model=CrawlerWorkerDetail, summary="获取Worker详细信息")
async def get_crawler_worker_detail(worker_id: str):
    """
    获取Worker详细信息（包含关联配置）
    """
    try:
        worker_detail = await crawler_worker_service.get_worker_detail(worker_id)
        if not worker_detail:
            raise HTTPException(status_code=404, detail="Worker不存在")
        return worker_detail
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get crawler worker detail {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取Worker详情失败: {str(e)}")


@router.get("/by-name/{worker_name}", response_model=CrawlerWorker, summary="根据名称获取Worker")
async def get_crawler_worker_by_name(worker_name: str):
    """
    根据名称获取Worker
    """
    try:
        worker = await crawler_worker_service.get_worker_by_name(worker_name)
        if not worker:
            raise HTTPException(status_code=404, detail="Worker不存在")
        return worker
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get crawler worker by name {worker_name}: {e}")
        raise HTTPException(status_code=500, detail=f"获取Worker失败: {str(e)}")


@router.put("/{worker_id}", response_model=CrawlerWorker, summary="更新Worker")
async def update_crawler_worker(worker_id: str, update_data: CrawlerWorkerUpdate):
    """
    更新Worker配置
    
    只更新提供的字段，其他字段保持不变
    """
    try:
        worker = await crawler_worker_service.update_worker(worker_id, update_data)
        if not worker:
            raise HTTPException(status_code=404, detail="Worker不存在")
        return worker
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update crawler worker {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"更新Worker失败: {str(e)}")


@router.delete("/{worker_id}", summary="删除Worker")
async def delete_crawler_worker(worker_id: str):
    """
    删除Worker
    
    注意：如果Worker正在执行任务，删除会失败
    """
    try:
        success = await crawler_worker_service.delete_worker(worker_id)
        if not success:
            raise HTTPException(status_code=400, detail="删除Worker失败")
        return {"message": "Worker删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete crawler worker {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"删除Worker失败: {str(e)}")


@router.post("/{worker_id}/check-compatibility", response_model=WorkerCompatibilityCheck, summary="检查Worker兼容性")
async def check_worker_compatibility(worker_id: str):
    """
    检查Worker兼容性
    
    验证Worker的配置是否有效，是否存在资源冲突
    """
    try:
        compatibility_check = await crawler_worker_service.check_worker_compatibility(worker_id)
        if not compatibility_check:
            raise HTTPException(status_code=404, detail="Worker不存在")
        return compatibility_check
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to check worker compatibility {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"兼容性检查失败: {str(e)}")


@router.get("/{worker_id}/conflicts", response_model=List[str], summary="获取冲突Worker列表")
async def get_conflicted_workers(worker_id: str):
    """
    获取与指定Worker冲突的Worker列表
    
    返回使用相同后端资源的其他Worker ID
    """
    try:
        conflicted_workers = await crawler_worker_service.get_conflicted_workers(worker_id)
        return conflicted_workers
    except Exception as e:
        logger.error(f"Failed to get conflicted workers for {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取冲突Worker失败: {str(e)}")


@router.patch("/{worker_id}/status", response_model=CrawlerWorker, summary="更新Worker状态")
async def update_worker_status(worker_id: str, status: WorkerStatus):
    """
    更新Worker状态
    
    - **active**: 活跃状态，可以接收任务
    - **inactive**: 非活跃状态，暂停接收任务
    - **busy**: 忙碌状态，正在执行任务
    - **error**: 错误状态，出现故障
    - **maintenance**: 维护状态，正在维护
    """
    try:
        update_data = CrawlerWorkerUpdate(status=status)
        worker = await crawler_worker_service.update_worker(worker_id, update_data)
        if not worker:
            raise HTTPException(status_code=404, detail="Worker不存在")
        return worker
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update worker status {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"更新状态失败: {str(e)}")


@router.post("/check-group-compatibility", response_model=WorkerGroupCompatibilityCheck, summary="检查Worker组兼容性")
async def check_worker_group_compatibility(worker_ids: List[str]):
    """
    检查Worker组兼容性
    
    验证多个Worker是否可以同时使用，是否存在资源冲突
    """
    try:
        from ...services.task_assignment_service import task_assignment_service
        compatibility_check = await task_assignment_service.check_worker_group_compatibility(worker_ids)
        return compatibility_check
    except Exception as e:
        logger.error(f"Failed to check worker group compatibility: {e}")
        raise HTTPException(status_code=500, detail=f"组兼容性检查失败: {str(e)}")


@router.get("/{worker_id}/performance", summary="获取Worker性能指标")
async def get_worker_performance(worker_id: str):
    """
    获取Worker性能指标
    
    包括任务统计、成功率、利用率等信息
    """
    try:
        worker = await crawler_worker_service.get_worker(worker_id)
        if not worker:
            raise HTTPException(status_code=404, detail="Worker不存在")
        
        performance = {
            "worker_id": worker_id,
            "worker_name": worker.worker_name,
            "status": worker.status,
            "priority": worker.priority,
            "allocated_concurrent": worker.allocated_concurrent,
            "current_tasks": worker.current_tasks,
            "available_capacity": worker.available_capacity,
            "utilization_rate": worker.utilization_rate,
            "total_tasks": worker.total_tasks,
            "completed_tasks": worker.completed_tasks,
            "failed_tasks": worker.failed_tasks,
            "success_rate": worker.success_rate,
            "avg_task_duration": worker.avg_task_duration,
            "health_score": worker.health_score,
            "last_task_time": worker.last_task_time,
            "last_health_check": worker.last_health_check,
            "error_count": worker.error_count
        }
        
        return performance
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get worker performance {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取性能指标失败: {str(e)}")


@router.post("/{worker_id}/reset-stats", response_model=CrawlerWorker, summary="重置Worker统计")
async def reset_worker_stats(worker_id: str):
    """
    重置Worker统计信息
    
    清零任务计数、错误计数等统计数据
    """
    try:
        update_data = CrawlerWorkerUpdate(
            total_tasks=0,
            completed_tasks=0,
            failed_tasks=0,
            current_tasks=0,
            avg_task_duration=0.0,
            success_rate=0.0,
            error_count=0
        )
        worker = await crawler_worker_service.update_worker(worker_id, update_data)
        if not worker:
            raise HTTPException(status_code=404, detail="Worker不存在")
        return worker
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to reset worker stats {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"重置统计失败: {str(e)}")


@router.get("/backend/{backend_config_id}/workers", response_model=List[CrawlerWorkerSummary], summary="获取后端的Worker列表")
async def get_workers_by_backend(backend_config_id: str):
    """
    获取使用指定后端配置的所有Worker
    """
    try:
        workers = await crawler_worker_service.list_workers(
            backend_config_id=backend_config_id,
            limit=1000
        )
        return workers
    except Exception as e:
        logger.error(f"Failed to get workers by backend {backend_config_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取后端Worker失败: {str(e)}")


@router.get("/config/{crawler_config_id}/workers", response_model=List[CrawlerWorkerSummary], summary="获取配置的Worker列表")
async def get_workers_by_config(crawler_config_id: str):
    """
    获取使用指定爬取配置的所有Worker
    """
    try:
        workers = await crawler_worker_service.list_workers(
            crawler_config_id=crawler_config_id,
            limit=1000
        )
        return workers
    except Exception as e:
        logger.error(f"Failed to get workers by config {crawler_config_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置Worker失败: {str(e)}")
