import React, { useState, useEffect } from 'react';
import { Layout as AntdLayout, Menu, Button, Avatar, Dropdown, Badge, Space, Typography } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  MonitorOutlined,
  <PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  SettingOutlined,
  FileTextOutlined,
  BellOutlined,
  UserOutlined,
  LogoutOutlined,
  SunOutlined,
  MoonOutlined,
  ApiOutlined,
  TeamOutlined,
  CodeOutlined,
  CloudServerOutlined,
} from '@ant-design/icons';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '../../store';
import { toggleSidebar, toggleTheme } from '../../store/slices/uiSlice';
import { setConnectionStatus } from '../../store/slices/systemSlice';
import { useWebSocket } from '../../services/websocket';
import NotificationPanel from '../NotificationPanel';
import './index.css';

const { Head<PERSON>, Sider, Content } = AntdLayout;
const { Text } = Typography;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();

  const { sidebarCollapsed, theme, notifications } = useAppSelector(state => state.ui);
  const { isConnected } = useAppSelector(state => state.system);

  const [notificationVisible, setNotificationVisible] = useState(false);

  // 使用WebSocket连接状态
  const { isConnected: wsConnected } = useWebSocket();

  // 响应式侧边栏控制
  useEffect(() => {
    const handleResize = () => {
      const isMobile = window.innerWidth <= 768;
      if (isMobile && !sidebarCollapsed) {
        // 在小屏幕下自动收起侧边栏
        dispatch(toggleSidebar());
      }
    };

    // 初始检查
    handleResize();

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, [sidebarCollapsed, dispatch]);

  // 同步WebSocket连接状态到Redux store
  useEffect(() => {
    dispatch(setConnectionStatus(wsConnected));
  }, [wsConnected, dispatch]);

  // 菜单项配置
  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: '/url-pool',
      icon: <DatabaseOutlined />,
      label: 'URL池管理',
    },
    {
      key: '/monitoring',
      icon: <MonitorOutlined />,
      label: '任务管理',
    },
    {
      key: '/config',
      icon: <SettingOutlined />,
      label: '配置管理',
      children: [
        {
          key: '/config/crawler',
          icon: <CodeOutlined />,
          label: '爬取配置',
        },
        {
          key: '/config/backend',
          icon: <CloudServerOutlined />,
          label: '后端配置',
        },
      ],
    },
    {
      key: '/worker-management',
      icon: <TeamOutlined />,
      label: 'Worker管理',
    },

    {
      key: '/data-analysis',
      icon: <BarChartOutlined />,
      label: '数据分析',
    },
    {
      key: '/crawler-settings',
      icon: <ApiOutlined />,
      label: '爬虫设置',
      children: [
        {
          key: '/crawler-settings/instance',
          icon: <ApiOutlined />,
          label: '实例配置管理',
        },
        {
          key: '/crawler-settings/monitor',
          icon: <MonitorOutlined />,
          label: '配置监控',
        },
        {
          key: '/crawler-settings/legacy',
          icon: <SettingOutlined />,
          label: '传统配置',
        },
      ],
    },
    {
      key: '/logs',
      icon: <FileTextOutlined />,
      label: '日志监控',
    },
  ];

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  const handleMenuClick = (key: string) => {
    // 处理子菜单项的路由映射
    switch (key) {
      case '/config/crawler':
      case '/config/backend':
        navigate('/config');
        break;
      case '/crawler-settings/legacy':
        navigate('/crawler-settings');
        break;
      default:
        navigate(key);
        break;
    }
  };

  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'profile':
        // 处理个人资料
        break;
      case 'settings':
        // 处理设置
        break;
      case 'logout':
        // 处理退出登录
        localStorage.removeItem('auth_token');
        navigate('/login');
        break;
    }
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <AntdLayout className={`app-layout ${theme}`}>
      <Sider
        trigger={null}
        collapsible
        collapsed={sidebarCollapsed}
        className="app-sider"
        width={240}
      >
        <div className="app-logo">
          <div className="logo-icon">M</div>
          {!sidebarCollapsed && <span>MonIt 爬虫系统</span>}
        </div>
        
        <Menu
          theme={theme === 'dark' ? 'dark' : 'light'}
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={({ key }) => handleMenuClick(key)}
        />
      </Sider>
      
      <AntdLayout className="app-main">
        <Header className="app-header">
          <div className="header-left">
            <Button
              type="text"
              icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => dispatch(toggleSidebar())}
              className="sidebar-trigger"
            />
            
            <div className="connection-status">
              <div className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`} />
              <Text type="secondary">
                {isConnected ? '已连接' : '连接断开'}
              </Text>
            </div>
          </div>
          
          <div className="header-right">
            <Space size="middle">
              <Button
                type="text"
                icon={theme === 'light' ? <MoonOutlined /> : <SunOutlined />}
                onClick={() => dispatch(toggleTheme())}
              />
              
              <Badge count={unreadCount} size="small">
                <Button
                  type="text"
                  icon={<BellOutlined />}
                  onClick={() => setNotificationVisible(true)}
                />
              </Badge>
              
              <Dropdown
                menu={{
                  items: userMenuItems,
                  onClick: handleUserMenuClick,
                }}
                placement="bottomRight"
              >
                <div className="user-info">
                  <Avatar size="small" icon={<UserOutlined />} />
                  <Text>管理员</Text>
                </div>
              </Dropdown>
            </Space>
          </div>
        </Header>
        
        <Content className="app-content">
          {children}
        </Content>
      </AntdLayout>
      
      <NotificationPanel
        visible={notificationVisible}
        onClose={() => setNotificationVisible(false)}
      />
    </AntdLayout>
  );
};

export default Layout;
