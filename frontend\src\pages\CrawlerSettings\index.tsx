import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Switch,
  Select,
  Button,
  Tabs,
  Space,
  Divider,
  Alert,
  Tag,
  Tooltip,
  Row,
  Col,
  message,
  Modal,
  Spin
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  ExperimentOutlined,
  MonitorOutlined,
  ApiOutlined,
  ThunderboltOutlined,
  SafetyOutlined,
  StarOutlined
} from '@ant-design/icons';
import {
  crawlerConfigService,
  CrawlerFullConfig
} from '../../services/crawlerConfigService';
import { DEFAULT_CRAWLER_CONFIG } from '../../config/defaultCrawlerConfig';

const { Option } = Select;
const { TextArea } = Input;

// 使用从服务中导入的接口，而不是重复定义
// interface CrawlerConfig 已在 crawlerConfigService 中定义

const CrawlerSettings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [, setConfig] = useState<CrawlerFullConfig | null>(null);
  const [activeTab, setActiveTab] = useState('api');
  const [testingConnection, setTestingConnection] = useState(false);
  const [originalApiKey, setOriginalApiKey] = useState<string>(''); // 存储原始API密钥

  // 获取默认配置
  const getDefaultConfig = () => { // eslint-disable-line @typescript-eslint/no-unused-vars
    // 使用统一的默认配置
    return {
      api: {
        base_url: 'http://localhost:11234',
        timeout: 30000,
        max_retries: 3
      },
      browser: DEFAULT_CRAWLER_CONFIG.browser,
      crawler: DEFAULT_CRAWLER_CONFIG.crawler,
      llm: DEFAULT_CRAWLER_CONFIG.llm,
      schema_extraction: DEFAULT_CRAWLER_CONFIG.schema_extraction,
      content_processing: DEFAULT_CRAWLER_CONFIG.content_processing,
      link_filtering: DEFAULT_CRAWLER_CONFIG.link_filtering,
      monitor: DEFAULT_CRAWLER_CONFIG.monitor
    };
  };

  useEffect(() => {
    loadConfig();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const loadConfig = async () => {
    try {
      setLoading(true);

      // 首先获取默认配置来获取完整的API密钥
      const defaultConfig = await crawlerConfigService.getDefaultConfigFromServer();
      setOriginalApiKey(defaultConfig.llm.api_key);

      // 然后获取当前配置
      const loadedConfig = await crawlerConfigService.getConfig();
      setConfig(loadedConfig);

      // 调试：检查从后端加载的配置
      console.log('=== 配置加载调试信息 ===');
      console.log('从后端加载的原始配置:', JSON.stringify(loadedConfig, null, 2));
      console.log('配置节点检查:');
      console.log('- API配置:', loadedConfig.api);
      console.log('- 浏览器配置:', loadedConfig.browser);
      console.log('- 爬虫配置:', loadedConfig.crawler);
      console.log('- LLM配置:', loadedConfig.llm);
      console.log('- Schema提取配置:', loadedConfig.schema_extraction);
      console.log('- 内容处理配置:', loadedConfig.content_processing);
      console.log('- 链接过滤配置:', loadedConfig.link_filtering);
      console.log('- 调度器配置:', loadedConfig.scheduler);
      console.log('- 监控配置:', loadedConfig.monitor);

      // 处理JSON对象字段的序列化
      const formValues = {
        ...loadedConfig,
        schema_extraction: {
          ...loadedConfig.schema_extraction,
          extraction_schema: JSON.stringify(loadedConfig.schema_extraction.extraction_schema, null, 2)
        }
      };

      console.log('处理后的表单值:', JSON.stringify(formValues, null, 2));
      console.log('=== 配置加载调试信息结束 ===');

      // 测试：分别设置不同类型的字段
      setTimeout(() => {
        console.log('=== 分类型字段设置测试 ===');

        // 1. 只设置数值字段
        const numericFields = {
          browser: {
            viewport_width: formValues.browser.viewport_width,
            viewport_height: formValues.browser.viewport_height,
            wait_for: formValues.browser.wait_for,
            timeout: formValues.browser.timeout
          }
        };
        form.setFieldsValue(numericFields);
        console.log('数值字段设置完成');

        setTimeout(() => {
          const afterNumeric = form.getFieldsValue();
          console.log('设置数值字段后:', JSON.stringify(afterNumeric, null, 2));

          // 2. 设置布尔字段（Switch组件）
          const booleanFields = {
            browser: {
              headless: formValues.browser.headless,
              verbose: formValues.browser.verbose,
              ignore_https_errors: formValues.browser.ignore_https_errors
            }
          };
          form.setFieldsValue(booleanFields);
          console.log('布尔字段设置完成');

          setTimeout(() => {
            const afterBoolean = form.getFieldsValue();
            console.log('设置布尔字段后:', JSON.stringify(afterBoolean, null, 2));

            // 3. 设置数组字段
            const arrayFields = {
              browser: {
                extra_args: formValues.browser.extra_args
              }
            };
            form.setFieldsValue(arrayFields);
            console.log('数组字段设置完成');

            setTimeout(() => {
              const afterArray = form.getFieldsValue();
              console.log('设置数组字段后:', JSON.stringify(afterArray, null, 2));
              console.log('=== 分类型字段设置测试结束 ===');
            }, 100);
          }, 100);
        }, 100);
      }, 500);
    } catch (error) {
      message.error('加载配置失败');
      console.error('Load config error:', error);
      // 使用默认配置作为备用
      const defaultConfig = crawlerConfigService.getDefaultConfig();
      setConfig(defaultConfig);
      setOriginalApiKey(defaultConfig.llm.api_key);

      // 处理默认配置的JSON序列化
      const defaultFormValues = {
        ...defaultConfig,
        schema_extraction: {
          ...defaultConfig.schema_extraction,
          extraction_schema: JSON.stringify(defaultConfig.schema_extraction.extraction_schema, null, 2)
        }
      };

      form.setFieldsValue(defaultFormValues);
    } finally {
      setLoading(false);
    }
  };

  const saveConfig = async () => {
    try {
      setLoading(true);

      // 调试：检查表单验证前的状态
      console.log('=== 表单验证调试信息 ===');
      const allFieldsValues = form.getFieldsValue();
      console.log('验证前的所有表单字段值:', JSON.stringify(allFieldsValues, null, 2));

      // 使用 getFieldsValue 获取所有字段值，包括不可见Tab页中的字段
      const values = form.getFieldsValue();
      console.log('获取的所有表单字段值:', JSON.stringify(values, null, 2));

      // 只验证必需字段
      await form.validateFields([['api', 'base_url']]);
      console.log('必需字段验证通过');
      console.log('=== 表单验证调试信息结束 ===');

      // 先检查和处理API密钥
      let apiKey = values.llm?.api_key;
      if (!apiKey || apiKey.startsWith('***')) {
        if (!originalApiKey) {
          message.error('API密钥无效，请重新加载页面');
          return;
        }
        apiKey = originalApiKey;
      }

      // 处理Schema JSON字段的反序列化
      let extractionSchema;
      try {
        const schemaStr = values.schema_extraction?.extraction_schema;
        if (!schemaStr || schemaStr.trim() === '') {
          extractionSchema = {};
        } else if (typeof schemaStr === 'string') {
          extractionSchema = JSON.parse(schemaStr);
        } else {
          extractionSchema = schemaStr; // 已经是对象
        }

        // 验证解析后的Schema是否为对象
        if (typeof extractionSchema !== 'object' || extractionSchema === null || Array.isArray(extractionSchema)) {
          message.error('Schema必须是一个有效的JSON对象');
          return;
        }
      } catch (e) {
        message.error('Schema JSON格式错误，请检查语法');
        console.error('Schema parsing error:', e);
        return;
      }

      // 构建最终配置，确保包含所有必需字段
      const configToSave = {
        ...values,
        llm: {
          ...values.llm,
          api_key: apiKey
        },
        schema_extraction: {
          ...values.schema_extraction,
          extraction_schema: extractionSchema
        },
        // 确保所有必需的配置节都存在
        api: values.api || {},
        browser: values.browser || {},
        crawler: values.crawler || {},
        content_processing: values.content_processing || {},
        link_filtering: values.link_filtering || {},
        scheduler: values.scheduler || {},
        monitor: values.monitor || {}
      };



      // 基本验证（简化版，主要验证由后端处理）
      if (!configToSave.llm?.api_key || configToSave.llm.api_key.length < 10) {
        message.error('API密钥无效，请检查配置');
        return;
      }

      // 调试信息：输出向后端传递的配置详情
      console.log('=== 传统爬虫设置页面 - 保存配置调试信息 ===');
      console.log('1. 原始表单数据 (values):', JSON.stringify(values, null, 2));
      console.log('2. 处理后的配置数据 (configToSave):', JSON.stringify(configToSave, null, 2));
      console.log('3. 配置数据大小:', JSON.stringify(configToSave).length, '字符');

      console.log('4. 详细配置节点分析:');
      console.log('- API配置:', JSON.stringify(configToSave.api, null, 2));
      console.log('- 浏览器配置:', JSON.stringify(configToSave.browser, null, 2));
      console.log('- 爬虫配置:', JSON.stringify(configToSave.crawler, null, 2));
      console.log('- LLM配置 (API密钥已隐藏):', JSON.stringify({
        ...configToSave.llm,
        api_key: configToSave.llm?.api_key ? `***${configToSave.llm.api_key.slice(-4)}` : 'undefined'
      }, null, 2));
      console.log('- Schema提取配置:', JSON.stringify(configToSave.schema_extraction, null, 2));
      console.log('- 内容处理配置:', JSON.stringify(configToSave.content_processing, null, 2));
      console.log('- 链接过滤配置:', JSON.stringify(configToSave.link_filtering, null, 2));
      console.log('- 调度器配置:', JSON.stringify(configToSave.scheduler, null, 2));
      console.log('- 监控配置:', JSON.stringify(configToSave.monitor, null, 2));

      console.log('5. 配置节点是否为空对象检查:');
      console.log('- API配置是否为空:', Object.keys(configToSave.api || {}).length === 0);
      console.log('- 浏览器配置是否为空:', Object.keys(configToSave.browser || {}).length === 0);
      console.log('- 爬虫配置是否为空:', Object.keys(configToSave.crawler || {}).length === 0);
      console.log('- LLM配置是否为空:', Object.keys(configToSave.llm || {}).length === 0);
      console.log('- Schema提取配置是否为空:', Object.keys(configToSave.schema_extraction || {}).length === 0);
      console.log('- 内容处理配置是否为空:', Object.keys(configToSave.content_processing || {}).length === 0);
      console.log('- 链接过滤配置是否为空:', Object.keys(configToSave.link_filtering || {}).length === 0);
      console.log('- 调度器配置是否为空:', Object.keys(configToSave.scheduler || {}).length === 0);
      console.log('- 监控配置是否为空:', Object.keys(configToSave.monitor || {}).length === 0);
      console.log('=== 调试信息结束 ===');

      await crawlerConfigService.updateConfig(configToSave);
      setConfig(configToSave);
      message.success('配置保存成功');
    } catch (error) {
      console.error('Save config error:', error);

      // 处理不同类型的错误
      if (error instanceof Error) {
        if (error.message.includes('422')) {
          message.error('配置数据格式错误，请检查所有字段');
        } else if (error.message.includes('401')) {
          message.error('API密钥无效，请检查配置');
        } else {
          message.error(`保存配置失败: ${error.message}`);
        }
      } else {
        message.error('保存配置失败，请重试');
      }
    } finally {
      setLoading(false);
    }
  };

  const resetConfig = async () => {
    try {
      setLoading(true);
      // 从服务器获取默认配置
      const defaultConfig = await crawlerConfigService.getDefaultConfigFromServer();

      // 处理默认配置的JSON序列化
      const defaultFormValues = {
        ...defaultConfig,
        schema_extraction: {
          ...defaultConfig.schema_extraction,
          extraction_schema: JSON.stringify(defaultConfig.schema_extraction.extraction_schema, null, 2)
        }
      };

      form.setFieldsValue(defaultFormValues);
      setConfig(defaultConfig);
      setOriginalApiKey(defaultConfig.llm.api_key); // 更新原始API密钥
      message.success('已重置为默认配置');
    } catch (error) {
      message.error('重置配置失败');
      console.error('Reset config error:', error);

      // 如果服务器请求失败，使用本地默认配置作为备用
      const localDefaultConfig = crawlerConfigService.getDefaultConfig();
      const localDefaultFormValues = {
        ...localDefaultConfig,
        schema_extraction: {
          ...localDefaultConfig.schema_extraction,
          extraction_schema: JSON.stringify(localDefaultConfig.schema_extraction.extraction_schema, null, 2)
        }
      };

      form.setFieldsValue(localDefaultFormValues);
      setConfig(localDefaultConfig);
      message.info('已重置为本地默认配置');
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    try {
      setTestingConnection(true);
      const result = await crawlerConfigService.testConnection();

      // 显示详细的测试结果
      const showDetailedResult = () => {
        const getStatusIcon = (status: string) => {
          switch (status) {
            case 'success': return '✅';
            case 'warning': return '⚠️';
            case 'error': return '❌';
            default: return '❓';
          }
        };

        const getStatusColor = (status: string) => {
          switch (status) {
            case 'success': return '#52c41a';
            case 'warning': return '#faad14';
            case 'error': return '#ff4d4f';
            default: return '#d9d9d9';
          }
        };

        Modal.info({
          title: '连接测试结果',
          width: 600,
          content: (
            <div style={{ marginTop: 16 }}>
              <div style={{ marginBottom: 16 }}>
                <h4 style={{ color: getStatusColor(result.overall.status) }}>
                  {getStatusIcon(result.overall.status)} 总体状态: {result.overall.message}
                </h4>
              </div>

              <div style={{ marginBottom: 12 }}>
                <strong>API服务器测试:</strong>
                <div style={{ marginLeft: 16, color: getStatusColor(result.api_server.status) }}>
                  {getStatusIcon(result.api_server.status)} {result.api_server.message}
                  {result.api_server.response_time > 0 && (
                    <span style={{ color: '#666', marginLeft: 8 }}>
                      ({result.api_server.response_time}ms)
                    </span>
                  )}
                </div>
              </div>

              <div style={{ marginBottom: 12 }}>
                <strong>LLM服务测试:</strong>
                <div style={{ marginLeft: 16, color: getStatusColor(result.llm_service.status) }}>
                  {getStatusIcon(result.llm_service.status)} {result.llm_service.message}
                  {result.llm_service.response_time > 0 && (
                    <span style={{ color: '#666', marginLeft: 8 }}>
                      ({result.llm_service.response_time}ms)
                    </span>
                  )}
                </div>
              </div>

              <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                <small style={{ color: '#666' }}>
                  💡 提示: 如果测试失败，请检查配置中的API地址和密钥是否正确，以及网络连接是否正常。
                </small>
              </div>
            </div>
          ),
          onOk() {}
        });
      };

      // 显示简要消息
      if (result.overall.status === 'success') {
        message.success('连接测试成功');
      } else if (result.overall.status === 'warning') {
        message.warning('连接测试部分成功，请查看详细结果');
      } else {
        message.error('连接测试失败，请查看详细结果');
      }

      // 显示详细结果
      showDetailedResult();

    } catch (error) {
      message.error('连接测试失败');
      console.error('Connection test error:', error);

      // 显示错误详情
      Modal.error({
        title: '连接测试错误',
        content: (
          <div>
            <p>测试过程中发生错误，请检查以下项目：</p>
            <ul>
              <li>网络连接是否正常</li>
              <li>API服务器地址是否正确</li>
              <li>API密钥是否有效</li>
              <li>服务器是否正在运行</li>
            </ul>
            <div style={{ marginTop: 12, padding: 8, backgroundColor: '#fff2f0', border: '1px solid #ffccc7', borderRadius: 4 }}>
              <small style={{ color: '#a8071a' }}>
                错误详情: {error instanceof Error ? error.message : '未知错误'}
              </small>
            </div>
          </div>
        )
      });
    } finally {
      setTestingConnection(false);
    }
  };

  const applyPreset = (presetName: string) => {
    const presets = crawlerConfigService.getConfigPresets();
    const preset = presets[presetName];
    if (preset) {
      const currentValues = form.getFieldsValue();
      // 深度合并配置
      const mergedConfig = deepMerge(currentValues, preset);
      form.setFieldsValue(mergedConfig);
      message.success(`已应用${presetName}预设配置`);
    }
  };

  // 深度合并函数
  const deepMerge = (target: any, source: any): any => {
    const result = { ...target };
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    return result;
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <Space>
            <SettingOutlined />
            爬虫系统配置
            <Tag color="blue">Crawl4AI</Tag>
          </Space>
        }
        extra={
          <Space>
            <Select
              placeholder="选择预设配置"
              style={{ width: 120 }}
              onChange={applyPreset}
              allowClear
            >
              <Option value="高性能">
                <Space>
                  <ThunderboltOutlined />
                  高性能
                </Space>
              </Option>
              <Option value="高质量">
                <Space>
                  <StarOutlined />
                  高质量
                </Space>
              </Option>
              <Option value="反检测">
                <Space>
                  <SafetyOutlined />
                  反检测
                </Space>
              </Option>
            </Select>
            <Button
              icon={<ExperimentOutlined />}
              onClick={testConnection}
              loading={testingConnection}
            >
              测试连接
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={resetConfig}
            >
              重置默认
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={saveConfig}
              loading={loading}
            >
              保存配置
            </Button>
          </Space>
        }
      >
        <Alert
          message="爬虫配置说明"
          description="这里配置的是Crawl4AI爬虫系统的各项参数，包括浏览器设置、爬取策略、LLM提取等。修改后需要保存才能生效。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Spin spinning={loading} tip="加载配置中...">
          <Form
            form={form}
            layout="vertical"
          >
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={[
              {
                key: 'api',
                label: <span><ApiOutlined />API配置</span>,
                children: (
                  <div>
                    <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="API基础URL"
                    name={['api', 'base_url']}
                    rules={[{ required: true, message: '请输入API基础URL' }]}
                  >
                    <Input placeholder="http://localhost:11234" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="超时时间(ms)"
                    name={['api', 'timeout']}
                  >
                    <InputNumber min={1000} max={300000} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="最大重试次数"
                    name={['api', 'max_retries']}
                  >
                    <InputNumber min={0} max={10} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                    </Row>
                  </div>
                ),
              },
              {
                key: 'browser',
                label: <span><MonitorOutlined />浏览器配置</span>,
                children: (
                  <div>
                    <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="无头模式"
                    name={['browser', 'headless']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="详细日志"
                    name={['browser', 'verbose']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="忽略HTTPS错误"
                    name={['browser', 'ignore_https_errors']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item
                    label="视口宽度"
                    name={['browser', 'viewport_width']}
                  >
                    <InputNumber min={800} max={3840} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="视口高度"
                    name={['browser', 'viewport_height']}
                  >
                    <InputNumber min={600} max={2160} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="等待时间(秒)"
                    name={['browser', 'wait_for']}
                  >
                    <InputNumber min={0} max={30} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="超时时间(秒)"
                    name={['browser', 'timeout']}
                  >
                    <InputNumber min={5} max={300} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label={
                  <Space>
                    浏览器启动参数
                    <Tooltip title="用于反检测的浏览器启动参数，每行一个">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                }
                name={['browser', 'extra_args']}
              >
                <Select
                  mode="tags"
                  style={{ width: '100%' }}
                  placeholder="输入浏览器启动参数"
                  tokenSeparators={['\n']}
                />
                    </Form.Item>
                  </div>
                ),
              },
              {
                key: 'crawler',
                label: <span><ExperimentOutlined />爬虫配置</span>,
                children: (
                  <div>
                    <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="模拟用户行为"
                    name={['crawler', 'simulate_user']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="智能处理模式"
                    name={['crawler', 'magic']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="覆盖导航器"
                    name={['crawler', 'override_navigator']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="移除覆盖元素"
                    name={['crawler', 'remove_overlay_elements']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="扫描整页"
                    name={['crawler', 'scan_full_page']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="处理iframe"
                    name={['crawler', 'process_iframes']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item
                    label="页面超时(ms)"
                    name={['crawler', 'page_timeout']}
                  >
                    <InputNumber min={10000} max={300000} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="返回前延迟(s)"
                    name={['crawler', 'delay_before_return_html']}
                  >
                    <InputNumber min={0} max={10} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="滚动延迟(s)"
                    name={['crawler', 'scroll_delay']}
                  >
                    <InputNumber min={0} max={5} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="等待条件"
                    name={['crawler', 'wait_until']}
                  >
                    <Select>
                      <Option value="domcontentloaded">DOM内容加载</Option>
                      <Option value="load">完全加载</Option>
                      <Option value="networkidle0">网络空闲0</Option>
                      <Option value="networkidle2">网络空闲2</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="图片质量阈值"
                    name={['crawler', 'image_score_threshold']}
                  >
                    <InputNumber min={0} max={100} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="表格质量阈值"
                    name={['crawler', 'table_score_threshold']}
                  >
                    <InputNumber min={0} max={20} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="缓存模式"
                    name={['crawler', 'cache_mode']}
                  >
                    <Select>
                      <Option value="BYPASS">绕过缓存</Option>
                      <Option value="ENABLED">启用缓存</Option>
                      <Option value="DISABLED">禁用缓存</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Divider>高级设置</Divider>

              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="检查robots.txt"
                    name={['crawler', 'check_robots_txt']}
                    valuePropName="checked"
                    tooltip="是否检查网站的robots.txt文件"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="获取SSL证书"
                    name={['crawler', 'fetch_ssl_certificate']}
                    valuePropName="checked"
                    tooltip="是否获取网站的SSL证书信息"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="绕过缓存"
                    name={['crawler', 'bypass_cache']}
                    valuePropName="checked"
                    tooltip="是否绕过所有缓存机制"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="提取策略"
                    name={['crawler', 'extraction_strategy']}
                    tooltip="内容提取策略"
                  >
                    <Select>
                      <Option value="LLMExtractionStrategy">LLM提取</Option>
                      <Option value="CosineStrategy">余弦相似度</Option>
                      <Option value="NoExtractionStrategy">无提取</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="分块策略"
                    name={['crawler', 'chunking_strategy']}
                    tooltip="内容分块策略"
                  >
                    <Select>
                      <Option value="IdentityChunking">原样分块</Option>
                      <Option value="RegexChunking">正则分块</Option>
                      <Option value="NlpSentenceChunking">NLP句子分块</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="Markdown生成器"
                    name={['crawler', 'markdown_generator']}
                    tooltip="Markdown内容生成器"
                  >
                    <Select>
                      <Option value="DefaultMarkdownGenerator">默认生成器</Option>
                      <Option value="CustomMarkdownGenerator">自定义生成器</Option>
                    </Select>
                  </Form.Item>
                </Col>
                    </Row>
                  </div>
                ),
              },
              {
                key: 'llm',
                label: <span><ApiOutlined />LLM配置</span>,
                children: (
                  <div>
                    <Alert
                message="LLM配置"
                description="配置用于内容提取的大语言模型参数。API密钥将被安全存储。"
                type="warning"
                showIcon
                style={{ marginBottom: 16 }}
              />

              <Form.Item
                label={
                  <Space>
                    提取查询指令
                    <Tooltip title="告诉LLM要提取什么内容的指令">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                }
                name={['llm', 'query']}
                rules={[{ required: true, message: '请输入提取查询指令' }]}
              >
                <TextArea
                  rows={3}
                  placeholder="提取页面中所有文章的标题、作者和发布时间"
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="提供商"
                    name={['llm', 'provider']}
                    rules={[{ required: true, message: '请选择LLM提供商' }]}
                  >
                    <Select>
                      <Option value="openai">OpenAI</Option>
                      <Option value="anthropic">Anthropic</Option>
                      <Option value="google">Google</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="模型"
                    name={['llm', 'model']}
                    rules={[{ required: true, message: '请输入模型名称' }]}
                  >
                    <Input placeholder="deepseek-v3-0324" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="API基础URL"
                    name={['llm', 'base_url']}
                    rules={[{ required: true, message: '请输入API基础URL' }]}
                  >
                    <Input placeholder="https://api.openai.com/v1" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="API密钥"
                name={['llm', 'api_key']}
                rules={[{ required: true, message: '请输入API密钥' }]}
              >
                <Input.Password placeholder="sk-..." />
              </Form.Item>

              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="温度"
                    name={['llm', 'temperature']}
                    tooltip="控制生成的随机性，0为最确定性"
                  >
                    <InputNumber min={0} max={2} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="最大Token数"
                    name={['llm', 'max_tokens']}
                  >
                    <InputNumber min={100} max={32768} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="Top-p"
                    name={['llm', 'top_p']}
                    tooltip="核采样参数，控制生成的多样性"
                  >
                    <InputNumber min={0} max={1} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                    </Row>
                  </div>
                ),
              },
              {
                key: 'scheduler',
                label: <span><MonitorOutlined />调度配置</span>,
                children: (
                  <div>
                    <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="并发数量"
                    name={['scheduler', 'semaphore_count']}
                    tooltip="同时处理的URL数量"
                  >
                    <InputNumber min={1} max={20} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="平均延迟(s)"
                    name={['scheduler', 'mean_delay']}
                    tooltip="请求之间的平均延迟时间"
                  >
                    <InputNumber min={0} max={10} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="最大范围"
                    name={['scheduler', 'max_range']}
                    tooltip="延迟时间的随机范围"
                  >
                    <InputNumber min={1} max={10} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="线程池大小"
                    name={['scheduler', 'pool_size']}
                    tooltip="异步任务处理的线程池大小"
                  >
                    <InputNumber min={5} max={50} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="内存阈值(MB)"
                    name={['scheduler', 'memory_threshold']}
                    tooltip="内存使用阈值，超过时会触发清理"
                  >
                    <InputNumber min={1024} max={16384} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Divider>监控设置</Divider>

              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="显示模式"
                    name={['monitor', 'display_mode']}
                  >
                    <Select>
                      <Option value="simple">简单</Option>
                      <Option value="detailed">详细</Option>
                      <Option value="debug">调试</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="显示进度"
                    name={['monitor', 'show_progress']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="记录错误"
                    name={['monitor', 'log_errors']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                    </Row>
                  </div>
                ),
              },
              {
                key: 'schema',
                label: <span><ExperimentOutlined />Schema提取</span>,
                children: (
                  <div>
                    <Alert
                message="Schema提取配置"
                description="配置结构化数据提取的Schema和指令。这些设置决定了如何从页面中提取结构化数据。"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />

              <Form.Item
                label={
                  <Space>
                    提取指令
                    <Tooltip title="详细的提取指令，告诉系统如何提取数据">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                }
                name={['schema_extraction', 'instructions']}
              >
                <TextArea
                  rows={6}
                  placeholder="请严格按照以下条件提取商品信息：&#10;1. 只提取页面主要展示的商品信息&#10;2. 忽略推荐商品、相关商品&#10;..."
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="验证Schema"
                    name={['schema_extraction', 'validate_schema']}
                    valuePropName="checked"
                    tooltip="是否验证提取的数据符合Schema定义"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="返回原始数据"
                    name={['schema_extraction', 'return_raw']}
                    valuePropName="checked"
                    tooltip="是否同时返回原始的未处理数据"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label={
                  <Space>
                    提取Schema (JSON)
                    <Tooltip title="定义要提取的数据结构的JSON Schema，必须是有效的JSON格式">
                      <InfoCircleOutlined />
                    </Tooltip>
                    <Button
                      size="small"
                      type="link"
                      onClick={() => {
                        const currentValue = form.getFieldValue(['schema_extraction', 'extraction_schema']);
                        if (currentValue) {
                          try {
                            const parsed = JSON.parse(currentValue);
                            const formatted = JSON.stringify(parsed, null, 2);
                            form.setFieldValue(['schema_extraction', 'extraction_schema'], formatted);
                            message.success('JSON格式化成功');
                          } catch (e) {
                            message.error('JSON格式错误，无法格式化');
                          }
                        }
                      }}
                    >
                      格式化
                    </Button>
                  </Space>
                }
                name={['schema_extraction', 'extraction_schema']}
                rules={[
                  {
                    validator: (_, value) => {
                      if (!value || value.trim() === '') {
                        return Promise.resolve();
                      }
                      try {
                        JSON.parse(value);
                        return Promise.resolve();
                      } catch (e) {
                        return Promise.reject(new Error('请输入有效的JSON格式'));
                      }
                    }
                  }
                ]}
              >
                <TextArea
                  rows={12}
                  placeholder='{"type": "object", "properties": {"title": {"type": "string"}, "content": {"type": "string"}}}'
                  style={{ fontFamily: 'Monaco, Consolas, "Courier New", monospace' }}
                />
                    </Form.Item>
                  </div>
                ),
              },
              {
                key: 'content',
                label: <span><SettingOutlined />内容处理</span>,
                children: (
                  <div>
                    <Alert
                message="内容处理配置"
                description="配置页面内容的处理方式，包括过滤、清理和格式化选项。"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />

              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="词数阈值"
                    name={['content_processing', 'word_count_threshold']}
                    tooltip="内容块的最小词数阈值"
                  >
                    <InputNumber min={0} max={10000} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="解析器类型"
                    name={['content_processing', 'parser_type']}
                  >
                    <Select>
                      <Option value="lxml">lxml</Option>
                      <Option value="html.parser">html.parser</Option>
                      <Option value="html5lib">html5lib</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="CSS选择器"
                    name={['content_processing', 'css_selector']}
                    tooltip="用于选择特定内容的CSS选择器"
                  >
                    <Input placeholder=".content, #main" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="排除的标签"
                    name={['content_processing', 'excluded_tags']}
                    tooltip="要排除的HTML标签列表"
                  >
                    <Select
                      mode="tags"
                      style={{ width: '100%' }}
                      placeholder="nav, footer, aside"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="排除选择器"
                    name={['content_processing', 'excluded_selector']}
                    tooltip="要排除的CSS选择器"
                  >
                    <Input placeholder=".ads, .sidebar" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item
                    label="移除表单"
                    name={['content_processing', 'remove_forms']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="仅文本"
                    name={['content_processing', 'only_text']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="美化输出"
                    name={['content_processing', 'prettify']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="保留数据属性"
                    name={['content_processing', 'keep_data_attributes']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                    </Row>
                  </div>
                ),
              },
              {
                key: 'filtering',
                label: <span><SafetyOutlined />链接过滤</span>,
                children: (
                  <div>
                    <Alert
                message="链接过滤配置"
                description="配置链接和图片的过滤规则，可以排除不需要的内容和域名。"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />

              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="排除外部链接"
                    name={['link_filtering', 'exclude_external_links']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="排除内部链接"
                    name={['link_filtering', 'exclude_internal_links']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="排除社交媒体链接"
                    name={['link_filtering', 'exclude_social_media_links']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="排除域名列表"
                name={['link_filtering', 'exclude_domains']}
                tooltip="要排除的域名列表"
              >
                <Select
                  mode="tags"
                  style={{ width: '100%' }}
                  placeholder="example.com, ads.google.com"
                />
              </Form.Item>

              <Form.Item
                label="社交媒体域名"
                name={['link_filtering', 'social_media_domains']}
                tooltip="社交媒体域名列表"
              >
                <Select
                  mode="tags"
                  style={{ width: '100%' }}
                  placeholder="facebook.com, twitter.com, instagram.com"
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="排除外部图片"
                    name={['link_filtering', 'exclude_external_images']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="排除所有图片"
                    name={['link_filtering', 'exclude_all_images']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="图片评分阈值"
                    name={['link_filtering', 'image_score_threshold']}
                    tooltip="图片质量评分阈值"
                  >
                    <InputNumber min={0} max={100} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="图片描述最小词数"
                    name={['link_filtering', 'image_description_min_word_threshold']}
                    tooltip="图片描述的最小词数要求"
                  >
                    <InputNumber min={0} max={200} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="表格评分阈值"
                    name={['link_filtering', 'table_score_threshold']}
                    tooltip="表格质量评分阈值"
                  >
                    <InputNumber min={0} max={20} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                    </Row>
                  </div>
                ),
              },
            ]}
          />
          </Form>
        </Spin>
      </Card>
    </div>
  );
};

export default CrawlerSettings;
