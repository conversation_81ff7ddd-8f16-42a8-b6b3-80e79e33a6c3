/**
 * 新架构配置管理页面
 * 集成爬取配置和后端配置管理功能
 */

import React, { useState, useEffect } from 'react';
import {
  Typography,
  Card,
  Tabs,
  Button,
  Space,
  Table,
  Tag,
  Modal,
  message,
  Row,
  Col,
  Tooltip,
  Popconfirm,
  Badge,
  Statistic,
  Progress,
  Spin
} from 'antd';
import {
  SettingOutlined,
  CodeOutlined,
  PlayCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  CopyOutlined,
  PlusOutlined,
  CheckCircleOutlined,
  ReloadOutlined,
  EyeOutlined,
  ThunderboltOutlined,
  CloudServerOutlined
} from '@ant-design/icons';
import type { TableColumnsType } from 'antd';

// 导入新架构的API和类型
import {
  crawlerConfigApi,
  backendConfigApi,
  CrawlerConfigSummary,
  BackendConfigSummary,
  CrawlerConfigCreate,
  CrawlerConfigUpdate,
  ConfigStatus,
  BackendStatus,
  PerformanceLevel
} from '../../services';

// 导入子组件
import CrawlerConfigForm from './components/CrawlerConfigForm';
import BackendConfigForm from './components/BackendConfigForm';

const { Title, Text } = Typography;

const NewConfiguration: React.FC = () => {
  // 状态管理
  const [activeTab, setActiveTab] = useState('crawler-configs');
  const [crawlerConfigs, setCrawlerConfigs] = useState<CrawlerConfigSummary[]>([]);
  const [backendConfigs, setBackendConfigs] = useState<BackendConfigSummary[]>([]);
  const [loading, setLoading] = useState(false);
  const [, setSelectedCrawlerConfig] = useState<string | null>(null);
  const [, setSelectedBackendConfig] = useState<string | null>(null);
  const [crawlerConfigModalVisible, setCrawlerConfigModalVisible] = useState(false);
  const [backendConfigModalVisible, setBackendConfigModalVisible] = useState(false);
  const [editingCrawlerConfig, setEditingCrawlerConfig] = useState<string | null>(null);
  const [editingBackendConfig, setEditingBackendConfig] = useState<string | null>(null);

  // 初始化数据
  useEffect(() => {
    fetchConfigs();
  }, []);

  // 获取配置数据
  const fetchConfigs = async () => {
    setLoading(true);
    try {
      const [crawlerConfigsData, backendConfigsData] = await Promise.all([
        crawlerConfigApi.getConfigs(),
        backendConfigApi.getConfigs()
      ]);
      
      setCrawlerConfigs(crawlerConfigsData);
      setBackendConfigs(backendConfigsData);
    } catch (error) {
      message.error(`获取配置数据失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 爬取配置相关操作
  const handleCreateCrawlerConfig = () => {
    setEditingCrawlerConfig(null);
    setCrawlerConfigModalVisible(true);
  };

  const handleEditCrawlerConfig = (configId: string) => {
    setEditingCrawlerConfig(configId);
    setCrawlerConfigModalVisible(true);
  };

  const handleDeleteCrawlerConfig = async (configId: string) => {
    try {
      await crawlerConfigApi.deleteConfig(configId);
      message.success('删除爬取配置成功');
      fetchConfigs();
    } catch (error) {
      message.error(`删除爬取配置失败: ${error}`);
    }
  };

  const handleDuplicateCrawlerConfig = async (configId: string) => {
    try {
      const config = await crawlerConfigApi.getConfig(configId);
      const newName = `${config.config_name} - 副本`;
      await crawlerConfigApi.duplicateConfig(configId, newName);
      message.success('复制爬取配置成功');
      fetchConfigs();
    } catch (error) {
      message.error(`复制爬取配置失败: ${error}`);
    }
  };

  const handleValidateCrawlerConfig = async (configId: string) => {
    try {
      const result = await crawlerConfigApi.validateConfig(configId);
      if (result.is_valid) {
        message.success('配置验证通过');
      } else {
        Modal.warning({
          title: '配置验证失败',
          content: (
            <div>
              <p>发现以下问题：</p>
              <ul>
                {result.errors.map((error, index) => (
                  <li key={index} style={{ color: '#ff4d4f' }}>{error}</li>
                ))}
              </ul>
              {result.warnings.length > 0 && (
                <>
                  <p>警告：</p>
                  <ul>
                    {result.warnings.map((warning, index) => (
                      <li key={index} style={{ color: '#faad14' }}>{warning}</li>
                    ))}
                  </ul>
                </>
              )}
            </div>
          ),
        });
      }
    } catch (error) {
      message.error(`验证配置失败: ${error}`);
    }
  };

  // 后端配置相关操作
  const handleCreateBackendConfig = () => {
    setEditingBackendConfig(null);
    setBackendConfigModalVisible(true);
  };

  const handleEditBackendConfig = (configId: string) => {
    setEditingBackendConfig(configId);
    setBackendConfigModalVisible(true);
  };

  const handleDeleteBackendConfig = async (configId: string) => {
    try {
      await backendConfigApi.deleteConfig(configId);
      message.success('删除后端配置成功');
      fetchConfigs();
    } catch (error) {
      message.error(`删除后端配置失败: ${error}`);
    }
  };

  const handleTestBackendConnection = async (configId: string) => {
    try {
      const config = await backendConfigApi.getConfig(configId);
      const result = await backendConfigApi.testConnection({
        backend_name: config.backend_name,
        api_endpoint: config.api_endpoint,
        timeout: config.timeout,
        max_retries: config.max_retries,
        auth_config: config.auth_config
      });
      
      if (result.success) {
        message.success(`连接测试成功，响应时间: ${result.response_time}ms`);
      } else {
        message.error(`连接测试失败: ${result.error_message}`);
      }
    } catch (error) {
      message.error(`连接测试失败: ${error}`);
    }
  };

  const handlePerformHealthCheck = async (configId: string) => {
    try {
      const result = await backendConfigApi.performHealthCheck(configId);
      if (result.is_healthy) {
        message.success(`健康检查通过，响应时间: ${result.response_time}ms`);
      } else {
        message.warning(`健康检查失败: ${result.error_message}`);
      }
      fetchConfigs(); // 刷新数据以显示最新的健康状态
    } catch (error) {
      message.error(`健康检查失败: ${error}`);
    }
  };

  // 状态标签渲染
  const renderConfigStatus = (status: ConfigStatus) => {
    const statusConfig = {
      active: { color: 'green', text: '活跃' },
      inactive: { color: 'default', text: '未激活' },
      draft: { color: 'orange', text: '草稿' },
      deprecated: { color: 'red', text: '已废弃' }
    };
    
    const config = statusConfig[status] || statusConfig.inactive;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const renderBackendStatus = (status: BackendStatus) => {
    const statusConfig = {
      active: { color: 'green', text: '活跃' },
      inactive: { color: 'default', text: '未激活' },
      error: { color: 'red', text: '错误' },
      maintenance: { color: 'orange', text: '维护中' }
    };
    
    const config = statusConfig[status] || statusConfig.inactive;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const renderPerformanceLevel = (level: PerformanceLevel) => {
    const levelConfig = {
      low: { color: 'default', text: '低' },
      medium: { color: 'blue', text: '中' },
      high: { color: 'green', text: '高' },
      ultra: { color: 'purple', text: '超高' }
    };
    
    const config = levelConfig[level] || levelConfig.low;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 爬取配置表格列定义
  const crawlerConfigColumns: TableColumnsType<CrawlerConfigSummary> = [
    {
      title: '配置名称',
      dataIndex: 'config_name',
      key: 'config_name',
      render: (text, record) => (
        <Space>
          <Text strong>{text}</Text>
          {record.tags.map(tag => (
            <Tag key={tag}>{tag}</Tag>
          ))}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: renderConfigStatus,
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
    },
    {
      title: '使用次数',
      dataIndex: 'usage_count',
      key: 'usage_count',
      render: (count) => <Badge count={count} showZero />,
    },
    {
      title: '最后使用',
      dataIndex: 'last_used',
      key: 'last_used',
      render: (date) => date ? new Date(date).toLocaleString() : '从未使用',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => date ? new Date(date).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              onClick={() => setSelectedCrawlerConfig(record.config_id)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => handleEditCrawlerConfig(record.config_id)}
            />
          </Tooltip>
          <Tooltip title="验证配置">
            <Button 
              type="text" 
              icon={<CheckCircleOutlined />} 
              onClick={() => handleValidateCrawlerConfig(record.config_id)}
            />
          </Tooltip>
          <Tooltip title="复制">
            <Button 
              type="text" 
              icon={<CopyOutlined />} 
              onClick={() => handleDuplicateCrawlerConfig(record.config_id)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个配置吗？"
            onConfirm={() => handleDeleteCrawlerConfig(record.config_id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 后端配置表格列定义
  const backendConfigColumns: TableColumnsType<BackendConfigSummary> = [
    {
      title: '配置名称',
      dataIndex: 'backend_name',
      key: 'backend_name',
      render: (text) => <Text strong>{text}</Text>,
    },
    {
      title: 'API端点',
      dataIndex: 'api_endpoint',
      key: 'api_endpoint',
      render: (url) => (
        <Tooltip title={url}>
          <Text code style={{ maxWidth: 200 }} ellipsis>{url}</Text>
        </Tooltip>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: renderBackendStatus,
    },
    {
      title: '性能等级',
      dataIndex: 'performance_level',
      key: 'performance_level',
      render: renderPerformanceLevel,
    },
    {
      title: '健康评分',
      dataIndex: 'health_score',
      key: 'health_score',
      render: (score) => (
        <Progress
          percent={score * 100}
          size="small"
          status={score >= 0.8 ? 'success' : score >= 0.5 ? 'normal' : 'exception'}
          format={() => `${(score * 100).toFixed(0)}%`}
        />
      ),
    },
    {
      title: '负载',
      key: 'load',
      render: (_, record) => (
        <Progress
          percent={(record.current_load / record.max_concurrent) * 100}
          size="small"
          format={() => `${record.current_load}/${record.max_concurrent}`}
        />
      ),
    },
    {
      title: '成功率',
      dataIndex: 'success_rate',
      key: 'success_rate',
      render: (rate) => `${(rate * 100).toFixed(1)}%`,
    },
    {
      title: '平均响应时间',
      dataIndex: 'avg_response_time',
      key: 'avg_response_time',
      render: (time) => `${time.toFixed(0)}ms`,
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              onClick={() => setSelectedBackendConfig(record.backend_id)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => handleEditBackendConfig(record.backend_id)}
            />
          </Tooltip>
          <Tooltip title="测试连接">
            <Button 
              type="text" 
              icon={<ThunderboltOutlined />} 
              onClick={() => handleTestBackendConnection(record.backend_id)}
            />
          </Tooltip>
          <Tooltip title="健康检查">
            <Button 
              type="text" 
              icon={<CheckCircleOutlined />} 
              onClick={() => handlePerformHealthCheck(record.backend_id)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个配置吗？"
            onConfirm={() => handleDeleteBackendConfig(record.backend_id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <SettingOutlined /> 配置管理
        </Title>
        <Text type="secondary">
          管理爬取配置和后端配置，支持创建、编辑、验证和监控配置状态
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="爬取配置"
              value={crawlerConfigs.length}
              prefix={<CodeOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="后端配置"
              value={backendConfigs.length}
              prefix={<CloudServerOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃配置"
              value={crawlerConfigs.filter(c => c.status === 'active').length + 
                     backendConfigs.filter(c => c.status === 'active').length}
              prefix={<PlayCircleOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="健康后端"
              value={backendConfigs.filter(c => c.health_score >= 0.8).length}
              prefix={<CheckCircleOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          tabBarExtraContent={
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchConfigs}
                loading={loading}
              >
                刷新
              </Button>
              {activeTab === 'crawler-configs' && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleCreateCrawlerConfig}
                >
                  新建爬取配置
                </Button>
              )}
              {activeTab === 'backend-configs' && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleCreateBackendConfig}
                >
                  新建后端配置
                </Button>
              )}
            </Space>
          }
          items={[
            {
              key: 'crawler-configs',
              label: (
                <span>
                  <CodeOutlined />
                  爬取配置
                </span>
              ),
              children: (
                <Spin spinning={loading}>
                  <Table
                    columns={crawlerConfigColumns}
                    dataSource={crawlerConfigs}
                    rowKey="config_id"
                    pagination={{
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total) => `共 ${total} 条记录`,
                    }}
                  />
                </Spin>
              ),
            },
            {
              key: 'backend-configs',
              label: (
                <span>
                  <CloudServerOutlined />
                  后端配置
                </span>
              ),
              children: (
                <Spin spinning={loading}>
                  <Table
                    columns={backendConfigColumns}
                    dataSource={backendConfigs}
                    rowKey="backend_id"
                    pagination={{
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total) => `共 ${total} 条记录`,
                    }}
                  />
                </Spin>
              ),
            },
          ]}
        />
      </Card>

      {/* 爬取配置表单模态框 */}
      <Modal
        title={editingCrawlerConfig ? "编辑爬取配置" : "新建爬取配置"}
        open={crawlerConfigModalVisible}
        onCancel={() => setCrawlerConfigModalVisible(false)}
        footer={null}
        width="90%"
        style={{ maxWidth: '1400px', minWidth: '800px' }}
        destroyOnHidden
      >
        <CrawlerConfigForm
          configId={editingCrawlerConfig || undefined}
          onSave={async (configData) => {
            try {
              if (editingCrawlerConfig) {
                // 更新配置
                await crawlerConfigApi.updateConfig(editingCrawlerConfig, configData as CrawlerConfigUpdate);
                message.success('配置更新成功');
              } else {
                // 创建新配置
                await crawlerConfigApi.createConfig(configData as CrawlerConfigCreate);
                message.success('配置创建成功');
              }
              setCrawlerConfigModalVisible(false);
              fetchConfigs();
            } catch (error) {
              console.error('保存配置失败:', error);
              message.error('保存配置失败，请检查输入');
              throw error; // 重新抛出错误，让表单知道保存失败
            }
          }}
          onCancel={() => setCrawlerConfigModalVisible(false)}
        />
      </Modal>

      {/* 后端配置表单模态框 */}
      <Modal
        title={editingBackendConfig ? "编辑后端配置" : "新建后端配置"}
        open={backendConfigModalVisible}
        onCancel={() => setBackendConfigModalVisible(false)}
        footer={null}
        width={600}
        destroyOnHidden
      >
        <BackendConfigForm
          configId={editingBackendConfig}
          onSuccess={() => {
            setBackendConfigModalVisible(false);
            fetchConfigs();
          }}
          onCancel={() => setBackendConfigModalVisible(false)}
        />
      </Modal>
    </div>
  );
};

export default NewConfiguration;
