"""
新架构：后端配置数据模型
包含API连接信息和后端性能相关的调度配置
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Tuple
from pydantic import BaseModel, Field, validator
from enum import Enum

# 导入认证配置
from .crawler_instance_config import AuthConfig, AuthType


class BackendStatus(str, Enum):
    """后端状态枚举"""
    ACTIVE = "active"           # 活跃
    INACTIVE = "inactive"       # 非活跃
    MAINTENANCE = "maintenance" # 维护中
    ERROR = "error"            # 错误状态


class PerformanceLevel(str, Enum):
    """性能级别枚举"""
    LOW = "low"                # 低性能
    MEDIUM = "medium"          # 中等性能
    HIGH = "high"              # 高性能
    ULTRA = "ultra"            # 超高性能


class BackendConfig(BaseModel):
    """后端配置（API连接 + 性能调度配置）"""
    
    # 基本信息
    backend_id: str = Field(description="后端唯一标识")
    backend_name: str = Field(description="后端名称", max_length=100)
    description: Optional[str] = Field(None, description="后端描述", max_length=500)
    
    # API连接配置
    api_endpoint: str = Field(description="API端点地址")
    timeout: int = Field(default=30000, ge=1000, le=300000, description="超时时间(ms)")
    max_retries: int = Field(default=3, ge=0, le=10, description="最大重试次数")
    auth_config: AuthConfig = Field(description="认证配置")
    
    # 性能和调度配置（与后端性能直接相关）
    max_concurrent: int = Field(default=2, ge=1, le=50, description="最大并发数")
    mean_delay: int = Field(default=1000, ge=100, le=10000, description="平均延迟(ms)")
    max_range: int = Field(default=2000, ge=100, le=20000, description="延迟最大范围(ms)")
    pool_size: int = Field(default=10, ge=1, le=100, description="线程池大小")
    memory_threshold: int = Field(default=512, ge=128, le=4096, description="内存阈值(MB)")
    
    # 负载均衡配置
    weight: int = Field(default=1, ge=1, le=100, description="负载均衡权重")
    priority: int = Field(default=1, ge=1, le=10, description="优先级")
    performance_level: PerformanceLevel = Field(default=PerformanceLevel.MEDIUM, description="性能级别")
    
    # 健康检查配置
    health_check_interval: int = Field(default=60, ge=10, le=3600, description="健康检查间隔(秒)")
    health_check_timeout: int = Field(default=5000, ge=1000, le=30000, description="健康检查超时(ms)")
    failure_threshold: int = Field(default=3, ge=1, le=10, description="故障阈值")
    recovery_threshold: int = Field(default=2, ge=1, le=10, description="恢复阈值")
    
    # 状态信息
    status: BackendStatus = Field(default=BackendStatus.ACTIVE, description="后端状态")
    health_score: float = Field(default=1.0, ge=0.0, le=1.0, description="健康评分")
    last_health_check: Optional[datetime] = Field(None, description="最后健康检查时间")
    
    # 统计信息
    total_requests: int = Field(default=0, ge=0, description="总请求数")
    success_requests: int = Field(default=0, ge=0, description="成功请求数")
    failed_requests: int = Field(default=0, ge=0, description="失败请求数")
    avg_response_time: float = Field(default=0.0, ge=0.0, description="平均响应时间(ms)")
    current_load: int = Field(default=0, ge=0, description="当前负载")
    
    # 时间戳
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    created_by: Optional[str] = Field(None, description="创建者")
    updated_by: Optional[str] = Field(None, description="更新者")
    
    @validator('api_endpoint')
    def validate_api_endpoint(cls, v):
        """验证API端点"""
        if not v or not v.strip():
            raise ValueError("API端点不能为空")
        
        v = v.strip()
        if not (v.startswith('http://') or v.startswith('https://')):
            raise ValueError("API端点必须以http://或https://开头")
        
        return v
    
    @validator('backend_name')
    def validate_backend_name(cls, v):
        """验证后端名称"""
        if not v or not v.strip():
            raise ValueError("后端名称不能为空")
        return v.strip()
    
    @validator('success_requests')
    def validate_success_requests(cls, v, values):
        """验证成功请求数不能超过总请求数"""
        if 'total_requests' in values and v > values['total_requests']:
            raise ValueError("成功请求数不能超过总请求数")
        return v
    
    @validator('max_range')
    def validate_max_range(cls, v, values):
        """验证延迟范围"""
        if 'mean_delay' in values and v < values['mean_delay']:
            raise ValueError("延迟最大范围不能小于平均延迟")
        return v
    
    @property
    def success_rate(self) -> float:
        """计算成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.success_requests / self.total_requests
    
    @property
    def is_healthy(self) -> bool:
        """判断后端是否健康"""
        return (
            self.status == BackendStatus.ACTIVE and 
            self.health_score >= 0.5 and
            self.current_load < self.max_concurrent
        )
    
    @property
    def is_available(self) -> bool:
        """判断后端是否可用"""
        return (
            self.is_healthy and 
            self.current_load < self.max_concurrent
        )
    
    @property
    def available_capacity(self) -> int:
        """获取可用容量"""
        return max(0, self.max_concurrent - self.current_load)
    
    @property
    def delay_range(self) -> Tuple[int, int]:
        """获取延迟范围"""
        min_delay = max(100, self.mean_delay - self.max_range // 2)
        max_delay = self.mean_delay + self.max_range // 2
        return (min_delay, max_delay)


class BackendConfigCreate(BaseModel):
    """创建后端配置的数据模型"""
    
    backend_name: str = Field(description="后端名称", max_length=100)
    description: Optional[str] = Field(None, description="后端描述", max_length=500)
    
    # API连接配置
    api_endpoint: str = Field(description="API端点地址")
    timeout: int = Field(default=30000, ge=1000, le=300000)
    max_retries: int = Field(default=3, ge=0, le=10)
    auth_config: AuthConfig = Field(description="认证配置")
    
    # 性能配置
    max_concurrent: int = Field(default=2, ge=1, le=50)
    mean_delay: int = Field(default=1000, ge=100, le=10000)
    max_range: int = Field(default=2000, ge=100, le=20000)
    pool_size: int = Field(default=10, ge=1, le=100)
    memory_threshold: int = Field(default=512, ge=128, le=4096)
    
    # 负载均衡配置
    weight: int = Field(default=1, ge=1, le=100)
    priority: int = Field(default=1, ge=1, le=10)
    performance_level: PerformanceLevel = Field(default=PerformanceLevel.MEDIUM)
    
    # 健康检查配置
    health_check_interval: int = Field(default=60, ge=10, le=3600)
    health_check_timeout: int = Field(default=5000, ge=1000, le=30000)
    failure_threshold: int = Field(default=3, ge=1, le=10)
    recovery_threshold: int = Field(default=2, ge=1, le=10)
    
    # 创建者
    created_by: Optional[str] = Field(None, description="创建者")


class BackendConfigUpdate(BaseModel):
    """更新后端配置的数据模型"""
    
    backend_name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    
    # API连接配置
    api_endpoint: Optional[str] = None
    timeout: Optional[int] = Field(None, ge=1000, le=300000)
    max_retries: Optional[int] = Field(None, ge=0, le=10)
    auth_config: Optional[AuthConfig] = None
    
    # 性能配置
    max_concurrent: Optional[int] = Field(None, ge=1, le=50)
    mean_delay: Optional[int] = Field(None, ge=100, le=10000)
    max_range: Optional[int] = Field(None, ge=100, le=20000)
    pool_size: Optional[int] = Field(None, ge=1, le=100)
    memory_threshold: Optional[int] = Field(None, ge=128, le=4096)
    
    # 负载均衡配置
    weight: Optional[int] = Field(None, ge=1, le=100)
    priority: Optional[int] = Field(None, ge=1, le=10)
    performance_level: Optional[PerformanceLevel] = None
    
    # 健康检查配置
    health_check_interval: Optional[int] = Field(None, ge=10, le=3600)
    health_check_timeout: Optional[int] = Field(None, ge=1000, le=30000)
    failure_threshold: Optional[int] = Field(None, ge=1, le=10)
    recovery_threshold: Optional[int] = Field(None, ge=1, le=10)
    
    # 状态更新
    status: Optional[BackendStatus] = None
    updated_by: Optional[str] = None


class BackendConfigSummary(BaseModel):
    """后端配置摘要（用于列表显示）"""
    
    backend_id: str
    backend_name: str
    description: Optional[str]
    api_endpoint: str
    status: BackendStatus
    performance_level: PerformanceLevel
    
    # 性能指标
    max_concurrent: int
    current_load: int
    available_capacity: int
    health_score: float
    success_rate: float
    avg_response_time: float
    
    # 时间信息
    created_at: Optional[datetime]
    last_health_check: Optional[datetime]
    
    @classmethod
    def from_config(cls, config: BackendConfig) -> 'BackendConfigSummary':
        """从完整配置创建摘要"""
        return cls(
            backend_id=config.backend_id,
            backend_name=config.backend_name,
            description=config.description,
            api_endpoint=config.api_endpoint,
            status=config.status,
            performance_level=config.performance_level,
            max_concurrent=config.max_concurrent,
            current_load=config.current_load,
            available_capacity=config.available_capacity,
            health_score=config.health_score,
            success_rate=config.success_rate,
            avg_response_time=config.avg_response_time,
            created_at=config.created_at,
            last_health_check=config.last_health_check
        )


class BackendHealthCheck(BaseModel):
    """后端健康检查结果"""
    
    backend_id: str = Field(description="后端ID")
    is_healthy: bool = Field(description="是否健康")
    response_time: float = Field(description="响应时间(ms)")
    error_message: Optional[str] = Field(None, description="错误信息")
    check_time: datetime = Field(description="检查时间")
    
    # 详细健康信息
    api_version: Optional[str] = Field(None, description="API版本")
    server_info: Optional[Dict[str, Any]] = Field(None, description="服务器信息")
    load_info: Optional[Dict[str, Any]] = Field(None, description="负载信息")
    
    @property
    def health_score(self) -> float:
        """计算健康评分"""
        if not self.is_healthy:
            return 0.0
        
        # 基于响应时间计算评分
        if self.response_time <= 1000:  # 1秒以内
            return 1.0
        elif self.response_time <= 3000:  # 3秒以内
            return 0.8
        elif self.response_time <= 5000:  # 5秒以内
            return 0.6
        else:
            return 0.4


class BackendConfigStats(BaseModel):
    """后端配置统计信息"""
    
    total_backends: int = Field(description="总后端数")
    active_backends: int = Field(description="活跃后端数")
    healthy_backends: int = Field(description="健康后端数")
    available_backends: int = Field(description="可用后端数")
    
    total_capacity: int = Field(description="总容量")
    used_capacity: int = Field(description="已用容量")
    available_capacity: int = Field(description="可用容量")
    
    avg_health_score: float = Field(description="平均健康评分")
    avg_response_time: float = Field(description="平均响应时间")
    total_success_rate: float = Field(description="总体成功率")
    
    # 性能级别分布
    performance_distribution: Dict[str, int] = Field(description="性能级别分布")
    
    # 最佳和最差后端
    best_backend: Optional[BackendConfigSummary] = Field(None, description="最佳后端")
    worst_backend: Optional[BackendConfigSummary] = Field(None, description="最差后端")
    
    # 最近状态
    recently_created: List[BackendConfigSummary] = Field(description="最近创建的后端")
    recently_failed: List[BackendConfigSummary] = Field(description="最近失败的后端")
