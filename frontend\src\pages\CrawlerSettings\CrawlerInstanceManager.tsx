/**
 * 爬虫实例配置管理页面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Progress,
  Tooltip,
  message,
  Modal,
  Descriptions,
  Row,
  Col,
  Statistic,
  Badge,
  Popconfirm,
  Typography,
  Divider
} from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
  SettingOutlined,
  DeleteOutlined,
  EyeOutlined,
  HeartOutlined,
  ThunderboltOutlined,
  ApiOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  StarOutlined,
  StarFilled
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

import { 
  crawlerInstanceApi, 
  CrawlerInstanceConfig, 
  ConnectionTestResult,
  ConfigStats
} from '../../services/crawlerInstanceApi';

const { Title, Text } = Typography;

const CrawlerInstanceManagerPage: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [configs, setConfigs] = useState<CrawlerInstanceConfig[]>([]);
  const [stats, setStats] = useState<ConfigStats | null>(null);
  const [selectedConfig, setSelectedConfig] = useState<CrawlerInstanceConfig | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [testResults, setTestResults] = useState<Record<string, ConnectionTestResult>>({});

  // 加载配置列表
  const loadConfigs = async () => {
    try {
      setLoading(true);
      const [configsData, statsData] = await Promise.all([
        crawlerInstanceApi.getAllConfigs(),
        crawlerInstanceApi.getConfigStats()
      ]);
      setConfigs(configsData);
      setStats(statsData);
    } catch (error) {
      message.error('加载爬虫配置列表失败');
      console.error('Load configs error:', error);
    } finally {
      setLoading(false);
    }
  };

  // 删除配置
  const handleDeleteConfig = async (configId: string) => {
    try {
      await crawlerInstanceApi.deleteConfig(configId);
      message.success('爬虫配置删除成功');
      loadConfigs();
    } catch (error: any) {
      message.error(error.response?.data?.detail || '删除爬虫配置失败');
    }
  };

  // 设置默认配置
  const handleSetDefault = async (configId: string) => {
    try {
      await crawlerInstanceApi.setDefaultConfig(configId);
      message.success('默认配置设置成功');
      loadConfigs();
    } catch (error: any) {
      message.error(error.response?.data?.detail || '设置默认配置失败');
    }
  };

  // 测试连接
  const handleTestConnection = async (configId: string) => {
    try {
      setLoading(true);
      const result = await crawlerInstanceApi.testConnection(configId);
      setTestResults(prev => ({
        ...prev,
        [configId]: result
      }));
      
      if (result.is_connected) {
        message.success(`连接测试成功 (${crawlerInstanceApi.formatResponseTime(result.response_time)})`);
      } else {
        message.error(`连接测试失败: ${result.error_message}`);
      }
    } catch (error) {
      message.error('连接测试失败');
    } finally {
      setLoading(false);
    }
  };

  // 批量测试连接
  const handleBatchTest = async () => {
    try {
      setLoading(true);
      const result = await crawlerInstanceApi.batchTestConnections();
      
      // 更新测试结果
      const newTestResults: Record<string, ConnectionTestResult> = {};
      result.results.forEach((testResult: ConnectionTestResult) => {
        newTestResults[testResult.config_id] = testResult;
      });
      setTestResults(newTestResults);
      
      message.success(
        `批量测试完成: ${result.successful_connections}/${result.total_configs} 连接成功`
      );
    } catch (error) {
      message.error('批量测试失败');
    } finally {
      setLoading(false);
    }
  };

  // 查看配置详情
  const handleViewDetails = (config: CrawlerInstanceConfig) => {
    setSelectedConfig(config);
    setDetailModalVisible(true);
  };

  useEffect(() => {
    loadConfigs();
  }, []);

  // 表格列定义
  const columns = [
    {
      title: '配置名称',
      dataIndex: 'config_name',
      key: 'config_name',
      render: (text: string, record: CrawlerInstanceConfig) => (
        <Space>
          <ApiOutlined />
          <strong>{text}</strong>
          {stats?.default_config_id === record.config_id && (
            <Tag color="gold" icon={<StarFilled />}>默认</Tag>
          )}
        </Space>
      ),
    },
    {
      title: 'API端点',
      dataIndex: 'api_endpoint',
      key: 'api_endpoint',
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <Text code>{text}</Text>
        </Tooltip>
      ),
    },
    {
      title: '认证类型',
      dataIndex: 'auth_config',
      key: 'auth_type',
      render: (authConfig: any) => (
        <Tag color="blue">
          {crawlerInstanceApi.getAuthTypeText(authConfig.auth_type)}
        </Tag>
      ),
    },
    {
      title: '性能配置',
      key: 'performance',
      render: (record: CrawlerInstanceConfig) => (
        <Space direction="vertical" size="small">
          <Text type="secondary">并发: {record.max_concurrent}</Text>
          <Text type="secondary">权重: {record.weight}</Text>
          <Text type="secondary">优先级: {record.priority}</Text>
        </Space>
      ),
    },
    {
      title: '健康状态',
      key: 'health',
      render: (record: CrawlerInstanceConfig) => {
        const testResult = testResults[record.config_id];
        
        return (
          <Space direction="vertical" size="small">
            <Progress 
              percent={Math.round(record.health_score * 100)} 
              size="small" 
              status={record.health_score >= 0.8 ? 'success' : record.health_score >= 0.5 ? 'active' : 'exception'}
            />
            <Space>
              <Tag color={crawlerInstanceApi.getStatusColor(record.status)}>
                {crawlerInstanceApi.getStatusText(record.status)}
              </Tag>
              {testResult && (
                <Tag color={testResult.is_connected ? 'green' : 'red'}>
                  {testResult.is_connected ? '连接正常' : '连接失败'}
                </Tag>
              )}
            </Space>
          </Space>
        );
      },
    },
    {
      title: '统计信息',
      key: 'stats',
      render: (record: CrawlerInstanceConfig) => (
        <Space direction="vertical" size="small">
          <Text type="secondary">
            成功率: {crawlerInstanceApi.calculateSuccessRate(record).toFixed(1)}%
          </Text>
          <Text type="secondary">
            响应时间: {crawlerInstanceApi.formatResponseTime(record.avg_response_time)}
          </Text>
          <Text type="secondary">
            总请求: {record.total_requests}
          </Text>
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: CrawlerInstanceConfig) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          
          <Tooltip title="测试连接">
            <Button 
              type="text" 
              icon={<HeartOutlined />} 
              onClick={() => handleTestConnection(record.config_id)}
            />
          </Tooltip>
          
          <Tooltip title="编辑配置">
            <Button 
              type="text" 
              icon={<SettingOutlined />} 
              onClick={() => navigate(`/crawler-settings/instance/${record.config_id}/edit`)}
            />
          </Tooltip>
          
          {stats?.default_config_id !== record.config_id && (
            <Tooltip title="设为默认">
              <Button 
                type="text" 
                icon={<StarOutlined />} 
                onClick={() => handleSetDefault(record.config_id)}
              />
            </Tooltip>
          )}
          
          {configs.length > 1 && (
            <Popconfirm
              title="确定要删除这个爬虫配置吗？"
              description="删除后无法恢复，请谨慎操作。"
              onConfirm={() => handleDeleteConfig(record.config_id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button 
                  type="text" 
                  danger 
                  icon={<DeleteOutlined />} 
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <Space>
            <ApiOutlined />
            爬虫实例配置管理
            <Tag color="blue">Instance Manager</Tag>
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<HeartOutlined />}
              onClick={handleBatchTest}
              loading={loading}
            >
              批量测试
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadConfigs}
              loading={loading}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/crawler-settings/instance/create')}
            >
              创建配置
            </Button>
          </Space>
        }
      >
        {/* 统计信息 */}
        {stats && (
          <>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Statistic
                  title="总配置数"
                  value={stats.total_configs}
                  prefix={<ApiOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="活跃配置"
                  value={stats.active_configs}
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="健康配置"
                  value={stats.healthy_configs}
                  prefix={<HeartOutlined />}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="平均健康度"
                  value={stats.health_percentage}
                  suffix="%"
                  prefix={<ThunderboltOutlined />}
                  valueStyle={{ 
                    color: stats.health_percentage >= 80 ? '#3f8600' : '#cf1322' 
                  }}
                />
              </Col>
            </Row>
            <Divider />
          </>
        )}

        {/* 配置列表 */}
        <Table
          columns={columns}
          dataSource={configs}
          rowKey="config_id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个爬虫配置`,
          }}
        />
      </Card>

      {/* 详情模态框 */}
      <Modal
        title={
          <Space>
            <ApiOutlined />
            爬虫配置详情
          </Space>
        }
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
          <Button 
            key="edit" 
            type="primary" 
            onClick={() => {
              setDetailModalVisible(false);
              navigate(`/crawler-settings/instance/${selectedConfig?.config_id}/edit`);
            }}
          >
            编辑配置
          </Button>,
        ]}
        width={800}
      >
        {selectedConfig && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="配置名称">{selectedConfig.config_name}</Descriptions.Item>
            <Descriptions.Item label="配置ID">{selectedConfig.config_id}</Descriptions.Item>
            <Descriptions.Item label="描述" span={2}>{selectedConfig.description || '无'}</Descriptions.Item>
            <Descriptions.Item label="API端点" span={2}>
              <Text code>{selectedConfig.api_endpoint}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="认证类型">
              {crawlerInstanceApi.getAuthTypeText(selectedConfig.auth_config.auth_type)}
            </Descriptions.Item>
            <Descriptions.Item label="超时时间">{selectedConfig.timeout}ms</Descriptions.Item>
            <Descriptions.Item label="最大重试">{selectedConfig.max_retries}</Descriptions.Item>
            <Descriptions.Item label="最大并发">{selectedConfig.max_concurrent}</Descriptions.Item>
            <Descriptions.Item label="权重">{selectedConfig.weight}</Descriptions.Item>
            <Descriptions.Item label="优先级">{selectedConfig.priority}</Descriptions.Item>
            <Descriptions.Item label="健康评分">
              <Progress 
                percent={Math.round(selectedConfig.health_score * 100)} 
                size="small" 
                status={selectedConfig.health_score >= 0.8 ? 'success' : 'exception'}
              />
            </Descriptions.Item>
            <Descriptions.Item label="成功率">
              {crawlerInstanceApi.calculateSuccessRate(selectedConfig).toFixed(1)}%
            </Descriptions.Item>
            <Descriptions.Item label="创建时间" span={2}>
              {selectedConfig.created_at ? new Date(selectedConfig.created_at).toLocaleString() : '未知'}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default CrawlerInstanceManagerPage;

// 导出其他组件供路由使用
export { default as CrawlerInstanceCreate } from './CrawlerInstanceCreate';
export { default as CrawlerInstanceEdit } from './CrawlerInstanceEdit';
