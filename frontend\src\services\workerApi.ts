/**
 * Worker管理API服务
 * 提供Worker的CRUD操作和任务分配功能
 */

import {
  <PERSON><PERSON>lerWorker,
  CrawlerWorkerCreate,
  CrawlerWorkerUpdate,
  CrawlerWorkerSummary,
  WorkerCompatibilityCheck,
  TaskAssignment,
  TaskAssignmentCreate,
  AssignmentRecommendation,
  ApiError
} from '../types/newArchitecture';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';
const WORKER_API_BASE = `${API_BASE_URL}/api/v1/crawler-workers`;
const ASSIGNMENT_API_BASE = `${API_BASE_URL}/api/v1/task-assignments`;

class WorkerApiService {
  // ==================== Worker管理 ====================

  /**
   * 获取Worker列表
   */
  async getWorkers(params?: {
    status?: string;
    priority?: string;
    tags?: string[];
    search?: string;
    page?: number;
    size?: number;
  }): Promise<CrawlerWorkerSummary[]> {
    const searchParams = new URLSearchParams();
    
    if (params?.status) searchParams.append('status', params.status);
    if (params?.priority) searchParams.append('priority', params.priority);
    if (params?.search) searchParams.append('search', params.search);
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.size) searchParams.append('size', params.size.toString());
    if (params?.tags) {
      params.tags.forEach(tag => searchParams.append('tags', tag));
    }

    const response = await fetch(`${WORKER_API_BASE}/?${searchParams}`);
    if (!response.ok) {
      throw new Error(`获取Worker列表失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 获取单个Worker详情
   */
  async getWorker(workerId: string): Promise<CrawlerWorker> {
    const response = await fetch(`${WORKER_API_BASE}/${workerId}`);
    if (!response.ok) {
      if (response.status === 404) {
        throw new Error('Worker不存在');
      }
      throw new Error(`获取Worker详情失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 创建新的Worker
   */
  async createWorker(workerData: CrawlerWorkerCreate): Promise<CrawlerWorker> {
    const response = await fetch(WORKER_API_BASE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(workerData),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '创建Worker失败');
    }

    return response.json();
  }

  /**
   * 更新Worker
   */
  async updateWorker(workerId: string, updateData: CrawlerWorkerUpdate): Promise<CrawlerWorker> {
    const response = await fetch(`${WORKER_API_BASE}/${workerId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '更新Worker失败');
    }

    return response.json();
  }

  /**
   * 删除Worker
   */
  async deleteWorker(workerId: string): Promise<void> {
    const response = await fetch(`${WORKER_API_BASE}/${workerId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '删除Worker失败');
    }
  }

  /**
   * 检查Worker兼容性
   */
  async checkCompatibility(workerId: string): Promise<WorkerCompatibilityCheck> {
    const response = await fetch(`${WORKER_API_BASE}/${workerId}/compatibility`, {
      method: 'POST',
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '检查兼容性失败');
    }

    return response.json();
  }

  /**
   * 获取Worker统计信息
   */
  async getWorkerStats(workerId: string): Promise<{
    total_tasks: number;
    completed_tasks: number;
    failed_tasks: number;
    success_rate: number;
    avg_task_duration: number;
    current_load: number;
    health_score: number;
    uptime: number;
    last_heartbeat: string;
  }> {
    const response = await fetch(`${WORKER_API_BASE}/${workerId}/stats`);
    if (!response.ok) {
      throw new Error(`获取Worker统计失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 启动/停止Worker
   */
  async toggleWorker(workerId: string, action: 'start' | 'stop' | 'restart'): Promise<CrawlerWorker> {
    const response = await fetch(`${WORKER_API_BASE}/${workerId}/${action}`, {
      method: 'POST',
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || `${action} Worker失败`);
    }

    return response.json();
  }

  /**
   * 获取可用的Worker（用于任务分配）
   */
  async getAvailableWorkers(requirements?: {
    crawler_config_id?: string;
    backend_config_id?: string;
    min_concurrent?: number;
    tags?: string[];
  }): Promise<CrawlerWorkerSummary[]> {
    const searchParams = new URLSearchParams();
    
    if (requirements?.crawler_config_id) {
      searchParams.append('crawler_config_id', requirements.crawler_config_id);
    }
    if (requirements?.backend_config_id) {
      searchParams.append('backend_config_id', requirements.backend_config_id);
    }
    if (requirements?.min_concurrent) {
      searchParams.append('min_concurrent', requirements.min_concurrent.toString());
    }
    if (requirements?.tags) {
      requirements.tags.forEach(tag => searchParams.append('tags', tag));
    }

    const response = await fetch(`${WORKER_API_BASE}/available?${searchParams}`);
    if (!response.ok) {
      throw new Error(`获取可用Worker失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  // ==================== 任务分配管理 ====================

  /**
   * 获取任务分配列表
   */
  async getAssignments(params?: {
    status?: string;
    worker_id?: string;
    task_id?: string;
    page?: number;
    size?: number;
  }): Promise<TaskAssignment[]> {
    const searchParams = new URLSearchParams();
    
    if (params?.status) searchParams.append('status', params.status);
    if (params?.worker_id) searchParams.append('worker_id', params.worker_id);
    if (params?.task_id) searchParams.append('task_id', params.task_id);
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.size) searchParams.append('size', params.size.toString());

    const response = await fetch(`${ASSIGNMENT_API_BASE}/?${searchParams}`);
    if (!response.ok) {
      throw new Error(`获取任务分配列表失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 创建任务分配
   */
  async createAssignment(assignmentData: TaskAssignmentCreate): Promise<TaskAssignment> {
    const response = await fetch(ASSIGNMENT_API_BASE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(assignmentData),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '创建任务分配失败');
    }

    return response.json();
  }

  /**
   * 获取任务分配推荐
   */
  async getAssignmentRecommendations(taskId: string, requirements?: {
    priority?: number;
    estimated_duration?: number;
    required_tags?: string[];
  }): Promise<AssignmentRecommendation[]> {
    const searchParams = new URLSearchParams();
    
    if (requirements?.priority) {
      searchParams.append('priority', requirements.priority.toString());
    }
    if (requirements?.estimated_duration) {
      searchParams.append('estimated_duration', requirements.estimated_duration.toString());
    }
    if (requirements?.required_tags) {
      requirements.required_tags.forEach(tag => searchParams.append('required_tags', tag));
    }

    const response = await fetch(`${ASSIGNMENT_API_BASE}/recommendations/${taskId}?${searchParams}`);
    if (!response.ok) {
      throw new Error(`获取分配推荐失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 取消任务分配
   */
  async cancelAssignment(assignmentId: string, reason?: string): Promise<TaskAssignment> {
    const response = await fetch(`${ASSIGNMENT_API_BASE}/${assignmentId}/cancel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ reason }),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '取消任务分配失败');
    }

    return response.json();
  }

  /**
   * 重新分配任务
   */
  async reassignTask(assignmentId: string, newWorkerId: string): Promise<TaskAssignment> {
    const response = await fetch(`${ASSIGNMENT_API_BASE}/${assignmentId}/reassign`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ new_worker_id: newWorkerId }),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '重新分配任务失败');
    }

    return response.json();
  }

  /**
   * 获取Worker的任务分配历史
   */
  async getWorkerAssignmentHistory(workerId: string, limit?: number): Promise<TaskAssignment[]> {
    const searchParams = new URLSearchParams();
    if (limit) searchParams.append('limit', limit.toString());

    const response = await fetch(`${WORKER_API_BASE}/${workerId}/assignments?${searchParams}`);
    if (!response.ok) {
      throw new Error(`获取Worker分配历史失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 批量操作Worker
   */
  async batchWorkerOperation(operation: 'start' | 'stop' | 'delete', workerIds: string[]): Promise<{
    success: string[];
    failed: { id: string; error: string }[];
  }> {
    const response = await fetch(`${WORKER_API_BASE}/batch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        operation,
        worker_ids: workerIds,
      }),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '批量操作失败');
    }

    return response.json();
  }
}

// 导出单例实例
export const workerApi = new WorkerApiService();
export default workerApi;
