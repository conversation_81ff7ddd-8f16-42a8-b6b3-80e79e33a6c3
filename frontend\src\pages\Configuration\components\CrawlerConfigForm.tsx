import React, { useEffect, useState, useCallback } from 'react';
import {
  Form,
  Input,
  InputNumber,
  Switch,
  Select,
  Button,
  Space,
  Tabs,
  Card,
  Row,
  Col,
  message,
  Spin,
  Alert,
  Divider,
  Tooltip
} from 'antd';
import {
  SaveOutlined,
  CloseOutlined,
  <PERSON>boltOutlined,
  DesktopOutlined,
  ApiOutlined,
  ExperimentOutlined,
  SettingOutlined,
  LinkOutlined,
  MonitorOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

import {
  CrawlerConfigData,
  CrawlerConfigCreate,
  CrawlerConfigUpdate
} from '../../../services';
import { getNewConfigFormDefaults } from '../../../config/defaultCrawlerConfig';
import { crawlerConfigService } from '../../../services/crawlerConfigService';

const { TextArea } = Input;
const { Option } = Select;

interface CrawlerConfigFormProps {
  configId?: string;
  initialData?: CrawlerConfigData;
  onSave: (data: CrawlerConfigCreate | CrawlerConfigUpdate) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

const CrawlerConfigForm: React.FC<CrawlerConfigFormProps> = ({
  configId,
  initialData,
  onSave,
  onCancel,
  loading = false
}) => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);

  // 设置默认值
  const setDefaultValues = useCallback(() => {
    const defaultValues = getNewConfigFormDefaults();
    form.setFieldsValue(defaultValues);
  }, [form]);

  // 加载初始数据
  useEffect(() => {
    if (initialData) {
      // 将数据转换为表单格式
      form.setFieldsValue({
        name: initialData.config_name,
        description: initialData.description,
        tags: initialData.tags?.join(', '),

        // Browser配置
        'browser.headless': initialData.browser?.headless,
        'browser.verbose': initialData.browser?.verbose,
        'browser.viewport_width': initialData.browser?.viewport_width,
        'browser.viewport_height': initialData.browser?.viewport_height,
        'browser.wait_for': initialData.browser?.wait_for,
        'browser.timeout': initialData.browser?.timeout,
        'browser.ignore_https_errors': initialData.browser?.ignore_https_errors,
        'browser.extra_args': Array.isArray(initialData.browser?.extra_args)
          ? initialData.browser.extra_args.join('\n')
          : initialData.browser?.extra_args,

        // Crawler配置
        'crawler.method': initialData.crawler?.method,
        'crawler.verbose': initialData.crawler?.verbose,
        'crawler.simulate_user': initialData.crawler?.simulate_user,
        'crawler.magic': initialData.crawler?.magic,
        'crawler.override_navigator': initialData.crawler?.override_navigator,
        'crawler.remove_overlay_elements': initialData.crawler?.remove_overlay_elements,
        'crawler.scan_full_page': initialData.crawler?.scan_full_page,
        'crawler.process_iframes': initialData.crawler?.process_iframes,
        'crawler.ignore_body_visibility': initialData.crawler?.ignore_body_visibility,
        'crawler.page_timeout': initialData.crawler?.page_timeout,
        'crawler.delay_before_return_html': initialData.crawler?.delay_before_return_html,
        'crawler.scroll_delay': initialData.crawler?.scroll_delay,
        'crawler.wait_until': initialData.crawler?.wait_until,
        'crawler.image_score_threshold': initialData.crawler?.image_score_threshold,
        'crawler.table_score_threshold': initialData.crawler?.table_score_threshold,
        'crawler.cache_mode': initialData.crawler?.cache_mode,
        'crawler.check_robots_txt': initialData.crawler?.check_robots_txt,
        'crawler.fetch_ssl_certificate': initialData.crawler?.fetch_ssl_certificate,
        'crawler.bypass_cache': initialData.crawler?.bypass_cache,
        'crawler.extraction_strategy': initialData.crawler?.extraction_strategy,
        'crawler.chunking_strategy': initialData.crawler?.chunking_strategy,
        'crawler.markdown_generator': initialData.crawler?.markdown_generator,

        // LLM配置
        'llm.query': initialData.llm?.query,
        'llm.provider': initialData.llm?.provider,
        'llm.model': initialData.llm?.model,
        'llm.api_key': initialData.llm?.api_key,
        'llm.base_url': initialData.llm?.base_url,
        'llm.temperature': initialData.llm?.temperature,
        'llm.max_tokens': initialData.llm?.max_tokens,
        'llm.top_p': initialData.llm?.top_p,

        // Schema Extraction配置
        'schema_extraction.enabled': initialData.schema_extraction?.enabled,
        'schema_extraction.schema_type': initialData.schema_extraction?.schema_type,
        'schema_extraction.validate_schema': initialData.schema_extraction?.validate_schema,
        'schema_extraction.return_raw': initialData.schema_extraction?.return_raw,
        'schema_extraction.extraction_schema': typeof initialData.schema_extraction?.extraction_schema === 'object'
          ? JSON.stringify(initialData.schema_extraction.extraction_schema, null, 2)
          : initialData.schema_extraction?.extraction_schema,
        'schema_extraction.instructions': initialData.schema_extraction?.instructions,

        // Content Processing配置
        'content_processing.word_count_threshold': initialData.content_processing?.word_count_threshold,
        'content_processing.excluded_tags': Array.isArray(initialData.content_processing?.excluded_tags)
          ? initialData.content_processing.excluded_tags.join('\n')
          : initialData.content_processing?.excluded_tags,
        'content_processing.parser_type': initialData.content_processing?.parser_type,

        // Link Filtering配置
        'link_filtering.exclude_external_links': initialData.link_filtering?.exclude_external_links,
        'link_filtering.exclude_domains': Array.isArray(initialData.link_filtering?.exclude_domains)
          ? initialData.link_filtering.exclude_domains.join('\n')
          : initialData.link_filtering?.exclude_domains,

        // Monitor配置
        'monitor.display_mode': initialData.monitor?.display_mode,
        'monitor.show_progress': initialData.monitor?.show_progress,
        'monitor.log_errors': initialData.monitor?.log_errors,
      });
    } else {
      // 新建时设置默认值
      setDefaultValues();
    }
  }, [initialData, form, setDefaultValues]);

  // 手动填入默认值的函数
  const fillDefaultValues = async () => {
    try {
      setSubmitting(true);
      // 从服务器获取默认配置
      const defaultConfig = await crawlerConfigService.getDefaultConfigFromServer();

      // 转换为表单格式
      const defaultFormValues = {
        // 基本信息
        name: '',
        tags: '',
        description: '',

        // Browser配置
        'browser.headless': defaultConfig.browser.headless,
        'browser.verbose': defaultConfig.browser.verbose,
        'browser.viewport_width': defaultConfig.browser.viewport_width,
        'browser.viewport_height': defaultConfig.browser.viewport_height,
        'browser.wait_for': defaultConfig.browser.wait_for,
        'browser.timeout': defaultConfig.browser.timeout,
        'browser.ignore_https_errors': defaultConfig.browser.ignore_https_errors,
        'browser.extra_args': defaultConfig.browser.extra_args.join('\n'),

        // Crawler配置
        'crawler.method': defaultConfig.crawler.method,
        'crawler.verbose': defaultConfig.crawler.verbose,
        'crawler.simulate_user': defaultConfig.crawler.simulate_user,
        'crawler.magic': defaultConfig.crawler.magic,
        'crawler.override_navigator': defaultConfig.crawler.override_navigator,
        'crawler.remove_overlay_elements': defaultConfig.crawler.remove_overlay_elements,
        'crawler.scan_full_page': defaultConfig.crawler.scan_full_page,
        'crawler.process_iframes': defaultConfig.crawler.process_iframes,
        'crawler.ignore_body_visibility': defaultConfig.crawler.ignore_body_visibility,
        'crawler.adjust_viewport_to_content': defaultConfig.crawler.adjust_viewport_to_content,
        'crawler.page_timeout': defaultConfig.crawler.page_timeout,
        'crawler.delay_before_return_html': defaultConfig.crawler.delay_before_return_html,
        'crawler.scroll_delay': defaultConfig.crawler.scroll_delay,
        'crawler.wait_until': defaultConfig.crawler.wait_until,
        'crawler.image_score_threshold': defaultConfig.crawler.image_score_threshold,
        'crawler.table_score_threshold': defaultConfig.crawler.table_score_threshold,
        'crawler.cache_mode': defaultConfig.crawler.cache_mode,
        'crawler.check_robots_txt': defaultConfig.crawler.check_robots_txt,
        'crawler.fetch_ssl_certificate': defaultConfig.crawler.fetch_ssl_certificate,
        'crawler.bypass_cache': defaultConfig.crawler.bypass_cache,
        'crawler.extraction_strategy': defaultConfig.crawler.extraction_strategy,
        'crawler.chunking_strategy': defaultConfig.crawler.chunking_strategy,
        'crawler.markdown_generator': defaultConfig.crawler.markdown_generator,

        // LLM配置
        'llm.query': defaultConfig.llm.query,
        'llm.provider': defaultConfig.llm.provider,
        'llm.model': defaultConfig.llm.model,
        'llm.api_key': defaultConfig.llm.api_key,
        'llm.base_url': defaultConfig.llm.base_url,
        'llm.temperature': defaultConfig.llm.temperature,
        'llm.max_tokens': defaultConfig.llm.max_tokens,
        'llm.top_p': defaultConfig.llm.top_p,

        // Schema Extraction配置
        'schema_extraction.validate_schema': defaultConfig.schema_extraction.validate_schema,
        'schema_extraction.return_raw': defaultConfig.schema_extraction.return_raw,
        'schema_extraction.instructions': defaultConfig.schema_extraction.instructions,
        'schema_extraction.extraction_schema': JSON.stringify(defaultConfig.schema_extraction.extraction_schema, null, 2),

        // Content Processing配置
        'content_processing.word_count_threshold': defaultConfig.content_processing.word_count_threshold,
        'content_processing.excluded_tags': defaultConfig.content_processing.excluded_tags,
        'content_processing.excluded_selector': defaultConfig.content_processing.excluded_selector,
        'content_processing.parser_type': defaultConfig.content_processing.parser_type,
        'content_processing.css_selector': defaultConfig.content_processing.css_selector,
        'content_processing.remove_forms': defaultConfig.content_processing.remove_forms,
        'content_processing.only_text': defaultConfig.content_processing.only_text,
        'content_processing.prettify': defaultConfig.content_processing.prettify,
        'content_processing.keep_data_attributes': defaultConfig.content_processing.keep_data_attributes,

        // Link Filtering配置
        'link_filtering.exclude_external_links': defaultConfig.link_filtering.exclude_external_links,
        'link_filtering.exclude_internal_links': defaultConfig.link_filtering.exclude_internal_links,
        'link_filtering.exclude_social_media_links': defaultConfig.link_filtering.exclude_social_media_links,
        'link_filtering.exclude_domains': defaultConfig.link_filtering.exclude_domains,
        'link_filtering.social_media_domains': defaultConfig.link_filtering.social_media_domains,
        'link_filtering.exclude_external_images': defaultConfig.link_filtering.exclude_external_images,
        'link_filtering.exclude_all_images': defaultConfig.link_filtering.exclude_all_images,
        'link_filtering.image_score_threshold': defaultConfig.link_filtering.image_score_threshold,
        'link_filtering.image_description_min_word_threshold': defaultConfig.link_filtering.image_description_min_word_threshold,
        'link_filtering.table_score_threshold': defaultConfig.link_filtering.table_score_threshold,

        // Monitor配置
        'monitor.display_mode': defaultConfig.monitor.display_mode,
        'monitor.show_progress': defaultConfig.monitor.show_progress,
        'monitor.log_errors': defaultConfig.monitor.log_errors,
      };

      form.setFieldsValue(defaultFormValues);
      message.success('已从服务器获取并填入默认配置值');
    } catch (error) {
      message.error('获取默认配置失败，使用本地默认值');
      console.error('Get default config error:', error);

      // 如果API失败，使用本地默认配置作为备用
      const localDefaultValues = getNewConfigFormDefaults();
      form.setFieldsValue(localDefaultValues);
      message.info('已填入本地默认配置值');
    } finally {
      setSubmitting(false);
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      const values = await form.validateFields();
      
      // 转换表单数据为API格式
      const configData: CrawlerConfigCreate = {
        config_name: values.name as string,
        description: values.description || '',
        version: '1.0',
        tags: values.tags ? values.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean) : [],
        created_by: 'user',
        
        browser: {
          headless: values['browser.headless'] || false,
          verbose: values['browser.verbose'] || false,
          viewport_width: values['browser.viewport_width'] || 1920,
          viewport_height: values['browser.viewport_height'] || 1080,
          wait_for: values['browser.wait_for'] || 2.0,
          timeout: values['browser.timeout'] || 30,
          ignore_https_errors: values['browser.ignore_https_errors'] || false,
          extra_args: values['browser.extra_args'] ? values['browser.extra_args'].split('\n').filter(Boolean) : [],
        },
        
        crawler: {
          method: values['crawler.method'] || 'arun_many',
          verbose: values['crawler.verbose'] !== undefined ? values['crawler.verbose'] : true,
          check_robots_txt: values['crawler.check_robots_txt'] || false,
          fetch_ssl_certificate: values['crawler.fetch_ssl_certificate'] || false,
          simulate_user: values['crawler.simulate_user'] !== undefined ? values['crawler.simulate_user'] : true,
          magic: values['crawler.magic'] !== undefined ? values['crawler.magic'] : true,
          override_navigator: values['crawler.override_navigator'] || true,
          remove_overlay_elements: values['crawler.remove_overlay_elements'] || true,
          ignore_body_visibility: values['crawler.ignore_body_visibility'] || true,
          adjust_viewport_to_content: true,
          wait_until: values['crawler.wait_until'] || 'domcontentloaded',
          wait_for_images: false,
          page_timeout: values['crawler.page_timeout'] || 60000,
          delay_before_return_html: values['crawler.delay_before_return_html'] || 0.1,
          js_only: false,
          scan_full_page: values['crawler.scan_full_page'] || true,
          process_iframes: values['crawler.process_iframes'] || true,
          scroll_delay: values['crawler.scroll_delay'] || 0.2,
          cache_mode: values['crawler.cache_mode'] || 'BYPASS',
          screenshot: false,
          pdf: false,
          capture_mhtml: false,
          exclude_external_images: false,
          exclude_all_images: false,
          image_score_threshold: values['crawler.image_score_threshold'] || 50,
          image_description_min_word_threshold: 50,
          table_score_threshold: values['crawler.table_score_threshold'] || 7,
          capture_network_requests: false,
          capture_console_messages: false,
          log_console: false,
          extraction_strategy: values['crawler.extraction_strategy'],
          chunking_strategy: values['crawler.chunking_strategy'],
          markdown_generator: values['crawler.markdown_generator'],
          bypass_cache: values['crawler.bypass_cache'] || true,
        },
        
        llm: {
          query: values['llm.query'] || '',
          provider: values['llm.provider'] || 'openai',
          model: values['llm.model'] || 'gpt-4',
          api_key: values['llm.api_key'] || '',
          base_url: values['llm.base_url'] || '',
          temperature: values['llm.temperature'] || 0.7,
          max_tokens: values['llm.max_tokens'] || 4096,
          top_p: values['llm.top_p'] || 1.0,
        },

        schema_extraction: {
          enabled: values['schema_extraction.enabled'] || false,
          schema_type: values['schema_extraction.schema_type'] || 'auto',
          validate_schema: values['schema_extraction.validate_schema'] || false,
          return_raw: values['schema_extraction.return_raw'] || false,
          extraction_schema: values['schema_extraction.extraction_schema']
            ? (typeof values['schema_extraction.extraction_schema'] === 'string'
                ? JSON.parse(values['schema_extraction.extraction_schema'])
                : values['schema_extraction.extraction_schema'])
            : {},
          instructions: values['schema_extraction.instructions'] || '',
        },
        
        content_processing: {
          word_count_threshold: values['content_processing.word_count_threshold'] || 200,
          css_selector: values['content_processing.css_selector'] || '',
          target_elements: [], // 默认空数组
          excluded_tags: Array.isArray(values['content_processing.excluded_tags'])
            ? values['content_processing.excluded_tags']
            : values['content_processing.excluded_tags']
              ? values['content_processing.excluded_tags'].split('\n').filter(Boolean)
              : [],
          excluded_selector: values['content_processing.excluded_selector'] || '',
          remove_forms: values['content_processing.remove_forms'] || false,
          only_text: values['content_processing.only_text'] || false,
          prettify: values['content_processing.prettify'] || false,
          parser_type: values['content_processing.parser_type'] || 'lxml',
          keep_data_attributes: values['content_processing.keep_data_attributes'] || false,
          keep_attrs: [], // 默认空数组
        },

        link_filtering: {
          exclude_external_links: values['link_filtering.exclude_external_links'] || false,
          exclude_internal_links: values['link_filtering.exclude_internal_links'] || false,
          exclude_social_media_links: values['link_filtering.exclude_social_media_links'] || false,
          exclude_domains: Array.isArray(values['link_filtering.exclude_domains'])
            ? values['link_filtering.exclude_domains']
            : values['link_filtering.exclude_domains']
              ? values['link_filtering.exclude_domains'].split('\n').filter(Boolean)
              : [],
          social_media_domains: Array.isArray(values['link_filtering.social_media_domains'])
            ? values['link_filtering.social_media_domains']
            : values['link_filtering.social_media_domains']
              ? values['link_filtering.social_media_domains'].split('\n').filter(Boolean)
              : [],
          exclude_external_images: values['link_filtering.exclude_external_images'] || false,
          exclude_all_images: values['link_filtering.exclude_all_images'] || false,
          image_score_threshold: values['link_filtering.image_score_threshold'] || 3,
          image_description_min_word_threshold: values['link_filtering.image_description_min_word_threshold'] || 50,
          table_score_threshold: values['link_filtering.table_score_threshold'] || 7,
        },

        monitor: {
          display_mode: values['monitor.display_mode'] || 'detailed',
          show_progress: values['monitor.show_progress'] || true,
          log_errors: values['monitor.log_errors'] || true,
        },
      };

      console.log('发送的配置数据:', JSON.stringify(configData, null, 2));
      await onSave(configData);
    } catch (error) {
      console.error('保存配置失败:', error);
      // 错误处理由父组件负责
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
    >
      {/* 基本信息 */}
      <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label="配置名称"
              rules={[{ required: true, message: '请输入配置名称' }]}
            >
              <Input placeholder="请输入配置名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="tags"
              label="标签"
              tooltip="多个标签用逗号分隔"
            >
              <Input placeholder="标签1, 标签2" />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item
          name="description"
          label="描述"
        >
          <TextArea rows={2} placeholder="请输入配置描述" />
        </Form.Item>
        
        {/* 默认值按钮 */}
        {!configId && (
          <div style={{ marginTop: 16, textAlign: 'right' }}>
            <Button
              type="dashed"
              icon={<ThunderboltOutlined />}
              onClick={fillDefaultValues}
            >
              使用默认值
            </Button>
          </div>
        )}
      </Card>

      {/* 配置标签页 - 与传统配置页面完全一致 */}
      <Tabs
        defaultActiveKey="browser"
        items={[
          {
            key: 'browser',
            label: <span><DesktopOutlined />浏览器配置</span>,
            children: (
              <div>
                <Alert
                  message="浏览器配置"
                  description="配置浏览器的基本参数和行为。这些设置影响页面的加载和渲染方式。"
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      label="无头模式"
                      name="browser.headless"
                      valuePropName="checked"
                      tooltip="是否以无头模式运行浏览器（不显示界面）"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="详细日志"
                      name="browser.verbose"
                      valuePropName="checked"
                      tooltip="是否输出详细的浏览器日志信息"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="忽略HTTPS错误"
                      name="browser.ignore_https_errors"
                      valuePropName="checked"
                      tooltip="是否忽略SSL证书错误"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      label="视口宽度"
                      name="browser.viewport_width"
                      rules={[{ required: true, message: '请输入视口宽度' }]}
                    >
                      <InputNumber min={800} max={3840} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="视口高度"
                      name="browser.viewport_height"
                      rules={[{ required: true, message: '请输入视口高度' }]}
                    >
                      <InputNumber min={600} max={2160} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="等待时间(秒)"
                      name="browser.wait_for"
                      tooltip="页面加载后的等待时间"
                    >
                      <InputNumber min={0} max={30} step={0.1} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="超时时间(秒)"
                      name="browser.timeout"
                      rules={[{ required: true, message: '请输入超时时间' }]}
                      tooltip="页面加载的最大等待时间"
                    >
                      <InputNumber min={10} max={300} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item
                  label={
                    <Space>
                      额外启动参数
                      <Tooltip title="浏览器启动时的额外命令行参数，每行一个">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="browser.extra_args"
                >
                  <TextArea
                    rows={4}
                    placeholder="--no-sandbox&#10;--disable-dev-shm-usage&#10;--disable-blink-features=AutomationControlled"
                  />
                </Form.Item>
              </div>
            ),
          },
          {
            key: 'crawler',
            label: <span><SettingOutlined />爬虫配置</span>,
            children: (
              <div>
                <Alert
                  message="爬虫配置"
                  description="配置爬虫的核心行为参数。这些设置决定了爬虫如何处理页面内容和执行策略。"
                  type="warning"
                  showIcon
                  style={{ marginBottom: 16 }}
                />

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      label="爬取方法"
                      name="crawler.method"
                      rules={[{ required: true, message: '请选择爬取方法' }]}
                    >
                      <Select>
                        <Option value="arun_many">异步批量处理</Option>
                        <Option value="arun">异步单个处理</Option>
                        <Option value="run">同步处理</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="模拟用户行为"
                      name="crawler.simulate_user"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="智能处理模式"
                      name="crawler.magic"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="覆盖导航器"
                      name="crawler.override_navigator"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      label="移除覆盖元素"
                      name="crawler.remove_overlay_elements"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="扫描整页"
                      name="crawler.scan_full_page"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="处理iframe"
                      name="crawler.process_iframes"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      label="详细日志"
                      name="crawler.verbose"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="忽略body可见性"
                      name="crawler.ignore_body_visibility"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="调整视口到内容"
                      name="crawler.adjust_viewport_to_content"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Divider>时间和性能设置</Divider>

                <Row gutter={16}>
                  <Col span={6}>
                    <Form.Item
                      label="页面超时(ms)"
                      name="crawler.page_timeout"
                    >
                      <InputNumber min={10000} max={300000} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="返回前延迟(s)"
                      name="crawler.delay_before_return_html"
                    >
                      <InputNumber min={0} max={10} step={0.1} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="滚动延迟(s)"
                      name="crawler.scroll_delay"
                    >
                      <InputNumber min={0} max={5} step={0.1} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="等待条件"
                      name="crawler.wait_until"
                    >
                      <Select>
                        <Option value="domcontentloaded">DOM内容加载</Option>
                        <Option value="load">完全加载</Option>
                        <Option value="networkidle0">网络空闲0</Option>
                        <Option value="networkidle2">网络空闲2</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>

                <Divider>底层和缓存设置</Divider>

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      label="图片质量阈值"
                      name="crawler.image_score_threshold"
                    >
                      <InputNumber min={0} max={100} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="表格质量阈值"
                      name="crawler.table_score_threshold"
                    >
                      <InputNumber min={0} max={20} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="缓存模式"
                      name="crawler.cache_mode"
                    >
                      <Select>
                        <Option value="BYPASS">绕过缓存</Option>
                        <Option value="ENABLED">启用缓存</Option>
                        <Option value="DISABLED">禁用缓存</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>

                <Divider>监控设置</Divider>

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      label="显示模式"
                      name="monitor.display_mode"
                    >
                      <Select>
                        <Option value="simple">简单</Option>
                        <Option value="detailed">详细</Option>
                        <Option value="debug">调试</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="显示进度"
                      name="monitor.show_progress"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="记录错误"
                      name="monitor.log_errors"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Divider>高级设置</Divider>

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      label="提取策略"
                      name="crawler.extraction_strategy"
                      rules={[{ required: true, message: '请选择提取策略' }]}
                      tooltip="内容提取使用的策略"
                    >
                      <Select>
                        <Option value="LLMExtractionStrategy">LLM提取策略</Option>
                        <Option value="CosineStrategy">余弦相似度策略</Option>
                        <Option value="NoExtractionStrategy">无提取策略</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="分块策略"
                      name="crawler.chunking_strategy"
                      rules={[{ required: true, message: '请选择分块策略' }]}
                      tooltip="内容分块处理策略"
                    >
                      <Select>
                        <Option value="IdentityChunking">身份分块</Option>
                        <Option value="RegexChunking">正则表达式分块</Option>
                        <Option value="NlpSentenceChunking">NLP句子分块</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="Markdown生成器"
                      name="crawler.markdown_generator"
                      rules={[{ required: true, message: '请选择Markdown生成器' }]}
                      tooltip="Markdown内容生成器类型"
                    >
                      <Select>
                        <Option value="DefaultMarkdownGenerator">默认生成器</Option>
                        <Option value="CustomMarkdownGenerator">自定义生成器</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              </div>
            ),
          },
          {
            key: 'llm',
            label: <span><ApiOutlined />LLM配置</span>,
            children: (
              <div>
                <Alert
                  message="LLM配置"
                  description="配置用于内容提取的大语言模型参数。API密钥将被安全存储。"
                  type="warning"
                  showIcon
                  style={{ marginBottom: 16 }}
                />

                <Form.Item
                  label={
                    <Space>
                      提取查询指令
                      <Tooltip title="告诉LLM要提取什么内容的指令">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="llm.query"
                  rules={[{ required: true, message: '请输入提取查询指令' }]}
                >
                  <TextArea
                    rows={3}
                    placeholder="提取页面中所有文章的标题、作者和发布时间"
                  />
                </Form.Item>

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      label="提供商"
                      name="llm.provider"
                      rules={[{ required: true, message: '请选择LLM提供商' }]}
                    >
                      <Select>
                        <Option value="openai">OpenAI</Option>
                        <Option value="anthropic">Anthropic</Option>
                        <Option value="google">Google</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="模型"
                      name="llm.model"
                      rules={[{ required: true, message: '请输入模型名称' }]}
                    >
                      <Input placeholder="deepseek-v3-0324" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="API基础URL"
                      name="llm.base_url"
                      rules={[{ required: true, message: '请输入API基础URL' }]}
                    >
                      <Input placeholder="https://api.openai.com/v1" />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item
                  label="API密钥"
                  name="llm.api_key"
                  rules={[{ required: true, message: '请输入API密钥' }]}
                >
                  <Input.Password placeholder="sk-..." />
                </Form.Item>

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      label="温度"
                      name="llm.temperature"
                      tooltip="控制生成的随机性，0为最确定性"
                    >
                      <InputNumber min={0} max={2} step={0.1} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="最大Token数"
                      name="llm.max_tokens"
                    >
                      <InputNumber min={100} max={32768} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="Top-p"
                      name="llm.top_p"
                      tooltip="核采样参数，控制生成的多样性"
                    >
                      <InputNumber min={0} max={1} step={0.1} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>
              </div>
            ),
          },
          {
            key: 'schema',
            label: <span><ExperimentOutlined />Schema提取</span>,
            children: (
              <div>
                <Alert
                  message="Schema提取配置"
                  description="配置结构化数据提取的Schema和指令。这些设置决定了如何从页面中提取结构化数据。"
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />

                <Form.Item
                  label={
                    <Space>
                      提取指令
                      <Tooltip title="详细的提取指令，告诉系统如何提取数据">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="schema_extraction.instructions"
                >
                  <TextArea
                    rows={6}
                    placeholder="请严格按照以下条件提取商品信息：&#10;1. 只提取页面主要展示的商品信息&#10;2. 忽略推荐商品、相关商品&#10;..."
                  />
                </Form.Item>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="验证Schema"
                      name="schema_extraction.validate_schema"
                      valuePropName="checked"
                      tooltip="是否验证提取的数据符合Schema定义"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="返回原始数据"
                      name="schema_extraction.return_raw"
                      valuePropName="checked"
                      tooltip="是否同时返回原始的未处理数据"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item
                  label={
                    <Space>
                      提取Schema (JSON)
                      <Tooltip title="定义要提取的数据结构的JSON Schema，必须是有效的JSON格式">
                        <InfoCircleOutlined />
                      </Tooltip>
                      <Button
                        size="small"
                        type="link"
                        onClick={() => {
                          const currentValue = form.getFieldValue('schema_extraction.extraction_schema');
                          if (currentValue) {
                            try {
                              const parsed = JSON.parse(currentValue);
                              const formatted = JSON.stringify(parsed, null, 2);
                              form.setFieldValue('schema_extraction.extraction_schema', formatted);
                              message.success('JSON格式化成功');
                            } catch (e) {
                              message.error('JSON格式错误，无法格式化');
                            }
                          }
                        }}
                      >
                        格式化
                      </Button>
                    </Space>
                  }
                  name="schema_extraction.extraction_schema"
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value || value.trim() === '') {
                          return Promise.resolve();
                        }
                        try {
                          JSON.parse(value);
                          return Promise.resolve();
                        } catch (e) {
                          return Promise.reject(new Error('请输入有效的JSON格式'));
                        }
                      }
                    }
                  ]}
                >
                  <TextArea
                    rows={15}
                    placeholder='{"type": "object", "properties": {...}}'
                    style={{ fontFamily: 'monospace' }}
                  />
                </Form.Item>
              </div>
            ),
          },
          {
            key: 'content',
            label: <span><SettingOutlined />内容处理</span>,
            children: (
              <div>
                <Alert
                  message="内容处理配置"
                  description="配置内容提取和处理的相关参数。这些设置影响如何处理和过滤页面内容。"
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      label="词数阈值"
                      name="content_processing.word_count_threshold"
                      tooltip="内容块的最小词数阈值"
                    >
                      <InputNumber min={0} max={10000} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="解析器类型"
                      name="content_processing.parser_type"
                    >
                      <Select>
                        <Option value="lxml">lxml</Option>
                        <Option value="html.parser">html.parser</Option>
                        <Option value="html5lib">html5lib</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="CSS选择器"
                      name="content_processing.css_selector"
                      tooltip="用于选择特定内容的CSS选择器"
                    >
                      <Input placeholder=".content, #main" />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="排除的标签"
                      name="content_processing.excluded_tags"
                      tooltip="要排除的HTML标签列表"
                    >
                      <Select
                        mode="tags"
                        style={{ width: '100%' }}
                        placeholder="nav, footer, aside"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="排除选择器"
                      name="content_processing.excluded_selector"
                      tooltip="要排除的CSS选择器"
                    >
                      <Input placeholder=".ads, .sidebar" />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={6}>
                    <Form.Item
                      label="移除表单"
                      name="content_processing.remove_forms"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="仅文本"
                      name="content_processing.only_text"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="美化输出"
                      name="content_processing.prettify"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="保留数据属性"
                      name="content_processing.keep_data_attributes"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>
              </div>
            ),
          },
          {
            key: 'links',
            label: <span><LinkOutlined />链接过滤</span>,
            children: (
              <div>
                <Alert
                  message="链接过滤配置"
                  description="配置链接的过滤和处理规则。这些设置决定了哪些链接会被跟踪和处理。"
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      label="排除外部链接"
                      name="link_filtering.exclude_external_links"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="排除内部链接"
                      name="link_filtering.exclude_internal_links"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="排除社交媒体链接"
                      name="link_filtering.exclude_social_media_links"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item
                  label="排除域名列表"
                  name="link_filtering.exclude_domains"
                  tooltip="要排除的域名列表"
                >
                  <Select
                    mode="tags"
                    style={{ width: '100%' }}
                    placeholder="example.com, ads.google.com"
                  />
                </Form.Item>

                <Form.Item
                  label="社交媒体域名"
                  name="link_filtering.social_media_domains"
                  tooltip="社交媒体域名列表"
                >
                  <Select
                    mode="tags"
                    style={{ width: '100%' }}
                    placeholder="facebook.com, twitter.com, instagram.com"
                  />
                </Form.Item>

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      label="排除外部图片"
                      name="link_filtering.exclude_external_images"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="排除所有图片"
                      name="link_filtering.exclude_all_images"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="图片评分阈值"
                      name="link_filtering.image_score_threshold"
                      tooltip="图片质量评分阈值"
                    >
                      <InputNumber min={0} max={100} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="图片描述最小词数"
                      name="link_filtering.image_description_min_word_threshold"
                      tooltip="图片描述的最小词数要求"
                    >
                      <InputNumber min={0} max={200} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="表格评分阈值"
                      name="link_filtering.table_score_threshold"
                      tooltip="表格质量评分阈值"
                    >
                      <InputNumber min={0} max={20} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>
              </div>
            ),
          },
          {
            key: 'monitor',
            label: <span><MonitorOutlined />监控配置</span>,
            children: (
              <div>
                <Alert
                  message="监控配置"
                  description="配置爬虫运行时的监控和日志设置。这些设置影响运行时的信息输出和错误处理。"
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      label="显示模式"
                      name="monitor.display_mode"
                      tooltip="监控信息的显示详细程度"
                    >
                      <Select>
                        <Option value="simple">简单</Option>
                        <Option value="detailed">详细</Option>
                        <Option value="debug">调试</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="显示进度"
                      name="monitor.show_progress"
                      valuePropName="checked"
                      tooltip="是否显示处理进度信息"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="记录错误"
                      name="monitor.log_errors"
                      valuePropName="checked"
                      tooltip="是否记录详细的错误信息"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>
              </div>
            ),
          },
        ]}
      />

      {/* 操作按钮 */}
      <div style={{ textAlign: 'right', marginTop: 24 }}>
        <Space>
          <Button onClick={onCancel}>
            <CloseOutlined />
            取消
          </Button>
          <Button
            type="primary"
            loading={submitting}
            onClick={handleSubmit}
          >
            <SaveOutlined />
            {configId ? '更新配置' : '创建配置'}
          </Button>
        </Space>
      </div>
    </Form>
  );
};

export default CrawlerConfigForm;
