/**
 * Worker详情模态框组件
 * 显示Worker的详细信息和统计数据
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Descriptions,
  Tag,
  Progress,
  Spin,
  Alert,
  Row,
  Col,
  Statistic,
  Table,
  Space,
  Button
} from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  <PERSON>boltOutlined,
  TeamOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import type { TableColumnsType } from 'antd';

import { 
  workerApi,
  CrawlerWorker,
  TaskAssignment,
  WorkerStatus,
  WorkerPriority
} from '../../../services';

interface WorkerDetailModalProps {
  workerId: string;
  onClose: () => void;
}

const WorkerDetailModal: React.FC<WorkerDetailModalProps> = ({ workerId, onClose }) => {
  const [loading, setLoading] = useState(false);
  const [worker, setWorker] = useState<CrawlerWorker | null>(null);
  const [stats, setStats] = useState<any>(null);
  const [assignments, setAssignments] = useState<TaskAssignment[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchWorkerData();
  }, [workerId]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchWorkerData = async () => {
    setLoading(true);
    setError(null);
    try {
      const [workerData, statsData, assignmentsData] = await Promise.all([
        workerApi.getWorker(workerId),
        workerApi.getWorkerStats(workerId),
        workerApi.getWorkerAssignmentHistory(workerId, 10)
      ]);
      
      setWorker(workerData);
      setStats(statsData);
      setAssignments(assignmentsData);
    } catch (err) {
      setError(`获取Worker数据失败: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="加载失败"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={fetchWorkerData}>
            重试
          </Button>
        }
      />
    );
  }

  if (!worker) {
    return (
      <Alert
        message="Worker不存在"
        description="找不到指定的Worker"
        type="warning"
        showIcon
      />
    );
  }

  // 状态标签渲染
  const renderWorkerStatus = (status: WorkerStatus) => {
    const statusConfig = {
      idle: { color: 'default', text: '空闲' },
      running: { color: 'green', text: '运行中' },
      busy: { color: 'orange', text: '忙碌' },
      error: { color: 'red', text: '错误' },
      maintenance: { color: 'blue', text: '维护中' }
    };
    
    const config = statusConfig[status] || statusConfig.idle;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const renderWorkerPriority = (priority: WorkerPriority) => {
    const priorityConfig = {
      low: { color: 'default', text: '低' },
      normal: { color: 'blue', text: '普通' },
      high: { color: 'orange', text: '高' },
      urgent: { color: 'red', text: '紧急' }
    };
    
    const config = priorityConfig[priority] || priorityConfig.normal;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 任务分配表格列定义
  const assignmentColumns: TableColumnsType<TaskAssignment> = [
    {
      title: '任务ID',
      dataIndex: 'task_id',
      key: 'task_id',
      render: (id) => <span style={{ fontFamily: 'monospace' }}>{id.slice(0, 8)}...</span>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig: Record<string, { color: string; text: string }> = {
          pending: { color: 'default', text: '待处理' },
          assigned: { color: 'blue', text: '已分配' },
          running: { color: 'orange', text: '运行中' },
          completed: { color: 'green', text: '已完成' },
          failed: { color: 'red', text: '失败' },
          cancelled: { color: 'default', text: '已取消' }
        };
        const config = statusConfig[status] || statusConfig.pending;
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '分配时间',
      dataIndex: 'assigned_at',
      key: 'assigned_at',
      render: (date) => date ? new Date(date).toLocaleString() : '-',
    },
    {
      title: '完成时间',
      dataIndex: 'completed_at',
      key: 'completed_at',
      render: (date) => date ? new Date(date).toLocaleString() : '-',
    },
    {
      title: '执行时长',
      key: 'duration',
      render: (_, record) => {
        if (record.actual_duration) {
          return `${(record.actual_duration / 1000).toFixed(1)}s`;
        }
        return '-';
      },
    },
  ];

  return (
    <div>
      {/* 基本信息 */}
      <Card title="基本信息" style={{ marginBottom: 16 }}>
        <Descriptions column={2}>
          <Descriptions.Item label="Worker名称">{worker.worker_name}</Descriptions.Item>
          <Descriptions.Item label="状态">{renderWorkerStatus(worker.status)}</Descriptions.Item>
          <Descriptions.Item label="优先级">{renderWorkerPriority(worker.priority)}</Descriptions.Item>
          <Descriptions.Item label="健康评分">
            <Progress 
              percent={worker.health_score * 100} 
              size="small" 
              status={worker.health_score >= 0.8 ? 'success' : worker.health_score >= 0.5 ? 'normal' : 'exception'}
              format={() => `${(worker.health_score * 100).toFixed(0)}%`}
            />
          </Descriptions.Item>
          <Descriptions.Item label="当前任务">
            {worker.current_tasks}/{worker.max_concurrent_tasks}
          </Descriptions.Item>
          <Descriptions.Item label="分配并发">{worker.allocated_concurrent}</Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {worker.created_at ? new Date(worker.created_at).toLocaleString() : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="最后心跳">
            {worker.last_heartbeat ? new Date(worker.last_heartbeat).toLocaleString() : '从未'}
          </Descriptions.Item>
          <Descriptions.Item label="描述" span={2}>
            {worker.description || '无描述'}
          </Descriptions.Item>
          <Descriptions.Item label="标签" span={2}>
            <Space>
              {worker.tags.map(tag => (
                <Tag key={tag}>{tag}</Tag>
              ))}
              {worker.tags.length === 0 && <span style={{ color: '#999' }}>无标签</span>}
            </Space>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 统计信息 */}
      {stats && (
        <Card title="统计信息" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="总任务数"
                value={stats.total_tasks}
                prefix={<TeamOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="完成任务"
                value={stats.completed_tasks}
                prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="失败任务"
                value={stats.failed_tasks}
                prefix={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="成功率"
                value={stats.success_rate * 100}
                precision={1}
                suffix="%"
                prefix={<ThunderboltOutlined />}
              />
            </Col>
          </Row>
          <Row gutter={16} style={{ marginTop: 16 }}>
            <Col span={8}>
              <Statistic
                title="平均任务时长"
                value={stats.avg_task_duration / 1000}
                precision={1}
                suffix="秒"
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="当前负载"
                value={stats.current_load}
                suffix={`/${worker.max_concurrent_tasks}`}
              />
              <Progress
                percent={(stats.current_load / worker.max_concurrent_tasks) * 100}
                size="small"
                style={{ marginTop: 8 }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="运行时间"
                value={stats.uptime / 3600}
                precision={1}
                suffix="小时"
              />
            </Col>
          </Row>
        </Card>
      )}

      {/* 最近任务分配 */}
      <Card 
        title="最近任务分配" 
        style={{ marginBottom: 16 }}
        extra={
          <Button 
            size="small" 
            icon={<ReloadOutlined />} 
            onClick={fetchWorkerData}
          >
            刷新
          </Button>
        }
      >
        <Table
          columns={assignmentColumns}
          dataSource={assignments}
          rowKey="assignment_id"
          size="small"
          pagination={false}
          locale={{
            emptyText: '暂无任务分配记录'
          }}
        />
      </Card>

      {/* 配置信息 */}
      <Card title="配置信息">
        <Descriptions column={1}>
          <Descriptions.Item label="爬取配置ID">
            <span style={{ fontFamily: 'monospace' }}>{worker.crawler_config_id}</span>
          </Descriptions.Item>
          <Descriptions.Item label="后端配置ID">
            <span style={{ fontFamily: 'monospace' }}>{worker.backend_config_id}</span>
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </div>
  );
};

export default WorkerDetailModal;
