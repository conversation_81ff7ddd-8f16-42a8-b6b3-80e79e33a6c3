/**
 * Worker管理页面
 * 支持Worker的创建、编辑、删除和状态监控功能
 */

import React, { useState, useEffect } from 'react';
import {
  Typography,
  Card,
  Button,
  Space,
  Table,
  Tag,
  Modal,
  message,
  Row,
  Col,
  Tooltip,
  Popconfirm,
  Badge,
  Statistic,
  Progress,
  Spin,
  Select,
  Input
} from 'antd';
import {
  TeamOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SearchOutlined
} from '@ant-design/icons';
import type { TableColumnsType } from 'antd';

// 导入新架构的API和类型
import { 
  workerApi,
  crawlerConfigApi,
  backendConfigApi,
  CrawlerWorkerSummary,
  WorkerStatus,
  WorkerPriority,
  CrawlerConfigSummary,
  BackendConfigSummary
} from '../../services';

// 导入子组件
import WorkerForm from './components/WorkerForm';
import WorkerDetailModal from './components/WorkerDetailModal';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

const WorkerManagement: React.FC = () => {
  // 状态管理
  const [workers, setWorkers] = useState<CrawlerWorkerSummary[]>([]);
  const [crawlerConfigs, setCrawlerConfigs] = useState<CrawlerConfigSummary[]>([]);
  const [backendConfigs, setBackendConfigs] = useState<BackendConfigSummary[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedWorker, setSelectedWorker] = useState<string | null>(null);
  const [workerModalVisible, setWorkerModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editingWorker, setEditingWorker] = useState<string | null>(null);
  
  // 过滤和搜索状态
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [priorityFilter, setPriorityFilter] = useState<string>('');

  // 初始化数据
  useEffect(() => {
    fetchData();
  }, []);

  // 获取数据
  const fetchData = async () => {
    setLoading(true);
    try {
      const [workersData, crawlerConfigsData, backendConfigsData] = await Promise.all([
        workerApi.getWorkers(),
        crawlerConfigApi.getConfigs(),
        backendConfigApi.getConfigs()
      ]);
      
      setWorkers(workersData);
      setCrawlerConfigs(crawlerConfigsData);
      setBackendConfigs(backendConfigsData);
    } catch (error) {
      message.error(`获取数据失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // Worker操作
  const handleCreateWorker = () => {
    setEditingWorker(null);
    setWorkerModalVisible(true);
  };

  const handleEditWorker = (workerId: string) => {
    setEditingWorker(workerId);
    setWorkerModalVisible(true);
  };

  const handleDeleteWorker = async (workerId: string) => {
    try {
      await workerApi.deleteWorker(workerId);
      message.success('删除Worker成功');
      fetchData();
    } catch (error) {
      message.error(`删除Worker失败: ${error}`);
    }
  };

  const handleToggleWorker = async (workerId: string, action: 'start' | 'stop' | 'restart') => {
    try {
      await workerApi.toggleWorker(workerId, action);
      message.success(`${action === 'start' ? '启动' : action === 'stop' ? '停止' : '重启'}Worker成功`);
      fetchData();
    } catch (error) {
      message.error(`${action === 'start' ? '启动' : action === 'stop' ? '停止' : '重启'}Worker失败: ${error}`);
    }
  };

  const handleViewWorkerDetail = (workerId: string) => {
    setSelectedWorker(workerId);
    setDetailModalVisible(true);
  };

  const handleCheckCompatibility = async (workerId: string) => {
    try {
      const result = await workerApi.checkCompatibility(workerId);
      if (result.is_compatible) {
        message.success('Worker兼容性检查通过');
      } else {
        Modal.warning({
          title: 'Worker兼容性检查失败',
          content: (
            <div>
              <p>发现以下问题：</p>
              <ul>
                {result.errors.map((error, index) => (
                  <li key={index} style={{ color: '#ff4d4f' }}>{error}</li>
                ))}
              </ul>
              {result.warnings.length > 0 && (
                <>
                  <p>警告：</p>
                  <ul>
                    {result.warnings.map((warning, index) => (
                      <li key={index} style={{ color: '#faad14' }}>{warning}</li>
                    ))}
                  </ul>
                </>
              )}
            </div>
          ),
        });
      }
    } catch (error) {
      message.error(`兼容性检查失败: ${error}`);
    }
  };

  // 状态标签渲染
  const renderWorkerStatus = (status: WorkerStatus) => {
    const statusConfig = {
      idle: { color: 'default', text: '空闲' },
      running: { color: 'green', text: '运行中' },
      busy: { color: 'orange', text: '忙碌' },
      error: { color: 'red', text: '错误' },
      maintenance: { color: 'blue', text: '维护中' }
    };
    
    const config = statusConfig[status] || statusConfig.idle;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const renderWorkerPriority = (priority: WorkerPriority) => {
    const priorityConfig = {
      low: { color: 'default', text: '低' },
      normal: { color: 'blue', text: '普通' },
      high: { color: 'orange', text: '高' },
      urgent: { color: 'red', text: '紧急' }
    };
    
    const config = priorityConfig[priority] || priorityConfig.normal;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 过滤数据
  const filteredWorkers = workers.filter(worker => {
    const matchesSearch = !searchText || 
      worker.worker_name.toLowerCase().includes(searchText.toLowerCase()) ||
      worker.description?.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesStatus = !statusFilter || worker.status === statusFilter;
    const matchesPriority = !priorityFilter || worker.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  // Worker表格列定义
  const workerColumns: TableColumnsType<CrawlerWorkerSummary> = [
    {
      title: 'Worker名称',
      dataIndex: 'worker_name',
      key: 'worker_name',
      render: (text, record) => (
        <Space>
          <Text strong>{text}</Text>
          {record.tags.map(tag => (
            <Tag key={tag}>{tag}</Tag>
          ))}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: renderWorkerStatus,
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: renderWorkerPriority,
    },
    {
      title: '并发任务',
      key: 'concurrent_tasks',
      render: (_, record) => (
        <Progress 
          percent={(record.current_tasks / record.max_concurrent_tasks) * 100} 
          size="small"
          format={() => `${record.current_tasks}/${record.max_concurrent_tasks}`}
        />
      ),
    },
    {
      title: '健康评分',
      dataIndex: 'health_score',
      key: 'health_score',
      render: (score) => (
        <Progress 
          percent={score * 100} 
          size="small" 
          status={score >= 0.8 ? 'success' : score >= 0.5 ? 'normal' : 'exception'}
          format={() => `${(score * 100).toFixed(0)}%`}
        />
      ),
    },
    {
      title: '完成任务',
      dataIndex: 'total_tasks_completed',
      key: 'total_tasks_completed',
      render: (count) => <Badge count={count} showZero />,
    },
    {
      title: '成功率',
      dataIndex: 'success_rate',
      key: 'success_rate',
      render: (rate) => `${(rate * 100).toFixed(1)}%`,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => date ? new Date(date).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              onClick={() => handleViewWorkerDetail(record.worker_id)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => handleEditWorker(record.worker_id)}
            />
          </Tooltip>
          <Tooltip title="兼容性检查">
            <Button 
              type="text" 
              icon={<CheckCircleOutlined />} 
              onClick={() => handleCheckCompatibility(record.worker_id)}
            />
          </Tooltip>
          {record.status === 'idle' || record.status === 'error' ? (
            <Tooltip title="启动">
              <Button 
                type="text" 
                icon={<PlayCircleOutlined />} 
                onClick={() => handleToggleWorker(record.worker_id, 'start')}
              />
            </Tooltip>
          ) : (
            <Tooltip title="停止">
              <Button 
                type="text" 
                icon={<PauseCircleOutlined />} 
                onClick={() => handleToggleWorker(record.worker_id, 'stop')}
              />
            </Tooltip>
          )}
          <Popconfirm
            title="确定要删除这个Worker吗？"
            onConfirm={() => handleDeleteWorker(record.worker_id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <TeamOutlined /> Worker管理
        </Title>
        <Text type="secondary">
          管理爬虫Worker，支持创建、编辑、监控和任务分配
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总Worker数"
              value={workers.length}
              prefix={<TeamOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="运行中"
              value={workers.filter(w => w.status === 'running').length}
              prefix={<PlayCircleOutlined style={{ color: '#52c41a' }} />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="空闲"
              value={workers.filter(w => w.status === 'idle').length}
              prefix={<CheckCircleOutlined style={{ color: '#1890ff' }} />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="错误"
              value={workers.filter(w => w.status === 'error').length}
              prefix={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
              suffix="个"
            />
          </Card>
        </Col>
      </Row>

      <Card>
        {/* 工具栏 */}
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <Search
              placeholder="搜索Worker名称或描述"
              allowClear
              style={{ width: 300 }}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              prefix={<SearchOutlined />}
            />
            <Select
              placeholder="状态筛选"
              allowClear
              style={{ width: 120 }}
              value={statusFilter}
              onChange={setStatusFilter}
            >
              <Option value="idle">空闲</Option>
              <Option value="running">运行中</Option>
              <Option value="busy">忙碌</Option>
              <Option value="error">错误</Option>
              <Option value="maintenance">维护中</Option>
            </Select>
            <Select
              placeholder="优先级筛选"
              allowClear
              style={{ width: 120 }}
              value={priorityFilter}
              onChange={setPriorityFilter}
            >
              <Option value="low">低</Option>
              <Option value="normal">普通</Option>
              <Option value="high">高</Option>
              <Option value="urgent">紧急</Option>
            </Select>
          </Space>
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={fetchData}
              loading={loading}
            >
              刷新
            </Button>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={handleCreateWorker}
            >
              新建Worker
            </Button>
          </Space>
        </div>

        {/* Worker表格 */}
        <Spin spinning={loading}>
          <Table
            columns={workerColumns}
            dataSource={filteredWorkers}
            rowKey="worker_id"
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
            }}
          />
        </Spin>
      </Card>

      {/* Worker表单模态框 */}
      <Modal
        title={editingWorker ? "编辑Worker" : "新建Worker"}
        open={workerModalVisible}
        onCancel={() => setWorkerModalVisible(false)}
        footer={null}
        width={600}
        destroyOnHidden
      >
        <WorkerForm
          workerId={editingWorker}
          crawlerConfigs={crawlerConfigs}
          backendConfigs={backendConfigs}
          onSuccess={() => {
            setWorkerModalVisible(false);
            fetchData();
          }}
          onCancel={() => setWorkerModalVisible(false)}
        />
      </Modal>

      {/* Worker详情模态框 */}
      <Modal
        title="Worker详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
        destroyOnHidden
      >
        {selectedWorker && (
          <WorkerDetailModal
            workerId={selectedWorker}
            onClose={() => setDetailModalVisible(false)}
          />
        )}
      </Modal>
    </div>
  );
};

export default WorkerManagement;
