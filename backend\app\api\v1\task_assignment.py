"""
新架构：任务分配API接口
提供任务与Worker分配的管理功能
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query

from ...schemas.task_assignment import (
    TaskAssignment, TaskAssignmentCreate, TaskAssignmentUpdate,
    TaskAssignmentSummary, AssignmentRecommendation, TaskAssignmentStats,
    ConflictDetectionResult, AssignmentStatus, AssignmentStrategy
)
from ...services.task_assignment_service import task_assignment_service
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/task-assignments", tags=["任务分配管理"])


@router.post("/", response_model=TaskAssignment, summary="创建任务分配")
async def create_task_assignment(assignment_data: TaskAssignmentCreate):
    """
    创建新的任务分配
    
    - **task_id**: 任务ID
    - **worker_ids**: Worker ID列表
    - **assignment_strategy**: 分配策略
    - **auto_failover**: 是否自动故障转移
    """
    try:
        assignment = await task_assignment_service.create_assignment(assignment_data)
        return assignment
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create task assignment: {e}")
        raise HTTPException(status_code=500, detail=f"创建任务分配失败: {str(e)}")


@router.get("/", response_model=List[TaskAssignmentSummary], summary="获取任务分配列表")
async def list_task_assignments(
    task_id: Optional[str] = Query(None, description="任务ID过滤"),
    worker_id: Optional[str] = Query(None, description="Worker ID过滤"),
    status: Optional[AssignmentStatus] = Query(None, description="状态过滤"),
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """
    获取任务分配列表
    
    支持按任务、Worker、状态过滤，支持分页
    """
    try:
        assignments = await task_assignment_service.list_assignments(
            task_id=task_id,
            worker_id=worker_id,
            status=status,
            limit=limit,
            offset=offset
        )
        return assignments
    except Exception as e:
        logger.error(f"Failed to list task assignments: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务分配列表失败: {str(e)}")


@router.get("/active", response_model=List[TaskAssignmentSummary], summary="获取活跃任务分配")
async def get_active_assignments():
    """
    获取所有活跃的任务分配
    
    包括已分配和运行中的任务
    """
    try:
        assignments = await task_assignment_service.list_assignments(limit=1000)
        active_assignments = [a for a in assignments if a.is_active]
        return active_assignments
    except Exception as e:
        logger.error(f"Failed to get active assignments: {e}")
        raise HTTPException(status_code=500, detail=f"获取活跃任务分配失败: {str(e)}")


@router.get("/{assignment_id}", response_model=TaskAssignment, summary="获取任务分配详情")
async def get_task_assignment(assignment_id: str):
    """
    根据ID获取任务分配详情
    """
    try:
        assignment = await task_assignment_service.get_assignment(assignment_id)
        if not assignment:
            raise HTTPException(status_code=404, detail="任务分配不存在")
        return assignment
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get task assignment {assignment_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务分配失败: {str(e)}")


@router.put("/{assignment_id}", response_model=TaskAssignment, summary="更新任务分配")
async def update_task_assignment(assignment_id: str, update_data: TaskAssignmentUpdate):
    """
    更新任务分配
    
    只更新提供的字段，其他字段保持不变
    """
    try:
        assignment = await task_assignment_service.update_assignment(assignment_id, update_data)
        if not assignment:
            raise HTTPException(status_code=404, detail="任务分配不存在")
        return assignment
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update task assignment {assignment_id}: {e}")
        raise HTTPException(status_code=500, detail=f"更新任务分配失败: {str(e)}")


@router.delete("/{assignment_id}", summary="删除任务分配")
async def delete_task_assignment(assignment_id: str):
    """
    删除任务分配
    
    注意：如果任务正在运行，删除会失败
    """
    try:
        success = await task_assignment_service.delete_assignment(assignment_id)
        if not success:
            raise HTTPException(status_code=400, detail="删除任务分配失败")
        return {"message": "任务分配删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete task assignment {assignment_id}: {e}")
        raise HTTPException(status_code=500, detail=f"删除任务分配失败: {str(e)}")


@router.post("/check-conflicts", response_model=ConflictDetectionResult, summary="检测Worker冲突")
async def detect_worker_conflicts(worker_ids: List[str]):
    """
    检测Worker组合中的资源冲突
    
    返回冲突详情和解决建议
    """
    try:
        conflict_result = await task_assignment_service.detect_conflicts(worker_ids)
        return conflict_result
    except Exception as e:
        logger.error(f"Failed to detect worker conflicts: {e}")
        raise HTTPException(status_code=500, detail=f"冲突检测失败: {str(e)}")


@router.patch("/{assignment_id}/status", response_model=TaskAssignment, summary="更新分配状态")
async def update_assignment_status(assignment_id: str, status: AssignmentStatus):
    """
    更新任务分配状态
    
    - **pending**: 待分配
    - **assigned**: 已分配
    - **running**: 运行中
    - **completed**: 已完成
    - **failed**: 失败
    - **cancelled**: 已取消
    """
    try:
        update_data = TaskAssignmentUpdate(status=status)
        assignment = await task_assignment_service.update_assignment(assignment_id, update_data)
        if not assignment:
            raise HTTPException(status_code=404, detail="任务分配不存在")
        return assignment
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update assignment status {assignment_id}: {e}")
        raise HTTPException(status_code=500, detail=f"更新状态失败: {str(e)}")


@router.post("/{assignment_id}/start", response_model=TaskAssignment, summary="启动任务分配")
async def start_task_assignment(assignment_id: str):
    """
    启动任务分配
    
    将状态从已分配改为运行中，并记录开始时间
    """
    try:
        from datetime import datetime
        
        update_data = TaskAssignmentUpdate(
            status=AssignmentStatus.RUNNING,
            start_time=datetime.now()
        )
        assignment = await task_assignment_service.update_assignment(assignment_id, update_data)
        if not assignment:
            raise HTTPException(status_code=404, detail="任务分配不存在")
        return assignment
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start task assignment {assignment_id}: {e}")
        raise HTTPException(status_code=500, detail=f"启动任务分配失败: {str(e)}")


@router.post("/{assignment_id}/complete", response_model=TaskAssignment, summary="完成任务分配")
async def complete_task_assignment(assignment_id: str):
    """
    完成任务分配
    
    将状态改为已完成，并记录结束时间和实际时长
    """
    try:
        from datetime import datetime
        
        assignment = await task_assignment_service.get_assignment(assignment_id)
        if not assignment:
            raise HTTPException(status_code=404, detail="任务分配不存在")
        
        end_time = datetime.now()
        actual_duration = None
        if assignment.start_time:
            actual_duration = int((end_time - assignment.start_time).total_seconds())
        
        update_data = TaskAssignmentUpdate(
            status=AssignmentStatus.COMPLETED,
            end_time=end_time,
            actual_duration=actual_duration
        )
        
        updated_assignment = await task_assignment_service.update_assignment(assignment_id, update_data)
        return updated_assignment
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to complete task assignment {assignment_id}: {e}")
        raise HTTPException(status_code=500, detail=f"完成任务分配失败: {str(e)}")


@router.post("/{assignment_id}/fail", response_model=TaskAssignment, summary="标记任务分配失败")
async def fail_task_assignment(
    assignment_id: str, 
    error_message: str = Query(..., description="错误信息")
):
    """
    标记任务分配失败
    
    记录错误信息和失败时间
    """
    try:
        from datetime import datetime
        
        assignment = await task_assignment_service.get_assignment(assignment_id)
        if not assignment:
            raise HTTPException(status_code=404, detail="任务分配不存在")
        
        update_data = TaskAssignmentUpdate(
            status=AssignmentStatus.FAILED,
            end_time=datetime.now(),
            last_error=error_message,
            error_count=assignment.error_count + 1
        )
        
        updated_assignment = await task_assignment_service.update_assignment(assignment_id, update_data)
        return updated_assignment
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to fail task assignment {assignment_id}: {e}")
        raise HTTPException(status_code=500, detail=f"标记失败失败: {str(e)}")


@router.post("/{assignment_id}/update-progress", response_model=TaskAssignment, summary="更新任务进度")
async def update_task_progress(
    assignment_id: str,
    processed_urls: int = Query(..., ge=0, description="已处理URL数"),
    successful_urls: int = Query(..., ge=0, description="成功URL数"),
    failed_urls: int = Query(..., ge=0, description="失败URL数")
):
    """
    更新任务执行进度
    
    记录处理的URL数量和成功失败情况
    """
    try:
        update_data = TaskAssignmentUpdate(
            processed_urls=processed_urls,
            successful_urls=successful_urls,
            failed_urls=failed_urls
        )
        
        assignment = await task_assignment_service.update_assignment(assignment_id, update_data)
        if not assignment:
            raise HTTPException(status_code=404, detail="任务分配不存在")
        return assignment
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update task progress {assignment_id}: {e}")
        raise HTTPException(status_code=500, detail=f"更新进度失败: {str(e)}")


@router.get("/{assignment_id}/progress", summary="获取任务进度")
async def get_task_progress(assignment_id: str):
    """
    获取任务执行进度信息
    """
    try:
        assignment = await task_assignment_service.get_assignment(assignment_id)
        if not assignment:
            raise HTTPException(status_code=404, detail="任务分配不存在")
        
        progress = {
            "assignment_id": assignment_id,
            "task_id": assignment.task_id,
            "status": assignment.status,
            "total_urls": assignment.total_urls,
            "processed_urls": assignment.processed_urls,
            "successful_urls": assignment.successful_urls,
            "failed_urls": assignment.failed_urls,
            "remaining_urls": assignment.remaining_urls,
            "progress_rate": assignment.progress_rate,
            "success_rate": assignment.success_rate,
            "start_time": assignment.start_time,
            "estimated_remaining_time": assignment.get_estimated_remaining_time(),
            "current_worker_id": assignment.current_worker_id
        }
        
        return progress
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get task progress {assignment_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取进度失败: {str(e)}")


@router.get("/task/{task_id}/assignments", response_model=List[TaskAssignmentSummary], summary="获取任务的所有分配")
async def get_task_assignments(task_id: str):
    """
    获取指定任务的所有分配记录
    """
    try:
        assignments = await task_assignment_service.list_assignments(
            task_id=task_id,
            limit=1000
        )
        return assignments
    except Exception as e:
        logger.error(f"Failed to get task assignments for {task_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务分配失败: {str(e)}")


@router.get("/worker/{worker_id}/assignments", response_model=List[TaskAssignmentSummary], summary="获取Worker的所有分配")
async def get_worker_assignments(worker_id: str):
    """
    获取指定Worker的所有分配记录
    """
    try:
        assignments = await task_assignment_service.list_assignments(
            worker_id=worker_id,
            limit=1000
        )
        return assignments
    except Exception as e:
        logger.error(f"Failed to get worker assignments for {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取Worker分配失败: {str(e)}")
