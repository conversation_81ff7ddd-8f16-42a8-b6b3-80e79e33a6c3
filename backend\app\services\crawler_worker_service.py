"""
新架构：爬虫Worker管理服务
管理配置组合和Worker兼容性检查
"""

import json
import uuid
import logging
import redis
import os
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any, Set
from fastapi import HTTPException

from ..schemas.crawler_worker import (
    CrawlerWorker, CrawlerWorkerCreate, CrawlerWorkerUpdate,
    CrawlerWorkerSummary, CrawlerWorkerDetail, CrawlerWorkerStats,
    WorkerCompatibilityCheck, WorkerGroupCompatibilityCheck,
    WorkerStatus, WorkerPriority
)
from ..services.crawler_config_service import crawler_config_service
from ..services.backend_config_service import backend_config_service

logger = logging.getLogger(__name__)


class CrawlerWorkerService:
    """爬虫Worker管理服务"""
    
    def __init__(self):
        # 使用现有的Redis连接模式
        redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        self.data_dir = Path("data/crawler_workers")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Redis键模式
        self.keys = {
            'worker': 'crawler_worker:{}',
            'worker_list': 'crawler_worker:list',
            'worker_by_name': 'crawler_worker:name:{}',
            'workers_by_backend': 'crawler_worker:backend:{}',
            'workers_by_config': 'crawler_worker:config:{}',
            'worker_stats': 'crawler_worker:stats'
        }
    
    async def create_worker(self, worker_data: CrawlerWorkerCreate) -> CrawlerWorker:
        """创建Worker"""
        try:
            # 检查名称是否已存在
            existing_worker = await self.get_worker_by_name(worker_data.worker_name)
            if existing_worker:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Worker名称 '{worker_data.worker_name}' 已存在"
                )
            
            # 验证配置是否存在
            crawler_config = await crawler_config_service.get_config(worker_data.crawler_config_id)
            if not crawler_config:
                raise HTTPException(
                    status_code=400, 
                    detail=f"爬取配置 {worker_data.crawler_config_id} 不存在"
                )
            
            backend_config = await backend_config_service.get_config(worker_data.backend_config_id)
            if not backend_config:
                raise HTTPException(
                    status_code=400, 
                    detail=f"后端配置 {worker_data.backend_config_id} 不存在"
                )
            
            # 检查配置状态
            if not crawler_config.is_active:
                raise HTTPException(
                    status_code=400, 
                    detail=f"爬取配置 '{crawler_config.config_name}' 不是活跃状态"
                )
            
            if not backend_config.is_available:
                raise HTTPException(
                    status_code=400, 
                    detail=f"后端配置 '{backend_config.backend_name}' 不可用"
                )
            
            # 检查并发分配是否超过后端限制
            if worker_data.allocated_concurrent > backend_config.max_concurrent:
                raise HTTPException(
                    status_code=400, 
                    detail=f"分配的并发数 ({worker_data.allocated_concurrent}) 超过后端最大并发数 ({backend_config.max_concurrent})"
                )
            
            # 生成Worker ID
            worker_id = str(uuid.uuid4())
            now = datetime.now()
            
            # 创建Worker对象
            worker = CrawlerWorker(
                worker_id=worker_id,
                worker_name=worker_data.worker_name,
                description=worker_data.description,
                crawler_config_id=worker_data.crawler_config_id,
                backend_config_id=worker_data.backend_config_id,
                priority=worker_data.priority,
                allocated_concurrent=worker_data.allocated_concurrent,
                max_tasks_per_hour=worker_data.max_tasks_per_hour,
                created_at=now,
                updated_at=now,
                created_by=worker_data.created_by
            )
            
            # 保存Worker
            await self._save_worker(worker)
            
            # 添加到各种索引
            self.redis_client.sadd(self.keys['worker_list'], worker_id)
            self.redis_client.set(
                self.keys['worker_by_name'].format(worker_data.worker_name), 
                worker_id
            )
            self.redis_client.sadd(
                self.keys['workers_by_backend'].format(worker_data.backend_config_id), 
                worker_id
            )
            self.redis_client.sadd(
                self.keys['workers_by_config'].format(worker_data.crawler_config_id), 
                worker_id
            )
            
            logger.info(f"Created crawler worker: {worker_id} ({worker_data.worker_name})")
            return worker
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to create crawler worker: {e}")
            raise HTTPException(status_code=500, detail=f"创建Worker失败: {str(e)}")
    
    async def get_worker(self, worker_id: str) -> Optional[CrawlerWorker]:
        """获取Worker"""
        try:
            # 从Redis获取
            worker_data = self.redis_client.get(self.keys['worker'].format(worker_id))
            if worker_data:
                worker_dict = json.loads(worker_data)
                return CrawlerWorker(**worker_dict)
            
            # 从文件获取
            worker_file = self.data_dir / f"{worker_id}.json"
            if worker_file.exists():
                with open(worker_file, 'r', encoding='utf-8') as f:
                    worker_dict = json.load(f)
                    worker = CrawlerWorker(**worker_dict)
                    # 同步到Redis
                    await self._save_worker(worker)
                    return worker
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get crawler worker {worker_id}: {e}")
            return None
    
    async def get_worker_by_name(self, worker_name: str) -> Optional[CrawlerWorker]:
        """根据名称获取Worker"""
        try:
            worker_id = self.redis_client.get(self.keys['worker_by_name'].format(worker_name))
            if worker_id:
                worker_id_str = worker_id if isinstance(worker_id, str) else worker_id.decode()
                return await self.get_worker(worker_id_str)
            return None
        except Exception as e:
            logger.error(f"Failed to get worker by name {worker_name}: {e}")
            return None
    
    async def get_worker_detail(self, worker_id: str) -> Optional[CrawlerWorkerDetail]:
        """获取Worker详细信息（包含关联配置）"""
        try:
            worker = await self.get_worker(worker_id)
            if not worker:
                return None
            
            # 获取关联的配置
            crawler_config = await crawler_config_service.get_config(worker.crawler_config_id)
            backend_config = await backend_config_service.get_config(worker.backend_config_id)
            
            # 执行兼容性检查
            compatibility_check = await self.check_worker_compatibility(worker_id)
            
            return CrawlerWorkerDetail(
                worker=worker,
                crawler_config=crawler_config,
                backend_config=backend_config,
                compatibility_check=compatibility_check.dict() if compatibility_check else None
            )
            
        except Exception as e:
            logger.error(f"Failed to get worker detail {worker_id}: {e}")
            return None
    
    async def update_worker(self, worker_id: str, update_data: CrawlerWorkerUpdate) -> Optional[CrawlerWorker]:
        """更新Worker"""
        try:
            # 获取现有Worker
            worker = await self.get_worker(worker_id)
            if not worker:
                raise HTTPException(status_code=404, detail="Worker不存在")
            
            # 检查名称冲突
            if update_data.worker_name and update_data.worker_name != worker.worker_name:
                existing_worker = await self.get_worker_by_name(update_data.worker_name)
                if existing_worker and existing_worker.worker_id != worker_id:
                    raise HTTPException(
                        status_code=400, 
                        detail=f"Worker名称 '{update_data.worker_name}' 已存在"
                    )
            
            # 验证配置更新
            if update_data.crawler_config_id and update_data.crawler_config_id != worker.crawler_config_id:
                crawler_config = await crawler_config_service.get_config(update_data.crawler_config_id)
                if not crawler_config or not crawler_config.is_active:
                    raise HTTPException(
                        status_code=400, 
                        detail="指定的爬取配置不存在或不活跃"
                    )
            
            if update_data.backend_config_id and update_data.backend_config_id != worker.backend_config_id:
                backend_config = await backend_config_service.get_config(update_data.backend_config_id)
                if not backend_config or not backend_config.is_available:
                    raise HTTPException(
                        status_code=400, 
                        detail="指定的后端配置不存在或不可用"
                    )
            
            # 更新Worker
            update_dict = update_data.dict(exclude_unset=True)
            old_name = worker.worker_name
            old_backend_id = worker.backend_config_id
            old_config_id = worker.crawler_config_id
            
            for field, value in update_dict.items():
                if hasattr(worker, field):
                    setattr(worker, field, value)
            
            worker.updated_at = datetime.now()
            
            # 保存更新后的Worker
            await self._save_worker(worker)
            
            # 更新索引
            if update_data.worker_name and update_data.worker_name != old_name:
                self.redis_client.delete(self.keys['worker_by_name'].format(old_name))
                self.redis_client.set(
                    self.keys['worker_by_name'].format(update_data.worker_name), 
                    worker_id
                )
            
            if update_data.backend_config_id and update_data.backend_config_id != old_backend_id:
                self.redis_client.srem(self.keys['workers_by_backend'].format(old_backend_id), worker_id)
                self.redis_client.sadd(self.keys['workers_by_backend'].format(update_data.backend_config_id), worker_id)
            
            if update_data.crawler_config_id and update_data.crawler_config_id != old_config_id:
                self.redis_client.srem(self.keys['workers_by_config'].format(old_config_id), worker_id)
                self.redis_client.sadd(self.keys['workers_by_config'].format(update_data.crawler_config_id), worker_id)
            
            logger.info(f"Updated crawler worker: {worker_id}")
            return worker
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to update crawler worker {worker_id}: {e}")
            raise HTTPException(status_code=500, detail=f"更新Worker失败: {str(e)}")
    
    async def delete_worker(self, worker_id: str) -> bool:
        """删除Worker"""
        try:
            # 获取Worker信息
            worker = await self.get_worker(worker_id)
            if not worker:
                raise HTTPException(status_code=404, detail="Worker不存在")
            
            # 检查是否正在使用
            if worker.current_tasks > 0:
                raise HTTPException(
                    status_code=400, 
                    detail="Worker正在执行任务，无法删除"
                )
            
            # 从Redis删除
            self.redis_client.delete(self.keys['worker'].format(worker_id))
            self.redis_client.srem(self.keys['worker_list'], worker_id)
            self.redis_client.delete(self.keys['worker_by_name'].format(worker.worker_name))
            self.redis_client.srem(self.keys['workers_by_backend'].format(worker.backend_config_id), worker_id)
            self.redis_client.srem(self.keys['workers_by_config'].format(worker.crawler_config_id), worker_id)
            
            # 删除文件
            worker_file = self.data_dir / f"{worker_id}.json"
            if worker_file.exists():
                worker_file.unlink()
            
            logger.info(f"Deleted crawler worker: {worker_id}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to delete crawler worker {worker_id}: {e}")
            return False
    
    async def list_workers(
        self, 
        status: Optional[WorkerStatus] = None,
        priority: Optional[WorkerPriority] = None,
        backend_config_id: Optional[str] = None,
        crawler_config_id: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[CrawlerWorkerSummary]:
        """获取Worker列表"""
        try:
            # 获取Worker ID列表
            if backend_config_id:
                worker_ids = self.redis_client.smembers(self.keys['workers_by_backend'].format(backend_config_id))
            elif crawler_config_id:
                worker_ids = self.redis_client.smembers(self.keys['workers_by_config'].format(crawler_config_id))
            else:
                worker_ids = self.redis_client.smembers(self.keys['worker_list'])
            
            if not worker_ids:
                return []
            
            # 获取Worker详情
            workers = []
            for worker_id in worker_ids:
                worker_id_str = worker_id if isinstance(worker_id, str) else worker_id.decode()
                worker = await self.get_worker(worker_id_str)
                if worker:
                    workers.append(worker)
            
            # 过滤
            if status:
                workers = [w for w in workers if w.status == status]
            
            if priority:
                workers = [w for w in workers if w.priority == priority]
            
            # 排序（按优先级和健康评分）
            workers.sort(key=lambda x: (x.priority.value, x.health_score), reverse=True)
            
            # 分页
            workers = workers[offset:offset + limit]
            
            # 转换为摘要（需要获取配置名称）
            summaries = []
            for worker in workers:
                crawler_config = await crawler_config_service.get_config(worker.crawler_config_id)
                backend_config = await backend_config_service.get_config(worker.backend_config_id)
                
                summary = CrawlerWorkerSummary(
                    worker_id=worker.worker_id,
                    worker_name=worker.worker_name,
                    description=worker.description,
                    status=worker.status,
                    priority=worker.priority,
                    crawler_config_name=crawler_config.config_name if crawler_config else "未知配置",
                    backend_config_name=backend_config.backend_name if backend_config else "未知后端",
                    backend_endpoint=backend_config.api_endpoint if backend_config else "未知端点",
                    allocated_concurrent=worker.allocated_concurrent,
                    current_tasks=worker.current_tasks,
                    available_capacity=worker.available_capacity,
                    utilization_rate=worker.utilization_rate,
                    success_rate=worker.success_rate,
                    health_score=worker.health_score,
                    created_at=worker.created_at,
                    last_used=worker.last_used
                )
                summaries.append(summary)
            
            return summaries
            
        except Exception as e:
            logger.error(f"Failed to list crawler workers: {e}")
            return []
    
    async def check_worker_compatibility(self, worker_id: str) -> Optional[WorkerCompatibilityCheck]:
        """检查Worker兼容性"""
        try:
            worker = await self.get_worker(worker_id)
            if not worker:
                return None
            
            result = WorkerCompatibilityCheck(
                worker_id=worker_id,
                is_compatible=True
            )
            
            # 检查配置是否存在
            crawler_config = await crawler_config_service.get_config(worker.crawler_config_id)
            backend_config = await backend_config_service.get_config(worker.backend_config_id)
            
            result.config_exists = crawler_config is not None and backend_config is not None
            
            if not result.config_exists:
                result.is_compatible = False
                if not crawler_config:
                    result.errors.append(f"爬取配置 {worker.crawler_config_id} 不存在")
                if not backend_config:
                    result.errors.append(f"后端配置 {worker.backend_config_id} 不存在")
            
            # 检查后端是否可用
            if backend_config:
                result.backend_available = backend_config.is_available
                if not result.backend_available:
                    result.is_compatible = False
                    result.errors.append(f"后端 '{backend_config.backend_name}' 不可用")
            
            # 检查资源冲突
            conflicted_workers = await self.get_conflicted_workers(worker_id)
            result.resource_conflict = len(conflicted_workers) > 0
            result.conflicted_workers = conflicted_workers
            
            if result.resource_conflict:
                result.warnings.append(f"与 {len(conflicted_workers)} 个Worker存在资源冲突")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to check worker compatibility {worker_id}: {e}")
            return None
    
    async def get_conflicted_workers(self, worker_id: str) -> List[str]:
        """获取与指定Worker冲突的Worker列表"""
        try:
            worker = await self.get_worker(worker_id)
            if not worker:
                return []
            
            # 获取使用相同后端的所有Worker
            backend_workers = self.redis_client.smembers(
                self.keys['workers_by_backend'].format(worker.backend_config_id)
            )
            
            conflicted_workers = []
            for other_worker_id in backend_workers:
                other_worker_id_str = other_worker_id if isinstance(other_worker_id, str) else other_worker_id.decode()
                if other_worker_id_str != worker_id:
                    other_worker = await self.get_worker(other_worker_id_str)
                    if other_worker and other_worker.status == WorkerStatus.ACTIVE:
                        conflicted_workers.append(other_worker_id_str)
            
            return conflicted_workers
            
        except Exception as e:
            logger.error(f"Failed to get conflicted workers for {worker_id}: {e}")
            return []
    
    async def _save_worker(self, worker: CrawlerWorker):
        """保存Worker到Redis和文件"""
        try:
            worker_dict = worker.dict()
            worker_json = json.dumps(worker_dict, default=str, ensure_ascii=False)
            
            # 保存到Redis
            self.redis_client.set(
                self.keys['worker'].format(worker.worker_id), 
                worker_json
            )
            
            # 保存到文件
            worker_file = self.data_dir / f"{worker.worker_id}.json"
            with open(worker_file, 'w', encoding='utf-8') as f:
                json.dump(worker_dict, f, indent=2, ensure_ascii=False, default=str)
            
        except Exception as e:
            logger.error(f"Failed to save worker {worker.worker_id}: {e}")
            raise


# 全局服务实例
crawler_worker_service = CrawlerWorkerService()
