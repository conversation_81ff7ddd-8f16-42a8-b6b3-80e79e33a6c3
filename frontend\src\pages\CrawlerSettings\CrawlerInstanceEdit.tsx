/**
 * 编辑爬虫实例配置页面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  InputNumber,
  Button,
  Space,
  message,
  Steps,
  Row,
  Col,
  Divider,
  Typography,
  Alert,
  Spin,
  Tag
} from 'antd';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  ApiOutlined,
  HeartOutlined
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';

import { 
  crawlerInstanceApi, 
  CrawlerInstanceConfig,
  CrawlerInstanceConfigUpdate,
  AuthConfig
} from '../../services/crawlerInstanceApi';

const { Step } = Steps;
const { TextArea } = Input;
const { Option } = Select;
const { Title } = Typography;

const CrawlerInstanceEditPage: React.FC = () => {
  const navigate = useNavigate();
  const { configId } = useParams<{ configId: string }>();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState(0);
  const [config, setConfig] = useState<CrawlerInstanceConfig | null>(null);

  // 加载配置
  const loadConfig = async () => {
    if (!configId) return;
    
    try {
      setPageLoading(true);
      const configData = await crawlerInstanceApi.getConfig(configId);
      setConfig(configData);
      
      // 设置表单值
      setTimeout(() => {
        form.setFieldsValue({
          config_name: configData.config_name,
          description: configData.description,
          api_endpoint: configData.api_endpoint,
          timeout: configData.timeout,
          max_retries: configData.max_retries,
          auth_type: configData.auth_config.auth_type,
          api_key: configData.auth_config.api_key,
          username: configData.auth_config.username,
          password: configData.auth_config.password,
          max_concurrent: configData.max_concurrent,
          weight: configData.weight,
          priority: configData.priority,
        });
      }, 100);
      
    } catch (error: any) {
      message.error(error.response?.data?.detail || '加载爬虫配置失败');
      navigate('/crawler-settings/instance');
    } finally {
      setPageLoading(false);
    }
  };

  // 更新配置
  const handleUpdateConfig = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      // 构建认证配置
      const authConfig: AuthConfig = {
        auth_type: values.auth_type,
        api_key: values.api_key,
        username: values.username,
        password: values.password,
      };

      const updateData: CrawlerInstanceConfigUpdate = {
        config_name: values.config_name,
        description: values.description,
        api_endpoint: values.api_endpoint,
        timeout: values.timeout,
        max_retries: values.max_retries,
        auth_config: authConfig,
        max_concurrent: values.max_concurrent,
        weight: values.weight,
        priority: values.priority,
      };

      await crawlerInstanceApi.updateConfig(configId!, updateData);
      message.success('爬虫配置更新成功');
      navigate('/crawler-settings/instance');
      
    } catch (error: any) {
      message.error(error.response?.data?.detail || '更新爬虫配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试连接
  const handleTestConnection = async () => {
    if (!configId) return;
    
    try {
      setLoading(true);
      const result = await crawlerInstanceApi.testConnection(configId);
      
      if (result.is_connected) {
        message.success(`连接测试成功 (${crawlerInstanceApi.formatResponseTime(result.response_time)})`);
      } else {
        message.error(`连接测试失败: ${result.error_message}`);
      }
    } catch (error) {
      message.error('连接测试失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConfig();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [configId]);

  const steps = [
    {
      title: '基本信息',
      content: (
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="配置名称"
              name="config_name"
              rules={[{ required: true, message: '请输入配置名称' }]}
            >
              <Input placeholder="输入爬虫配置名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="API端点"
              name="api_endpoint"
              rules={[
                { required: true, message: '请输入API端点' },
                { type: 'url', message: '请输入有效的URL' }
              ]}
            >
              <Input placeholder="http://localhost:11234" />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="描述"
              name="description"
            >
              <TextArea rows={3} placeholder="输入配置描述（可选）" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="超时时间(ms)"
              name="timeout"
            >
              <InputNumber min={1000} max={300000} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="最大重试次数"
              name="max_retries"
            >
              <InputNumber min={0} max={10} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="最大并发数"
              name="max_concurrent"
            >
              <InputNumber min={1} max={20} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      ),
    },
    {
      title: '认证配置',
      content: (
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="认证类型"
              name="auth_type"
            >
              <Select>
                <Option value="none">无认证</Option>
                <Option value="api_key">API密钥</Option>
                <Option value="bearer_token">Bearer Token</Option>
                <Option value="basic_auth">基础认证</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) => 
                prevValues.auth_type !== currentValues.auth_type
              }
            >
              {({ getFieldValue }) => {
                const authType = getFieldValue('auth_type');
                
                if (authType === 'api_key' || authType === 'bearer_token') {
                  return (
                    <Form.Item
                      label="API密钥"
                      name="api_key"
                      rules={[{ required: true, message: '请输入API密钥' }]}
                    >
                      <Input.Password placeholder="输入API密钥" />
                    </Form.Item>
                  );
                }
                
                return null;
              }}
            </Form.Item>
          </Col>
          
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => 
              prevValues.auth_type !== currentValues.auth_type
            }
          >
            {({ getFieldValue }) => {
              const authType = getFieldValue('auth_type');
              
              if (authType === 'basic_auth') {
                return (
                  <>
                    <Col span={12}>
                      <Form.Item
                        label="用户名"
                        name="username"
                        rules={[{ required: true, message: '请输入用户名' }]}
                      >
                        <Input placeholder="输入用户名" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="密码"
                        name="password"
                        rules={[{ required: true, message: '请输入密码' }]}
                      >
                        <Input.Password placeholder="输入密码" />
                      </Form.Item>
                    </Col>
                  </>
                );
              }
              
              return null;
            }}
          </Form.Item>
        </Row>
      ),
    },
    {
      title: '性能配置',
      content: (
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              label="权重"
              name="weight"
              tooltip="负载均衡时的权重，数值越大分配的任务越多"
            >
              <InputNumber min={1} max={100} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="优先级"
              name="priority"
              tooltip="任务分配优先级，数值越小优先级越高"
            >
              <InputNumber min={1} max={10} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="最大并发数"
              name="max_concurrent"
              tooltip="该配置同时处理的最大任务数"
            >
              <InputNumber min={1} max={20} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          {config && (
            <Col span={24}>
              <Alert
                message="当前配置状态"
                description={
                  <div>
                    <p><strong>健康评分</strong>: {(config.health_score * 100).toFixed(1)}%</p>
                    <p><strong>成功率</strong>: {crawlerInstanceApi.calculateSuccessRate(config).toFixed(1)}%</p>
                    <p><strong>平均响应时间</strong>: {crawlerInstanceApi.formatResponseTime(config.avg_response_time)}</p>
                    <p><strong>总请求数</strong>: {config.total_requests}</p>
                  </div>
                }
                type="info"
                showIcon
              />
            </Col>
          )}
        </Row>
      ),
    },
  ];

  if (pageLoading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载爬虫配置中...</div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <Space>
            <ApiOutlined />
            编辑爬虫实例配置
            {config && <Tag color="blue">{config.config_name}</Tag>}
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<HeartOutlined />}
              onClick={handleTestConnection}
              loading={loading}
            >
              测试连接
            </Button>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/crawler-settings/instance')}
            >
              返回
            </Button>
          </Space>
        }
      >
        <Steps current={currentStep} style={{ marginBottom: 24 }}>
          {steps.map(item => (
            <Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <Form
          form={form}
          layout="vertical"
          style={{ marginTop: 24 }}
        >
          {steps[currentStep].content}
        </Form>

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Space>
            {currentStep > 0 && (
              <Button onClick={() => setCurrentStep(currentStep - 1)}>
                上一步
              </Button>
            )}
            
            {currentStep < steps.length - 1 && (
              <Button 
                type="primary" 
                onClick={() => setCurrentStep(currentStep + 1)}
              >
                下一步
              </Button>
            )}
            
            {currentStep === steps.length - 1 && (
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={loading}
                onClick={handleUpdateConfig}
              >
                保存更改
              </Button>
            )}
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default CrawlerInstanceEditPage;
