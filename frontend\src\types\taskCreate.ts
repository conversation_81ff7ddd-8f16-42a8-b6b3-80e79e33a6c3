/**
 * 任务创建相关的TypeScript类型定义
 */

export type ScheduleType = 'once' | 'daily' | 'weekly' | 'hourly' | 'custom';
export type TaskPriority = 'low' | 'normal' | 'high' | 'urgent';

export interface ScheduleConfig {
  type: ScheduleType;
  enabled: boolean;
  time?: string;              // "09:00" 格式，用于daily类型
  start_time?: string;        // 兼容字段
  days?: number[];            // [1,2,3,4,5] 周一到周五，用于weekly类型
  interval?: number;          // 自定义间隔（小时）
  cron_expression?: string;   // cron表达式
  timezone: string;
  end_time?: string;
  max_runs?: number;
  // 随机启动范围配置
  enable_random_delay?: boolean;
  random_delay_min?: number;  // 最小随机延迟（分钟）
  random_delay_max?: number;  // 最大随机延迟（分钟）
}

export interface TaskConfig {
  platform: string;
  priority: TaskPriority;
  retry_count: number;
  timeout: number;
  concurrent_limit: number;
  batch_size: number;
  enable_notifications: boolean;
  notification_config?: Record<string, any>;
}

export interface TaskCreateFromUrlsRequest {
  name: string;
  description?: string;
  url_ids: string[];
  schedule: ScheduleConfig;
  config: TaskConfig;
  tags?: string[];
}

export interface TaskCreateResponse {
  success: boolean;
  message: string;
  task_id: string;
  task_name: string;
  url_count: number;
  schedule_info: string;
  next_run?: string;
}

export interface UrlPreviewRequest {
  url_ids: string[];
}

export interface UrlPreviewResponse {
  success: boolean;
  total_count: number;
  valid_count: number;
  invalid_count: number;
  platform_distribution: Record<string, number>;
  urls: Array<{
    id: string;
    url: string;
    platform: string;
    status: string;
    source_file: string;
    added_at: string;
    check_count: number;
    success_count: number;
  }>;
}

export interface TaskValidationRequest {
  name: string;
  url_ids: string[];
  schedule: ScheduleConfig;
}

export interface TaskValidationResponse {
  success: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

export interface TaskTemplate {
  id: string;
  name: string;
  description?: string;
  platform: string;
  default_schedule: ScheduleConfig;
  default_config: TaskConfig;
  usage_count: number;
}

export interface TaskCreateStats {
  total_tasks: number;
  active_tasks: number;
  scheduled_tasks: number;
  platform_distribution: Record<string, number>;
  recent_tasks: Array<{
    id: string;
    name: string;
    url_count: number;
    status: string;
    created_at: string;
  }>;
}

// 任务创建向导步骤
export enum CreateStep {
  SELECT_URLS = 0,
  BASIC_CONFIG = 1,
  SCHEDULE_CONFIG = 2,
  CONFIRM = 3
}

// 任务创建向导数据
export interface TaskCreateWizardData {
  selectedUrls: string[];
  basicConfig: {
    name: string;
    description: string;
    platform: string;
    priority: TaskPriority;
    tags: string[];
  };
  taskConfig: TaskConfig;
  scheduleConfig: ScheduleConfig;
}

// 向导步骤配置
export interface WizardStepConfig {
  title: string;
  description: string;
  icon: React.ReactNode;
  optional?: boolean;
}

// URL选择步骤状态
export interface UrlSelectionState {
  selectedUrls: string[];
  previewData: UrlPreviewResponse | null;
  loading: boolean;
  error: string | null;
}

// 基础配置步骤状态
export interface BasicConfigState {
  name: string;
  description: string;
  platform: string;
  priority: TaskPriority;
  tags: string[];
  validation: {
    nameError?: string;
    platformError?: string;
  };
}

// 调度配置步骤状态
export interface ScheduleConfigState {
  type: ScheduleType;
  enabled: boolean;
  startTime?: Date;
  interval?: number;
  cronExpression?: string;
  timezone: string;
  endTime?: Date;
  maxRuns?: number;
  validation: {
    startTimeError?: string;
    intervalError?: string;
    cronError?: string;
  };
}

// 确认步骤状态
export interface ConfirmState {
  validationResult: TaskValidationResponse | null;
  creating: boolean;
  error: string | null;
}

// 调度类型选项
export interface ScheduleTypeOption {
  value: ScheduleType;
  label: string;
  description: string;
  icon: React.ReactNode;
  disabled?: boolean;
}

// 优先级选项
export interface PriorityOption {
  value: TaskPriority;
  label: string;
  description: string;
  color: string;
}

// 平台选项
export interface PlatformOption {
  value: string;
  label: string;
  description: string;
  icon: React.ReactNode;
}

// 表单验证规则
export interface ValidationRule {
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
  validator?: (value: any) => string | undefined;
}

// 表单字段配置
export interface FormFieldConfig {
  name: string;
  label: string;
  type: 'input' | 'textarea' | 'select' | 'number' | 'switch' | 'datetime' | 'tags';
  placeholder?: string;
  tooltip?: string;
  rules?: ValidationRule[];
  options?: Array<{ value: any; label: string; description?: string }>;
  disabled?: boolean;
  hidden?: boolean;
}

// 任务创建结果
export interface TaskCreateResult {
  success: boolean;
  task: {
    id: string;
    name: string;
    url_count: number;
    schedule_info: string;
    next_run?: string;
  };
  message: string;
}

// 错误类型
export interface TaskCreateError {
  type: 'validation' | 'network' | 'server' | 'unknown';
  message: string;
  details?: any;
}

// 常量定义
export const SCHEDULE_TYPE_OPTIONS: ScheduleTypeOption[] = [
  {
    value: 'once',
    label: '一次性执行',
    description: '立即执行一次',
    icon: '⚡'
  },
  {
    value: 'daily',
    label: '每日执行',
    description: '每天在指定时间执行',
    icon: '📅'
  },
  {
    value: 'weekly',
    label: '每周执行',
    description: '每周在指定时间执行',
    icon: '📆'
  },
  {
    value: 'hourly',
    label: '每小时执行',
    description: '按指定间隔执行',
    icon: '⏰'
  },
  {
    value: 'custom',
    label: '自定义调度',
    description: '使用Cron表达式',
    icon: '⚙️'
  }
];

export const PRIORITY_OPTIONS: PriorityOption[] = [
  {
    value: 'low',
    label: '低优先级',
    description: '适用于非紧急任务',
    color: '#52c41a'
  },
  {
    value: 'normal',
    label: '普通优先级',
    description: '默认优先级',
    color: '#1890ff'
  },
  {
    value: 'high',
    label: '高优先级',
    description: '重要任务优先执行',
    color: '#fa8c16'
  },
  {
    value: 'urgent',
    label: '紧急优先级',
    description: '最高优先级，立即执行',
    color: '#f5222d'
  }
];

export const DEFAULT_TASK_CONFIG: TaskConfig = {
  platform: 'mercadolibre',
  priority: 'normal',
  retry_count: 3,
  timeout: 300,
  concurrent_limit: 5,
  batch_size: 10,
  enable_notifications: true
};

export const DEFAULT_SCHEDULE_CONFIG: ScheduleConfig = {
  type: 'daily',
  enabled: true,
  timezone: 'Asia/Shanghai',
  enable_random_delay: false,
  random_delay_min: 0,
  random_delay_max: 180  // 默认3小时
};
