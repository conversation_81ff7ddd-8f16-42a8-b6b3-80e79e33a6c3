"""
新架构：任务分配管理服务
管理任务与Worker的分配关系和冲突检测
"""

import json
import uuid
import logging
import redis
import os
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any, Set
from fastapi import HTTPException

from ..schemas.task_assignment import (
    TaskAssignment, TaskAssignmentCreate, TaskAssignmentUpdate,
    TaskAssignmentSummary, AssignmentRecommendation, TaskAssignmentStats,
    ConflictDetectionResult, AssignmentStatus, AssignmentStrategy
)
from ..schemas.crawler_worker import WorkerGroupCompatibilityCheck
from ..services.crawler_worker_service import crawler_worker_service

logger = logging.getLogger(__name__)


class TaskAssignmentService:
    """任务分配管理服务"""
    
    def __init__(self):
        # 使用现有的Redis连接模式
        redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        self.data_dir = Path("data/task_assignments")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Redis键模式
        self.keys = {
            'assignment': 'task_assignment:{}',
            'assignment_list': 'task_assignment:list',
            'assignments_by_task': 'task_assignment:task:{}',
            'assignments_by_worker': 'task_assignment:worker:{}',
            'assignment_stats': 'task_assignment:stats'
        }
    
    async def create_assignment(self, assignment_data: TaskAssignmentCreate) -> TaskAssignment:
        """创建任务分配"""
        try:
            # 验证Worker是否存在
            workers = []
            for worker_id in assignment_data.worker_ids:
                worker = await crawler_worker_service.get_worker(worker_id)
                if not worker:
                    raise HTTPException(
                        status_code=400, 
                        detail=f"Worker {worker_id} 不存在"
                    )
                workers.append(worker)
            
            # 执行兼容性检查
            compatibility_check = await self.check_worker_group_compatibility(assignment_data.worker_ids)
            if not compatibility_check.is_compatible:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Worker组不兼容: {', '.join(compatibility_check.resource_conflicts)}"
                )
            
            # 生成分配ID
            assignment_id = str(uuid.uuid4())
            now = datetime.now()
            
            # 确定主要Worker
            primary_worker_id = assignment_data.primary_worker_id
            if not primary_worker_id and assignment_data.worker_ids:
                # 选择优先级最高的Worker作为主要Worker
                primary_worker = max(workers, key=lambda w: w.priority.value)
                primary_worker_id = primary_worker.worker_id
            
            # 创建分配对象
            assignment = TaskAssignment(
                assignment_id=assignment_id,
                task_id=assignment_data.task_id,
                assignment_name=assignment_data.assignment_name,
                description=assignment_data.description,
                worker_ids=assignment_data.worker_ids,
                primary_worker_id=primary_worker_id,
                backup_worker_ids=assignment_data.backup_worker_ids,
                assignment_strategy=assignment_data.assignment_strategy,
                auto_failover=assignment_data.auto_failover,
                max_retries=assignment_data.max_retries,
                total_urls=assignment_data.total_urls,
                estimated_duration=assignment_data.estimated_duration,
                compatibility_check=compatibility_check,
                created_at=now,
                updated_at=now,
                created_by=assignment_data.created_by
            )
            
            # 保存分配
            await self._save_assignment(assignment)
            
            # 添加到索引
            self.redis_client.sadd(self.keys['assignment_list'], assignment_id)
            self.redis_client.sadd(
                self.keys['assignments_by_task'].format(assignment_data.task_id), 
                assignment_id
            )
            
            for worker_id in assignment_data.worker_ids:
                self.redis_client.sadd(
                    self.keys['assignments_by_worker'].format(worker_id), 
                    assignment_id
                )
            
            logger.info(f"Created task assignment: {assignment_id} for task {assignment_data.task_id}")
            return assignment
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to create task assignment: {e}")
            raise HTTPException(status_code=500, detail=f"创建任务分配失败: {str(e)}")
    
    async def get_assignment(self, assignment_id: str) -> Optional[TaskAssignment]:
        """获取任务分配"""
        try:
            # 从Redis获取
            assignment_data = self.redis_client.get(self.keys['assignment'].format(assignment_id))
            if assignment_data:
                assignment_dict = json.loads(assignment_data)
                return TaskAssignment(**assignment_dict)
            
            # 从文件获取
            assignment_file = self.data_dir / f"{assignment_id}.json"
            if assignment_file.exists():
                with open(assignment_file, 'r', encoding='utf-8') as f:
                    assignment_dict = json.load(f)
                    assignment = TaskAssignment(**assignment_dict)
                    # 同步到Redis
                    await self._save_assignment(assignment)
                    return assignment
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get task assignment {assignment_id}: {e}")
            return None
    
    async def update_assignment(self, assignment_id: str, update_data: TaskAssignmentUpdate) -> Optional[TaskAssignment]:
        """更新任务分配"""
        try:
            # 获取现有分配
            assignment = await self.get_assignment(assignment_id)
            if not assignment:
                raise HTTPException(status_code=404, detail="任务分配不存在")
            
            # 更新分配
            update_dict = update_data.dict(exclude_unset=True)
            for field, value in update_dict.items():
                if hasattr(assignment, field):
                    setattr(assignment, field, value)
            
            assignment.updated_at = datetime.now()
            
            # 保存更新后的分配
            await self._save_assignment(assignment)
            
            logger.info(f"Updated task assignment: {assignment_id}")
            return assignment
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to update task assignment {assignment_id}: {e}")
            raise HTTPException(status_code=500, detail=f"更新任务分配失败: {str(e)}")
    
    async def delete_assignment(self, assignment_id: str) -> bool:
        """删除任务分配"""
        try:
            # 获取分配信息
            assignment = await self.get_assignment(assignment_id)
            if not assignment:
                raise HTTPException(status_code=404, detail="任务分配不存在")
            
            # 检查是否正在运行
            if assignment.is_running:
                raise HTTPException(
                    status_code=400, 
                    detail="任务分配正在运行，无法删除"
                )
            
            # 从Redis删除
            self.redis_client.delete(self.keys['assignment'].format(assignment_id))
            self.redis_client.srem(self.keys['assignment_list'], assignment_id)
            self.redis_client.srem(
                self.keys['assignments_by_task'].format(assignment.task_id), 
                assignment_id
            )
            
            for worker_id in assignment.worker_ids:
                self.redis_client.srem(
                    self.keys['assignments_by_worker'].format(worker_id), 
                    assignment_id
                )
            
            # 删除文件
            assignment_file = self.data_dir / f"{assignment_id}.json"
            if assignment_file.exists():
                assignment_file.unlink()
            
            logger.info(f"Deleted task assignment: {assignment_id}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to delete task assignment {assignment_id}: {e}")
            return False
    
    async def list_assignments(
        self, 
        task_id: Optional[str] = None,
        worker_id: Optional[str] = None,
        status: Optional[AssignmentStatus] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[TaskAssignmentSummary]:
        """获取任务分配列表"""
        try:
            # 获取分配ID列表
            if task_id:
                assignment_ids = self.redis_client.smembers(self.keys['assignments_by_task'].format(task_id))
            elif worker_id:
                assignment_ids = self.redis_client.smembers(self.keys['assignments_by_worker'].format(worker_id))
            else:
                assignment_ids = self.redis_client.smembers(self.keys['assignment_list'])
            
            if not assignment_ids:
                return []
            
            # 获取分配详情
            assignments = []
            for assignment_id in assignment_ids:
                assignment_id_str = assignment_id if isinstance(assignment_id, str) else assignment_id.decode()
                assignment = await self.get_assignment(assignment_id_str)
                if assignment:
                    assignments.append(assignment)
            
            # 过滤
            if status:
                assignments = [a for a in assignments if a.status == status]
            
            # 排序（按创建时间倒序）
            assignments.sort(key=lambda x: x.created_at or datetime.min, reverse=True)
            
            # 分页
            assignments = assignments[offset:offset + limit]
            
            # 转换为摘要
            summaries = []
            for assignment in assignments:
                # 获取Worker名称
                primary_worker_name = None
                current_worker_name = None
                
                if assignment.primary_worker_id:
                    primary_worker = await crawler_worker_service.get_worker(assignment.primary_worker_id)
                    primary_worker_name = primary_worker.worker_name if primary_worker else None
                
                if assignment.current_worker_id:
                    current_worker = await crawler_worker_service.get_worker(assignment.current_worker_id)
                    current_worker_name = current_worker.worker_name if current_worker else None
                
                summary = TaskAssignmentSummary(
                    assignment_id=assignment.assignment_id,
                    task_id=assignment.task_id,
                    assignment_name=assignment.assignment_name,
                    status=assignment.status,
                    assignment_strategy=assignment.assignment_strategy,
                    worker_count=len(assignment.worker_ids),
                    primary_worker_name=primary_worker_name,
                    current_worker_name=current_worker_name,
                    total_urls=assignment.total_urls,
                    processed_urls=assignment.processed_urls,
                    progress_rate=assignment.progress_rate,
                    success_rate=assignment.success_rate,
                    created_at=assignment.created_at,
                    start_time=assignment.start_time,
                    estimated_remaining_time=assignment.get_estimated_remaining_time()
                )
                summaries.append(summary)
            
            return summaries
            
        except Exception as e:
            logger.error(f"Failed to list task assignments: {e}")
            return []
    
    async def check_worker_group_compatibility(self, worker_ids: List[str]) -> WorkerGroupCompatibilityCheck:
        """检查Worker组兼容性"""
        try:
            result = WorkerGroupCompatibilityCheck(
                worker_ids=worker_ids,
                is_compatible=True,
                individual_checks=[],
                resource_conflicts=[],
                total_workers=len(worker_ids),
                valid_workers=0,
                conflicted_workers=0,
                recommended_workers=[],
                alternative_workers=[]
            )
            
            # 检查每个Worker的兼容性
            workers = []
            for worker_id in worker_ids:
                worker = await crawler_worker_service.get_worker(worker_id)
                if worker:
                    workers.append(worker)
                    worker_check = await crawler_worker_service.check_worker_compatibility(worker_id)
                    if worker_check:
                        result.individual_checks.append(worker_check)
                        if worker_check.is_compatible:
                            result.valid_workers += 1
                        else:
                            result.conflicted_workers += 1
            
            # 检查Worker间的资源冲突
            resource_usage = {}
            for worker in workers:
                resource_key = worker.resource_key
                if resource_key in resource_usage:
                    # 发现冲突
                    conflict_info = {
                        "resource": resource_key,
                        "conflicted_workers": resource_usage[resource_key] + [worker.worker_id]
                    }
                    result.resource_conflicts.append(conflict_info)
                    result.is_compatible = False
                    result.conflicted_workers += 1
                else:
                    resource_usage[resource_key] = [worker.worker_id]
            
            # 生成推荐
            if result.is_compatible:
                result.recommended_workers = worker_ids
            else:
                # 推荐无冲突的Worker组合
                valid_workers = [w.worker_id for w in workers if w.is_available]
                result.alternative_workers = valid_workers[:3]  # 推荐前3个可用Worker
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to check worker group compatibility: {e}")
            return WorkerGroupCompatibilityCheck(
                worker_ids=worker_ids,
                is_compatible=False,
                individual_checks=[],
                resource_conflicts=[],
                total_workers=len(worker_ids),
                valid_workers=0,
                conflicted_workers=len(worker_ids),
                recommended_workers=[],
                alternative_workers=[]
            )
    
    async def detect_conflicts(self, worker_ids: List[str]) -> ConflictDetectionResult:
        """检测Worker冲突"""
        try:
            result = ConflictDetectionResult(
                has_conflicts=False,
                conflict_groups=[],
                resource_conflicts={},
                capacity_conflicts=[],
                resolution_suggestions=[],
                alternative_workers=[]
            )
            
            # 获取Worker信息
            workers = []
            for worker_id in worker_ids:
                worker = await crawler_worker_service.get_worker(worker_id)
                if worker:
                    workers.append(worker)
            
            # 按资源键分组
            resource_groups = {}
            for worker in workers:
                resource_key = worker.resource_key
                if resource_key not in resource_groups:
                    resource_groups[resource_key] = []
                resource_groups[resource_key].append(worker.worker_id)
            
            # 检查冲突
            for resource_key, worker_group in resource_groups.items():
                if len(worker_group) > 1:
                    result.has_conflicts = True
                    result.conflict_groups.append(worker_group)
                    result.resource_conflicts[resource_key] = worker_group
            
            # 检查容量冲突
            for worker in workers:
                if not worker.is_available:
                    result.capacity_conflicts.append(worker.worker_id)
            
            # 生成解决建议
            if result.has_conflicts:
                result.resolution_suggestions.append("移除冲突的Worker，每个后端只保留一个Worker")
                result.resolution_suggestions.append("使用不同后端的Worker组合")
            
            if result.capacity_conflicts:
                result.resolution_suggestions.append("选择有可用容量的Worker")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to detect conflicts: {e}")
            return ConflictDetectionResult(
                has_conflicts=True,
                conflict_groups=[],
                resource_conflicts={},
                capacity_conflicts=worker_ids,
                resolution_suggestions=["检测过程出错，请重试"],
                alternative_workers=[]
            )
    
    async def _save_assignment(self, assignment: TaskAssignment):
        """保存分配到Redis和文件"""
        try:
            assignment_dict = assignment.dict()
            assignment_json = json.dumps(assignment_dict, default=str, ensure_ascii=False)
            
            # 保存到Redis
            self.redis_client.set(
                self.keys['assignment'].format(assignment.assignment_id), 
                assignment_json
            )
            
            # 保存到文件
            assignment_file = self.data_dir / f"{assignment.assignment_id}.json"
            with open(assignment_file, 'w', encoding='utf-8') as f:
                json.dump(assignment_dict, f, indent=2, ensure_ascii=False, default=str)
            
        except Exception as e:
            logger.error(f"Failed to save assignment {assignment.assignment_id}: {e}")
            raise


# 全局服务实例
task_assignment_service = TaskAssignmentService()
