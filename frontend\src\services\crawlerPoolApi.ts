/**
 * 爬虫池API服务
 * 提供爬虫池的CRUD操作和相关功能
 */

import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';
const API_BASE = `${API_BASE_URL}/api/v1/crawler/pools`;

// 创建API客户端
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
});

// 类型定义
export interface CrawlerBackend {
  id: string;
  name: string;
  description?: string;
  base_url: string;
  weight: number;
  priority: number;
  max_concurrent: number;
  timeout: number;
  auth_type: string;
  auth_config: Record<string, any>;
  performance_config: {
    max_requests_per_minute: number;
    concurrent_limit: number;
    retry_count: number;
    retry_delay: number;
  };
  health_check_config: {
    enabled: boolean;
    endpoint: string;
    interval: number;
    timeout: number;
    failure_threshold: number;
    recovery_threshold: number;
  };
  created_at?: string;
  updated_at?: string;
}

export interface CrawlerBackendCreate {
  name: string;
  description?: string;
  base_url: string;
  weight?: number;
  priority?: number;
  max_concurrent?: number;
  timeout?: number;
  auth_type?: string;
  auth_config?: Record<string, any>;
  performance_config?: {
    max_requests_per_minute?: number;
    concurrent_limit?: number;
    retry_count?: number;
    retry_delay?: number;
  };
  health_check_config?: {
    enabled?: boolean;
    endpoint?: string;
    interval?: number;
    timeout?: number;
    failure_threshold?: number;
    recovery_threshold?: number;
  };
}

export interface CrawlerBackendUpdate extends Partial<CrawlerBackendCreate> {}

export interface CrawlerPoolConfig {
  pool_id: string;
  pool_name: string;
  description?: string;
  backends: CrawlerBackend[];
  load_balance_strategy: string;
  health_check_interval: number;
  failure_threshold: number;
  recovery_threshold?: number;
  created_at?: string;
  updated_at?: string;
}

export interface CrawlerPoolCreate {
  pool_name: string;
  description?: string;
  backends: CrawlerBackendCreate[];
  load_balance_strategy?: string;
  health_check_interval?: number;
  failure_threshold?: number;
  recovery_threshold?: number;
}

export interface CrawlerPoolUpdate extends Partial<CrawlerPoolCreate> {}

export interface PoolHealthStatus {
  pool_id: string;
  overall_health: number;
  backend_health: Record<string, {
    status: 'healthy' | 'unhealthy' | 'unknown';
    response_time?: number;
    last_check?: string;
    error_message?: string;
  }>;
  last_updated: string;
}

// API函数
export const crawlerPoolApi = {
  // 获取所有爬虫池
  async getPools(): Promise<CrawlerPoolConfig[]> {
    const response = await api.get('/api/v1/crawler/pools');
    return response.data;
  },

  // 获取单个爬虫池
  async getPool(poolId: string): Promise<CrawlerPoolConfig> {
    const response = await api.get(`/api/v1/crawler/pools/${poolId}`);
    return response.data;
  },

  // 创建爬虫池
  async createPool(data: CrawlerPoolCreate): Promise<CrawlerPoolConfig> {
    const response = await api.post('/api/v1/crawler/pools', data);
    return response.data;
  },

  // 更新爬虫池
  async updatePool(poolId: string, data: CrawlerPoolUpdate): Promise<CrawlerPoolConfig> {
    const response = await api.put(`/api/v1/crawler/pools/${poolId}`, data);
    return response.data;
  },

  // 删除爬虫池
  async deletePool(poolId: string): Promise<void> {
    await api.delete(`/api/v1/crawler/pools/${poolId}`);
  },

  // 获取爬虫池健康状态
  async getPoolHealth(poolId: string): Promise<PoolHealthStatus> {
    const response = await api.get(`/api/v1/crawler/pools/${poolId}/health`);
    return response.data;
  },

  // 获取爬虫池配置（兼容格式）
  async getPoolConfig(poolId: string): Promise<any> {
    const response = await api.get(`/api/v1/crawler/pools/${poolId}/config`);
    return response.data;
  },

  // 测试后端连接
  async testBackend(backend: CrawlerBackendCreate): Promise<{ success: boolean; message: string; response_time?: number }> {
    const response = await api.post('/api/v1/crawler/pools/test-backend', backend);
    return response.data;
  },
};

export default crawlerPoolApi;
