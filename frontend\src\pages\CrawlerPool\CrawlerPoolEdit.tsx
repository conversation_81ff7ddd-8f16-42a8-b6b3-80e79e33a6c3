/**
 * 编辑爬虫池页面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  InputNumber,
  Button,
  Space,
  message,
  Steps,
  Row,
  Col,
  Divider,
  Table,
  Modal,
  Tag,
  Spin
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  ArrowLeftOutlined,
  SaveOutlined,
  ClusterOutlined
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';

import { 
  crawlerPoolApi, 
  CrawlerPoolConfig,
  CrawlerPoolUpdate, 
  CrawlerBackendCreate,
  CrawlerBackendUpdate 
} from '../../services/crawlerPoolApi';

const { Step } = Steps;
const { TextArea } = Input;
const { Option } = Select;

const CrawlerPoolEditPage: React.FC = () => {
  const navigate = useNavigate();
  const { poolId } = useParams<{ poolId: string }>();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState(0);
  const [poolConfig, setPoolConfig] = useState<CrawlerPoolConfig | null>(null);
  const [backends, setBackends] = useState<CrawlerBackendCreate[]>([]);
  const [backendModalVisible, setBackendModalVisible] = useState(false);
  const [editingBackend, setEditingBackend] = useState<CrawlerBackendCreate | null>(null);
  const [editingIndex, setEditingIndex] = useState(-1);

  // 加载爬虫池配置
  const loadPoolConfig = async () => {
    if (!poolId) return;

    try {
      setPageLoading(true);
      const config = await crawlerPoolApi.getPool(poolId);
      setPoolConfig(config);

      // 延迟设置表单值，确保Form组件已经渲染
      setTimeout(() => {
        form.setFieldsValue({
          pool_name: config.pool_name,
          description: config.description,
          load_balance_strategy: config.load_balance_strategy,
          health_check_interval: config.health_check_interval,
          failure_threshold: config.failure_threshold,
          recovery_threshold: config.recovery_threshold,
        });
      }, 100);
      
      // 检查架构类型并处理配置
      if (config.crawler_config_ids) {
        // 新架构：显示提示信息
        message.info('此爬虫池使用新架构，请使用新的管理界面进行编辑');
        navigate(`/crawler-pool`);
        return;
      } else if (config.backends) {
        // 旧架构：转换后端配置
        const backendConfigs = config.backends.map(backend => ({
          name: backend.name,
          description: backend.description,
          base_url: backend.base_url,
          timeout: backend.timeout,
          max_retries: backend.max_retries,
          auth_type: backend.auth_type,
          api_key: backend.api_key,
          username: backend.username,
          password: backend.password,
          max_concurrent: backend.max_concurrent,
          weight: backend.weight,
          priority: backend.priority,
        }));
        setBackends(backendConfigs);
      } else {
        // 空配置
        setBackends([]);
      }
      
    } catch (error: any) {
      message.error(error.response?.data?.detail || '加载爬虫池配置失败');
      navigate('/crawler-pool');
    } finally {
      setPageLoading(false);
    }
  };

  // 更新爬虫池
  const handleUpdatePool = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      if (backends.length === 0) {
        message.error('至少需要保留一个爬虫后端');
        return;
      }

      const updateData: CrawlerPoolUpdate = {
        pool_name: values.pool_name,
        description: values.description,
        load_balance_strategy: values.load_balance_strategy,
        health_check_interval: values.health_check_interval,
        failure_threshold: values.failure_threshold,
        recovery_threshold: values.recovery_threshold,
      };

      await crawlerPoolApi.updatePool(poolId!, updateData);
      
      // 更新后端配置
      if (poolConfig && poolConfig.backends) {
        // 删除不存在的后端（仅适用于旧架构）
        for (const existingBackend of poolConfig.backends) {
          const stillExists = backends.some(b =>
            b.name === existingBackend.name && b.base_url === existingBackend.base_url
          );
          if (!stillExists) {
            await crawlerPoolApi.removeBackendFromPool(poolId!, existingBackend.id);
          }
        }
        
        // 添加或更新后端（仅适用于旧架构）
        for (let i = 0; i < backends.length; i++) {
          const backend = backends[i];
          const existingBackend = poolConfig.backends?.find(b =>
            b.name === backend.name && b.base_url === backend.base_url
          );
          
          if (existingBackend) {
            // 更新现有后端
            const updateData: CrawlerBackendUpdate = {
              name: backend.name,
              description: backend.description,
              base_url: backend.base_url,
              timeout: backend.timeout,
              max_retries: backend.max_retries,
              auth_type: backend.auth_type,
              api_key: backend.api_key,
              username: backend.username,
              password: backend.password,
              max_concurrent: backend.max_concurrent,
              weight: backend.weight,
              priority: backend.priority,
            };
            await crawlerPoolApi.updateBackend(poolId!, existingBackend.id, updateData);
          } else {
            // 添加新后端
            await crawlerPoolApi.addBackendToPool(poolId!, backend);
          }
        }
      }

      message.success('爬虫池更新成功');
      navigate('/crawler-pool');
      
    } catch (error: any) {
      message.error(error.response?.data?.detail || '更新爬虫池失败');
    } finally {
      setLoading(false);
    }
  };

  // 添加/编辑后端
  const handleSaveBackend = (backendData: CrawlerBackendCreate) => {
    if (editingIndex >= 0) {
      // 编辑现有后端
      const newBackends = [...backends];
      newBackends[editingIndex] = backendData;
      setBackends(newBackends);
    } else {
      // 添加新后端
      setBackends([...backends, backendData]);
    }
    
    setBackendModalVisible(false);
    setEditingBackend(null);
    setEditingIndex(-1);
  };

  // 删除后端
  const handleDeleteBackend = (index: number) => {
    if (backends.length <= 1) {
      message.error('爬虫池必须至少保留一个后端');
      return;
    }
    
    const newBackends = backends.filter((_, i) => i !== index);
    setBackends(newBackends);
  };

  // 编辑后端
  const handleEditBackend = (index: number) => {
    setEditingBackend(backends[index]);
    setEditingIndex(index);
    setBackendModalVisible(true);
  };

  useEffect(() => {
    loadPoolConfig();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [poolId]);

  // 后端表格列
  const backendColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'API地址',
      dataIndex: 'base_url',
      key: 'base_url',
      ellipsis: true,
    },
    {
      title: '权重',
      dataIndex: 'weight',
      key: 'weight',
      render: (weight: number) => <Tag color="blue">{weight}</Tag>,
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: number) => <Tag color="green">{priority}</Tag>,
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, __: any, index: number) => (
        <Space>
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleEditBackend(index)}
          />
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDeleteBackend(index)}
            disabled={backends.length <= 1}
          />
        </Space>
      ),
    },
  ];

  const steps = [
    {
      title: '基本信息',
      content: (
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="池名称"
              name="pool_name"
              rules={[{ required: true, message: '请输入池名称' }]}
            >
              <Input placeholder="输入爬虫池名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="负载均衡策略"
              name="load_balance_strategy"
            >
              <Select>
                <Option value="round_robin">轮询</Option>
                <Option value="weighted_round_robin">加权轮询</Option>
                <Option value="least_connections">最少连接</Option>
                <Option value="health_based">基于健康评分</Option>
                <Option value="response_time">基于响应时间</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="描述"
              name="description"
            >
              <TextArea rows={3} placeholder="输入爬虫池描述（可选）" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="健康检查间隔(秒)"
              name="health_check_interval"
            >
              <InputNumber min={10} max={3600} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="故障阈值"
              name="failure_threshold"
            >
              <InputNumber min={1} max={10} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="恢复阈值"
              name="recovery_threshold"
            >
              <InputNumber min={1} max={10} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      ),
    },
    {
      title: '后端配置',
      content: (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingBackend(null);
                setEditingIndex(-1);
                setBackendModalVisible(true);
              }}
            >
              添加爬虫后端
            </Button>
          </div>
          
          <Table
            columns={backendColumns}
            dataSource={backends.map((backend, index) => ({ ...backend, key: `backend-${index}` }))}
            rowKey="key"
            pagination={false}
            locale={{ emptyText: '暂无爬虫后端，请添加至少一个后端' }}
          />
        </div>
      ),
    },
  ];

  if (pageLoading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载爬虫池配置中...</div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <Space>
            <ClusterOutlined />
            编辑爬虫池
            {poolConfig && <Tag color="blue">{poolConfig.pool_name}</Tag>}
          </Space>
        }
        extra={
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/crawler-pool')}
          >
            返回
          </Button>
        }
      >
        <Steps current={currentStep} style={{ marginBottom: 24 }}>
          {steps.map(item => (
            <Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <Form
          form={form}
          layout="vertical"
          style={{ marginTop: 24 }}
        >
          {steps[currentStep].content}
        </Form>

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Space>
            {currentStep > 0 && (
              <Button onClick={() => setCurrentStep(currentStep - 1)}>
                上一步
              </Button>
            )}
            
            {currentStep < steps.length - 1 && (
              <Button 
                type="primary" 
                onClick={() => setCurrentStep(currentStep + 1)}
              >
                下一步
              </Button>
            )}
            
            {currentStep === steps.length - 1 && (
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={loading}
                onClick={handleUpdatePool}
              >
                保存更改
              </Button>
            )}
          </Space>
        </div>
      </Card>

      {/* 后端配置模态框 - 重用创建页面的组件 */}
      <BackendModal
        visible={backendModalVisible}
        backend={editingBackend}
        onSave={handleSaveBackend}
        onCancel={() => {
          setBackendModalVisible(false);
          setEditingBackend(null);
          setEditingIndex(-1);
        }}
      />
    </div>
  );
};

// 后端配置模态框组件（从创建页面复制）
interface BackendModalProps {
  visible: boolean;
  backend: CrawlerBackendCreate | null;
  onSave: (backend: CrawlerBackendCreate) => void;
  onCancel: () => void;
}

const BackendModal: React.FC<BackendModalProps> = ({ visible, backend, onSave, onCancel }) => {
  const [form] = Form.useForm();

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      onSave(values);
      form.resetFields();
    } catch (error) {
      // 表单验证失败
    }
  };

  React.useEffect(() => {
    if (visible) {
      if (backend) {
        form.setFieldsValue(backend);
      } else {
        form.resetFields();
      }
    }
  }, [visible, backend, form]);

  return (
    <Modal
      title={backend ? '编辑爬虫后端' : '添加爬虫后端'}
      open={visible}
      onOk={handleSave}
      onCancel={onCancel}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          timeout: 30000,
          max_retries: 3,
          auth_type: 'none',
          max_concurrent: 2,
          weight: 1,
          priority: 1,
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="后端名称"
              name="name"
              rules={[{ required: true, message: '请输入后端名称' }]}
            >
              <Input placeholder="输入后端名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="API地址"
              name="base_url"
              rules={[
                { required: true, message: '请输入API地址' },
                { type: 'url', message: '请输入有效的URL' }
              ]}
            >
              <Input placeholder="http://localhost:11234" />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="描述"
              name="description"
            >
              <TextArea rows={2} placeholder="输入后端描述（可选）" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="超时时间(ms)"
              name="timeout"
            >
              <InputNumber min={1000} max={300000} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="最大重试次数"
              name="max_retries"
            >
              <InputNumber min={0} max={10} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="最大并发数"
              name="max_concurrent"
            >
              <InputNumber min={1} max={20} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="权重"
              name="weight"
            >
              <InputNumber min={1} max={100} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="优先级"
              name="priority"
            >
              <InputNumber min={1} max={10} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="认证类型"
              name="auth_type"
            >
              <Select>
                <Option value="none">无认证</Option>
                <Option value="api_key">API密钥</Option>
                <Option value="bearer_token">Bearer Token</Option>
                <Option value="basic_auth">基础认证</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) => 
            prevValues.auth_type !== currentValues.auth_type
          }
        >
          {({ getFieldValue }) => {
            const authType = getFieldValue('auth_type');
            
            if (authType === 'api_key' || authType === 'bearer_token') {
              return (
                <Form.Item
                  label="API密钥"
                  name="api_key"
                  rules={[{ required: true, message: '请输入API密钥' }]}
                >
                  <Input.Password placeholder="输入API密钥" />
                </Form.Item>
              );
            }
            
            if (authType === 'basic_auth') {
              return (
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="用户名"
                      name="username"
                      rules={[{ required: true, message: '请输入用户名' }]}
                    >
                      <Input placeholder="输入用户名" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="密码"
                      name="password"
                      rules={[{ required: true, message: '请输入密码' }]}
                    >
                      <Input.Password placeholder="输入密码" />
                    </Form.Item>
                  </Col>
                </Row>
              );
            }
            
            return null;
          }}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CrawlerPoolEditPage;
