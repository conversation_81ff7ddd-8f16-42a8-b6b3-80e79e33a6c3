"""
新架构：任务分配数据模型
TaskAssignment = Task + CrawlerWorker集合 (带互斥检查)
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Set
from pydantic import BaseModel, Field, validator
from enum import Enum

from .crawler_worker import CrawlerWorker, CrawlerWorkerSummary, WorkerGroupCompatibilityCheck


class AssignmentStatus(str, Enum):
    """分配状态枚举"""
    PENDING = "pending"         # 待分配
    ASSIGNED = "assigned"       # 已分配
    RUNNING = "running"         # 运行中
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"          # 失败
    CANCELLED = "cancelled"     # 已取消


class AssignmentStrategy(str, Enum):
    """分配策略枚举"""
    ROUND_ROBIN = "round_robin"                 # 轮询
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin" # 加权轮询
    LEAST_CONNECTIONS = "least_connections"      # 最少连接
    HEALTH_BASED = "health_based"               # 基于健康评分
    PRIORITY_BASED = "priority_based"           # 基于优先级
    PERFORMANCE_BASED = "performance_based"     # 基于性能


class TaskAssignment(BaseModel):
    """任务分配"""
    
    # 基本信息
    assignment_id: str = Field(description="分配唯一标识")
    task_id: str = Field(description="任务ID")
    assignment_name: str = Field(description="分配名称", max_length=100)
    description: Optional[str] = Field(None, description="分配描述", max_length=500)
    
    # Worker分配
    worker_ids: List[str] = Field(description="分配的Worker ID列表")
    primary_worker_id: Optional[str] = Field(None, description="主要Worker ID")
    backup_worker_ids: List[str] = Field(default_factory=list, description="备用Worker ID列表")
    
    # 分配策略
    assignment_strategy: AssignmentStrategy = Field(
        default=AssignmentStrategy.HEALTH_BASED, 
        description="分配策略"
    )
    auto_failover: bool = Field(default=True, description="是否自动故障转移")
    max_retries: int = Field(default=3, ge=0, le=10, description="最大重试次数")
    
    # 状态信息
    status: AssignmentStatus = Field(default=AssignmentStatus.PENDING, description="分配状态")
    current_worker_id: Optional[str] = Field(None, description="当前执行Worker ID")
    
    # 执行统计
    total_urls: int = Field(default=0, ge=0, description="总URL数")
    processed_urls: int = Field(default=0, ge=0, description="已处理URL数")
    successful_urls: int = Field(default=0, ge=0, description="成功URL数")
    failed_urls: int = Field(default=0, ge=0, description="失败URL数")
    
    # 性能统计
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    estimated_duration: Optional[int] = Field(None, description="预估时长(秒)")
    actual_duration: Optional[int] = Field(None, description="实际时长(秒)")
    
    # 错误信息
    error_count: int = Field(default=0, ge=0, description="错误次数")
    last_error: Optional[str] = Field(None, description="最后错误信息")
    retry_count: int = Field(default=0, ge=0, description="重试次数")
    
    # 兼容性检查结果
    compatibility_check: Optional[WorkerGroupCompatibilityCheck] = Field(
        None, description="兼容性检查结果"
    )
    
    # 时间戳
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    assigned_at: Optional[datetime] = Field(None, description="分配时间")
    created_by: Optional[str] = Field(None, description="创建者")
    
    @validator('worker_ids')
    def validate_worker_ids(cls, v):
        """验证Worker ID列表"""
        if not v:
            raise ValueError("至少需要分配一个Worker")
        
        # 检查ID唯一性
        if len(v) != len(set(v)):
            raise ValueError("Worker ID必须唯一")
        
        return v
    
    @validator('primary_worker_id')
    def validate_primary_worker_id(cls, v, values):
        """验证主要Worker ID"""
        if v and 'worker_ids' in values and v not in values['worker_ids']:
            raise ValueError("主要Worker ID必须在Worker列表中")
        return v
    
    @validator('backup_worker_ids')
    def validate_backup_worker_ids(cls, v, values):
        """验证备用Worker ID列表"""
        if v and 'worker_ids' in values:
            for backup_id in v:
                if backup_id not in values['worker_ids']:
                    raise ValueError(f"备用Worker ID {backup_id} 必须在Worker列表中")
        return v
    
    @validator('successful_urls')
    def validate_successful_urls(cls, v, values):
        """验证成功URL数"""
        if 'processed_urls' in values and v > values['processed_urls']:
            raise ValueError("成功URL数不能超过已处理URL数")
        return v
    
    @property
    def success_rate(self) -> float:
        """计算成功率"""
        if self.processed_urls == 0:
            return 0.0
        return self.successful_urls / self.processed_urls
    
    @property
    def progress_rate(self) -> float:
        """计算进度"""
        if self.total_urls == 0:
            return 0.0
        return self.processed_urls / self.total_urls
    
    @property
    def is_completed(self) -> bool:
        """判断是否完成"""
        return self.status in [AssignmentStatus.COMPLETED, AssignmentStatus.FAILED, AssignmentStatus.CANCELLED]
    
    @property
    def is_running(self) -> bool:
        """判断是否运行中"""
        return self.status == AssignmentStatus.RUNNING
    
    @property
    def remaining_urls(self) -> int:
        """获取剩余URL数"""
        return max(0, self.total_urls - self.processed_urls)
    
    def get_estimated_remaining_time(self) -> Optional[int]:
        """估算剩余时间(秒)"""
        if not self.is_running or self.processed_urls == 0:
            return None
        
        if self.start_time:
            elapsed = (datetime.now() - self.start_time).total_seconds()
            rate = self.processed_urls / elapsed
            if rate > 0:
                return int(self.remaining_urls / rate)
        
        return None


class TaskAssignmentCreate(BaseModel):
    """创建任务分配的数据模型"""
    
    task_id: str = Field(description="任务ID")
    assignment_name: str = Field(description="分配名称", max_length=100)
    description: Optional[str] = Field(None, description="分配描述", max_length=500)
    
    # Worker分配
    worker_ids: List[str] = Field(description="Worker ID列表")
    primary_worker_id: Optional[str] = None
    backup_worker_ids: List[str] = Field(default_factory=list)
    
    # 分配策略
    assignment_strategy: AssignmentStrategy = Field(default=AssignmentStrategy.HEALTH_BASED)
    auto_failover: bool = Field(default=True)
    max_retries: int = Field(default=3, ge=0, le=10)
    
    # 任务信息
    total_urls: int = Field(default=0, ge=0)
    estimated_duration: Optional[int] = None
    
    # 创建者
    created_by: Optional[str] = None


class TaskAssignmentUpdate(BaseModel):
    """更新任务分配的数据模型"""
    
    assignment_name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    
    # 状态更新
    status: Optional[AssignmentStatus] = None
    current_worker_id: Optional[str] = None
    
    # 进度更新
    processed_urls: Optional[int] = Field(None, ge=0)
    successful_urls: Optional[int] = Field(None, ge=0)
    failed_urls: Optional[int] = Field(None, ge=0)
    
    # 时间更新
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    actual_duration: Optional[int] = None
    
    # 错误信息更新
    error_count: Optional[int] = Field(None, ge=0)
    last_error: Optional[str] = None
    retry_count: Optional[int] = Field(None, ge=0)


class TaskAssignmentSummary(BaseModel):
    """任务分配摘要"""
    
    assignment_id: str
    task_id: str
    assignment_name: str
    status: AssignmentStatus
    assignment_strategy: AssignmentStrategy
    
    # Worker信息
    worker_count: int
    primary_worker_name: Optional[str]
    current_worker_name: Optional[str]
    
    # 进度信息
    total_urls: int
    processed_urls: int
    progress_rate: float
    success_rate: float
    
    # 时间信息
    created_at: Optional[datetime]
    start_time: Optional[datetime]
    estimated_remaining_time: Optional[int]
    
    @property
    def is_active(self) -> bool:
        """判断是否活跃"""
        return self.status in [AssignmentStatus.ASSIGNED, AssignmentStatus.RUNNING]


class AssignmentRecommendation(BaseModel):
    """分配推荐"""
    
    task_id: str = Field(description="任务ID")
    recommended_workers: List[str] = Field(description="推荐的Worker ID列表")
    recommended_strategy: AssignmentStrategy = Field(description="推荐的分配策略")
    
    # 推荐理由
    reasons: List[str] = Field(description="推荐理由")
    performance_score: float = Field(description="性能评分")
    reliability_score: float = Field(description="可靠性评分")
    
    # 替代方案
    alternative_workers: List[str] = Field(description="替代Worker列表")
    fallback_strategy: AssignmentStrategy = Field(description="备用策略")
    
    # 风险评估
    risks: List[str] = Field(description="风险列表")
    mitigation_suggestions: List[str] = Field(description="风险缓解建议")


class TaskAssignmentStats(BaseModel):
    """任务分配统计信息"""
    
    total_assignments: int = Field(description="总分配数")
    active_assignments: int = Field(description="活跃分配数")
    completed_assignments: int = Field(description="完成分配数")
    failed_assignments: int = Field(description="失败分配数")
    
    # 成功率统计
    overall_success_rate: float = Field(description="总体成功率")
    avg_completion_time: float = Field(description="平均完成时间")
    
    # 策略统计
    strategy_distribution: Dict[str, int] = Field(description="策略分布")
    strategy_success_rates: Dict[str, float] = Field(description="策略成功率")
    
    # Worker使用统计
    worker_usage: Dict[str, int] = Field(description="Worker使用统计")
    most_used_workers: List[str] = Field(description="最常用Worker")
    best_performing_workers: List[str] = Field(description="表现最佳Worker")
    
    # 最近活动
    recent_assignments: List[TaskAssignmentSummary] = Field(description="最近分配")
    recent_completions: List[TaskAssignmentSummary] = Field(description="最近完成")
    recent_failures: List[TaskAssignmentSummary] = Field(description="最近失败")


class ConflictDetectionResult(BaseModel):
    """冲突检测结果"""
    
    has_conflicts: bool = Field(description="是否有冲突")
    conflict_groups: List[List[str]] = Field(description="冲突组（每组内的Worker互相冲突）")
    
    # 详细冲突信息
    resource_conflicts: Dict[str, List[str]] = Field(description="资源冲突详情")
    capacity_conflicts: List[str] = Field(description="容量冲突Worker列表")
    
    # 解决建议
    resolution_suggestions: List[str] = Field(description="解决建议")
    alternative_workers: List[str] = Field(description="替代Worker建议")
    
    @property
    def conflict_count(self) -> int:
        """冲突数量"""
        return len(self.conflict_groups)
    
    @property
    def conflicted_workers(self) -> Set[str]:
        """所有冲突的Worker"""
        workers = set()
        for group in self.conflict_groups:
            workers.update(group)
        return workers
