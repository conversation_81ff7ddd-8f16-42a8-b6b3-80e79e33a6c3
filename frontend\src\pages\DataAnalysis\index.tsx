import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Card,
  Table,
  Button,
  Select,
  DatePicker,
  Input,
  Space,
  Row,
  Col,
  Statistic,
  Tag,
  Rate,
  Image,
  Tooltip,
  Divider,
  Tabs,
  Switch,
  InputNumber,
  message
} from 'antd';
import {
  SearchOutlined,
  DownloadOutlined,
  ReloadOutlined,
  FilterOutlined,
  EyeOutlined,
  RiseOutlined,
  FallOutlined,
  ShoppingCartOutlined,
  StarOutlined,
  DollarOutlined,
  Bar<PERSON><PERSON>Outlined,
  LineChartOutlined,
  PieChartOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import type { TableColumnsType } from 'antd';
import dayjs from 'dayjs';
import './index.css';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

// 类型定义
interface ProductData {
  id: string;
  title: string;
  url: string;
  imageUrl: string;
  currentPrice: number;
  originalPrice: number;
  discount: number;
  rating: number;
  reviewCount: number;
  salesCount: number;
  stock: number;
  stockStatus: 'in-stock' | 'low-stock' | 'out-of-stock';
  platform: string;
  category: string;
  lastUpdated: string;
  priceChange: number;
  priceChangePercent: number;
}

interface ChartData {
  date: string;
  price: number;
  sales: number;
  rating: number;
}

const DataAnalysis: React.FC = () => {
  // 状态管理
  const [activeTab, setActiveTab] = useState('overview');
  const [productData, setProductData] = useState<ProductData[]>([]);
  const [filteredData, setFilteredData] = useState<ProductData[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<string>('');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [platformFilter, setPlatformFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);

  // 数据将从API获取，不再使用mock数据
  const [chartData, setChartData] = useState<ChartData[]>([]);

  // 初始化数据 - 从API获取
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // TODO: 调用真实API获取商品数据
        // const response = await dataApi.getProductSnapshots();
        // setProductData(response.data);
        // setFilteredData(response.data);

        // TODO: 调用真实API获取图表数据
        // const chartResponse = await dataApi.getProductTrends();
        // setChartData(chartResponse.data);

        // 暂时设置为空数组，等待API实现
        setProductData([]);
        setFilteredData([]);
        setChartData([]);
      } catch (error) {
        console.error('获取商品数据失败:', error);
        setProductData([]);
        setFilteredData([]);
        setChartData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // 数据筛选
  useEffect(() => {
    let filtered = productData.filter(product => {
      const matchesPlatform = platformFilter === 'all' || product.platform === platformFilter;
      const matchesCategory = categoryFilter === 'all' || product.category === categoryFilter;
      const matchesSearch = product.title.toLowerCase().includes(searchKeyword.toLowerCase());
      const matchesPrice = product.currentPrice >= priceRange[0] && product.currentPrice <= priceRange[1];

      return matchesPlatform && matchesCategory && matchesSearch && matchesPrice;
    });

    setFilteredData(filtered);
  }, [productData, platformFilter, categoryFilter, searchKeyword, priceRange]);

  // 统计数据计算
  const totalProducts = filteredData.length;
  const averagePrice = filteredData.length > 0
    ? filteredData.reduce((sum, product) => sum + product.currentPrice, 0) / filteredData.length
    : 0;
  const totalSales = filteredData.reduce((sum, product) => sum + product.salesCount, 0);
  const averageRating = filteredData.length > 0
    ? filteredData.reduce((sum, product) => sum + product.rating, 0) / filteredData.length
    : 0;

  // Tabs配置
  const tabItems = [
    {
      key: 'overview',
      label: (
        <span>
          <BarChartOutlined />
          数据表格
        </span>
      ),
      children: (
        <div className="data-table-section">
          <Card title="商品数据列表" className="data-table-card">
            <div className="table-header">
              <div>
                <Text>共找到 {filteredData.length} 个商品</Text>
              </div>
              <div className="table-actions">
                <Button icon={<ReloadOutlined />} onClick={() => message.info('刷新功能开发中')}>
                  刷新
                </Button>
                <Button icon={<DownloadOutlined />} onClick={() => message.info('导出功能开发中')}>
                  导出
                </Button>
              </div>
            </div>

            <Table
              dataSource={filteredData}
              rowKey="id"
              loading={loading}
              scroll={{ x: 1200 }}
              columns={[
                {
                  title: '商品信息',
                  key: 'product',
                  width: 300,
                  fixed: 'left',
                  render: (_, record: ProductData) => (
                    <div className="product-info">
                      <Image
                        src={record.imageUrl}
                        alt={record.title}
                        width={50}
                        height={50}
                        className="product-image"
                        fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                      />
                      <div className="product-details">
                        <div className="product-title">{record.title}</div>
                        <a
                          href={record.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="product-url"
                        >
                          查看商品页面
                        </a>
                      </div>
                    </div>
                  )
                },
                {
                  title: '平台',
                  dataIndex: 'platform',
                  key: 'platform',
                  width: 120,
                  render: (platform: string) => (
                    <Tag color="blue">{platform}</Tag>
                  )
                },
                {
                  title: '分类',
                  dataIndex: 'category',
                  key: 'category',
                  width: 100
                },
                {
                  title: '当前价格',
                  key: 'price',
                  width: 150,
                  render: (_, record: ProductData) => (
                    <div className="price-display">
                      <div className="current-price">€{record.currentPrice}</div>
                      {record.originalPrice !== record.currentPrice && (
                        <div className="original-price">€{record.originalPrice}</div>
                      )}
                      {record.priceChange !== 0 && (
                        <div className={`price-change ${record.priceChange > 0 ? 'positive' : 'negative'}`}>
                          {record.priceChange > 0 ? '+' : ''}€{record.priceChange.toFixed(2)}
                          ({record.priceChangePercent > 0 ? '+' : ''}{record.priceChangePercent.toFixed(1)}%)
                        </div>
                      )}
                    </div>
                  )
                },
                {
                  title: '折扣',
                  dataIndex: 'discount',
                  key: 'discount',
                  width: 80,
                  render: (discount: number) => (
                    discount > 0 ? (
                      <Tag color="red">-{discount}%</Tag>
                    ) : (
                      <span>-</span>
                    )
                  )
                },
                {
                  title: '评分',
                  key: 'rating',
                  width: 150,
                  render: (_, record: ProductData) => (
                    <div className="rating-display">
                      <Rate disabled value={record.rating} allowHalf />
                      <div className="rating-count">({record.reviewCount})</div>
                    </div>
                  )
                },
                {
                  title: '销量',
                  dataIndex: 'salesCount',
                  key: 'salesCount',
                  width: 100,
                  render: (sales: number) => sales.toLocaleString()
                },
                {
                  title: '库存状态',
                  key: 'stock',
                  width: 120,
                  render: (_, record: ProductData) => (
                    <div className="stock-status">
                      <div className={`stock-indicator ${record.stockStatus}`}></div>
                      <span>
                        {record.stockStatus === 'in-stock' && '有库存'}
                        {record.stockStatus === 'low-stock' && '库存不足'}
                        {record.stockStatus === 'out-of-stock' && '缺货'}
                      </span>
                      <div style={{ fontSize: 12, color: '#666', marginTop: 2 }}>
                        {record.stock > 0 ? `剩余 ${record.stock}` : '无库存'}
                      </div>
                    </div>
                  )
                },
                {
                  title: '最后更新',
                  dataIndex: 'lastUpdated',
                  key: 'lastUpdated',
                  width: 160
                },
                {
                  title: '操作',
                  key: 'actions',
                  width: 120,
                  fixed: 'right',
                  render: (_, record: ProductData) => (
                    <Space>
                      <Button
                        icon={<EyeOutlined />}
                        size="small"
                        onClick={() => {
                          setSelectedProduct(record.id);
                          setActiveTab('charts');
                        }}
                      >
                        查看
                      </Button>
                    </Space>
                  )
                }
              ]}
              pagination={{
                total: filteredData.length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }}
            />
          </Card>
        </div>
      )
    },
    {
      key: 'charts',
      label: (
        <span>
          <LineChartOutlined />
          图表分析
        </span>
      ),
      children: (
        <div className="charts-section">
          <div className="charts-grid">
            {/* 价格趋势图 */}
            <Card title="价格趋势分析" className="chart-card">
              <ReactECharts
                option={{
                  title: {
                    text: '商品价格变化趋势',
                    left: 'center',
                    textStyle: { fontSize: 16, fontWeight: 600 }
                  },
                  tooltip: {
                    trigger: 'axis',
                    formatter: (params: any) => {
                      const data = params[0];
                      return `${data.name}<br/>价格: €${data.value}`;
                    }
                  },
                  xAxis: {
                    type: 'category',
                    data: chartData.map(item => item.date),
                    axisLabel: {
                      formatter: (value: string) => dayjs(value).format('MM-DD')
                    }
                  },
                  yAxis: {
                    type: 'value',
                    name: '价格 (€)',
                    axisLabel: {
                      formatter: '€{value}'
                    }
                  },
                  series: [{
                    name: '价格',
                    type: 'line',
                    data: chartData.map(item => item.price),
                    smooth: true,
                    lineStyle: {
                      color: '#1890ff',
                      width: 3
                    },
                    areaStyle: {
                      color: {
                        type: 'linear',
                        x: 0, y: 0, x2: 0, y2: 1,
                        colorStops: [
                          { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                          { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
                        ]
                      }
                    }
                  }],
                  grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                  }
                }}
                style={{ height: '400px', width: '100%' }}
              />
            </Card>

            {/* 销量统计图 */}
            <Card title="销量统计分析" className="chart-card">
              <ReactECharts
                option={{
                  title: {
                    text: '每日销量变化',
                    left: 'center',
                    textStyle: { fontSize: 16, fontWeight: 600 }
                  },
                  tooltip: {
                    trigger: 'axis',
                    formatter: (params: any) => {
                      const data = params[0];
                      return `${data.name}<br/>销量: ${data.value}`;
                    }
                  },
                  xAxis: {
                    type: 'category',
                    data: chartData.map(item => item.date),
                    axisLabel: {
                      formatter: (value: string) => dayjs(value).format('MM-DD')
                    }
                  },
                  yAxis: {
                    type: 'value',
                    name: '销量'
                  },
                  series: [{
                    name: '销量',
                    type: 'bar',
                    data: chartData.map(item => item.sales),
                    itemStyle: {
                      color: {
                        type: 'linear',
                        x: 0, y: 0, x2: 0, y2: 1,
                        colorStops: [
                          { offset: 0, color: '#faad14' },
                          { offset: 1, color: '#ffc53d' }
                        ]
                      }
                    }
                  }],
                  grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                  }
                }}
                style={{ height: '400px', width: '100%' }}
              />
            </Card>

            {/* 评分趋势图 */}
            <Card title="评分趋势分析" className="chart-card">
              <ReactECharts
                option={{
                  title: {
                    text: '商品评分变化趋势',
                    left: 'center',
                    textStyle: { fontSize: 16, fontWeight: 600 }
                  },
                  tooltip: {
                    trigger: 'axis',
                    formatter: (params: any) => {
                      const data = params[0];
                      return `${data.name}<br/>评分: ${data.value}/5.0`;
                    }
                  },
                  xAxis: {
                    type: 'category',
                    data: chartData.map(item => item.date),
                    axisLabel: {
                      formatter: (value: string) => dayjs(value).format('MM-DD')
                    }
                  },
                  yAxis: {
                    type: 'value',
                    name: '评分',
                    min: 0,
                    max: 5,
                    axisLabel: {
                      formatter: '{value}/5.0'
                    }
                  },
                  series: [{
                    name: '评分',
                    type: 'line',
                    data: chartData.map(item => item.rating),
                    smooth: true,
                    lineStyle: {
                      color: '#52c41a',
                      width: 3
                    },
                    itemStyle: {
                      color: '#52c41a'
                    }
                  }],
                  grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                  }
                }}
                style={{ height: '400px', width: '100%' }}
              />
            </Card>

            {/* 平台分布饼图 */}
            <Card title="平台分布统计" className="chart-card">
              <ReactECharts
                option={{
                  title: {
                    text: '商品平台分布',
                    left: 'center',
                    textStyle: { fontSize: 16, fontWeight: 600 }
                  },
                  tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                  },
                  legend: {
                    orient: 'vertical',
                    left: 'left'
                  },
                  series: [{
                    name: '平台分布',
                    type: 'pie',
                    radius: '50%',
                    data: (() => {
                      const platformCounts = filteredData.reduce((acc, product) => {
                        acc[product.platform] = (acc[product.platform] || 0) + 1;
                        return acc;
                      }, {} as Record<string, number>);

                      return Object.entries(platformCounts).map(([name, value]) => ({
                        name,
                        value
                      }));
                    })(),
                    emphasis: {
                      itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                      }
                    }
                  }]
                }}
                style={{ height: '400px', width: '100%' }}
              />
            </Card>
          </div>
        </div>
      )
    }
  ];

  return (
    <div className="data-analysis">
      {/* 页面头部 */}
      <div className="data-analysis-header">
        <Title level={2}>数据分析</Title>
        <Paragraph>
          分析电商商品数据，包括价格趋势、销量统计、评分对比、库存监控等多维度数据可视化
        </Paragraph>
      </div>

      {/* 数据概览 */}
      <div className="data-overview">
        <div className="overview-cards">
          <Card className="overview-card products">
            <Statistic
              title="监控商品数"
              value={totalProducts}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>

          <Card className="overview-card price">
            <Statistic
              title="平均价格"
              value={averagePrice}
              precision={2}
              prefix={<DollarOutlined />}
              suffix="€"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>

          <Card className="overview-card sales">
            <Statistic
              title="总销量"
              value={totalSales}
              prefix={<RiseOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>

          <Card className="overview-card rating">
            <Statistic
              title="平均评分"
              value={averageRating}
              precision={1}
              prefix={<StarOutlined />}
              suffix="/5.0"
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </div>
      </div>

      {/* 筛选控制 */}
      <div className="data-filters">
        <div className="filter-row">
          <div className="filter-item">
            <div className="filter-label">平台筛选</div>
            <Select
              value={platformFilter}
              onChange={setPlatformFilter}
              style={{ width: 150 }}
            >
              <Option value="all">全部平台</Option>
              <Option value="Amazon ES">Amazon ES</Option>
              <Option value="eBay ES">eBay ES</Option>
              <Option value="AliExpress">AliExpress</Option>
            </Select>
          </div>

          <div className="filter-item">
            <div className="filter-label">商品分类</div>
            <Select
              value={categoryFilter}
              onChange={setCategoryFilter}
              style={{ width: 150 }}
            >
              <Option value="all">全部分类</Option>
              <Option value="电子产品">电子产品</Option>
              <Option value="家居用品">家居用品</Option>
              <Option value="服装配饰">服装配饰</Option>
              <Option value="运动户外">运动户外</Option>
            </Select>
          </div>

          <div className="filter-item">
            <div className="filter-label">价格范围 (€)</div>
            <Space>
              <InputNumber
                min={0}
                max={10000}
                value={priceRange[0]}
                onChange={(value) => setPriceRange([value || 0, priceRange[1]])}
                style={{ width: 80 }}
              />
              <span>-</span>
              <InputNumber
                min={0}
                max={10000}
                value={priceRange[1]}
                onChange={(value) => setPriceRange([priceRange[0], value || 1000])}
                style={{ width: 80 }}
              />
            </Space>
          </div>

          <div className="filter-item">
            <div className="filter-label">时间范围</div>
            <RangePicker
              value={dateRange}
              onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null)}
              style={{ width: 240 }}
            />
          </div>

          <div className="filter-item">
            <div className="filter-label">搜索商品</div>
            <Search
              placeholder="搜索商品名称"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              style={{ width: 200 }}
              allowClear
            />
          </div>

          <div className="filter-item">
            <div className="filter-label">操作</div>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => {
                  setPlatformFilter('all');
                  setCategoryFilter('all');
                  setSearchKeyword('');
                  setPriceRange([0, 1000]);
                  setDateRange(null);
                  message.success('筛选条件已重置');
                }}
              >
                重置
              </Button>
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={() => message.info('导出功能开发中')}
              >
                导出数据
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        size="large"
        items={tabItems}
      />
    </div>
  );
};

export default DataAnalysis;
