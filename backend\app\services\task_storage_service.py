"""
统一的任务存储服务

只使用Redis作为持久化存储，移除内存存储的复杂性
"""

import json
import redis.asyncio as redis
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class TaskStorageService:
    """统一的任务存储服务 - 分层Redis存储"""

    def __init__(self, redis_url: str = None):
        """初始化Redis连接"""
        if redis_url is None:
            redis_url = self._detect_redis_url()
        self.redis_url = redis_url
        self._redis_client = None

        # 新的分层存储结构
        self.TASK_PREFIX = "monitoring_tasks:tasks"
        self.INDEX_PREFIX = "monitoring_tasks:indexes"
        self.URL_TASK_PREFIX = "monitoring_tasks:url_tasks"  # URL反向关联

    def _serialize_value(self, value):
        """正确序列化值到Redis"""
        if value is None:
            return ""  # 空字符串表示None
        elif isinstance(value, bool):
            return "true" if value else "false"  # 布尔值转换为小写字符串
        elif isinstance(value, (int, float)):
            return str(value)  # 数字转换为字符串
        elif isinstance(value, (list, dict)):
            import json
            return json.dumps(value)  # 复杂类型转换为JSON
        else:
            return str(value)  # 其他类型转换为字符串

    def _deserialize_tags(self, value):
        """专门处理tags字段的反序列化"""
        if not value or value == "":
            return []  # 空值返回空数组

        # 如果已经是列表，直接返回
        if isinstance(value, list):
            return value

        # 尝试JSON解析
        try:
            import json
            result = json.loads(value)
            if isinstance(result, list):
                return result
            else:
                # 如果解析结果不是列表，包装成列表
                return [result] if result is not None else []
        except (json.JSONDecodeError, ValueError):
            # JSON解析失败，尝试其他方法
            pass

        # 处理Python字符串表示的列表（如 "['tag1', 'tag2']"）
        if value.startswith('[') and value.endswith(']'):
            try:
                import ast
                result = ast.literal_eval(value)
                if isinstance(result, list):
                    return result
            except (ValueError, SyntaxError):
                pass

        # 如果是单个字符串，包装成列表
        return [value] if value else []

    def _deserialize_value(self, value, field_name=None):
        """正确反序列化Redis中的值"""
        if value == "":
            return None  # 空字符串表示None
        elif value in ("true", "false"):
            return value == "true"  # 布尔值
        elif field_name and field_name in ['total_runs', 'success_runs', 'failed_runs', 'url_count', 'max_runs', 'retry_count', 'timeout', 'concurrent_limit', 'batch_size', 'random_delay_min', 'random_delay_max']:
            try:
                return int(value)  # 整数字段
            except ValueError:
                return value
        elif field_name and field_name in ['avg_duration', 'success_rate']:
            try:
                return float(value)  # 浮点数字段
            except ValueError:
                return value
        elif value.startswith(('[', '{')):
            try:
                import json
                return json.loads(value)  # JSON数据
            except (json.JSONDecodeError, ValueError):
                return value
        else:
            return value  # 字符串

    def _detect_redis_url(self) -> str:
        """自动检测Redis连接地址"""
        import os

        # 检查环境变量
        if os.getenv('REDIS_URL'):
            return os.getenv('REDIS_URL')

        # 检查是否在Docker环境中
        if os.path.exists('/.dockerenv'):
            return "redis://redis:6379/0"

        # 默认本地环境
        return "redis://localhost:6379/0"
    
    async def get_redis_client(self):
        """获取Redis客户端"""
        if self._redis_client is None:
            self._redis_client = redis.from_url(self.redis_url)
        return self._redis_client
    
    async def close(self):
        """关闭Redis连接"""
        if self._redis_client:
            await self._redis_client.close()
            self._redis_client = None
    
    async def get_task(self, task_id: str) -> Optional[Dict]:
        """获取任务详情 - 从分层结构读取"""
        try:
            redis_client = await self.get_redis_client()

            # 从各个层读取数据
            basic_key = f"{self.TASK_PREFIX}:{task_id}:basic"
            config_key = f"{self.TASK_PREFIX}:{task_id}:config"
            schedule_key = f"{self.TASK_PREFIX}:{task_id}:schedule"
            stats_key = f"{self.TASK_PREFIX}:{task_id}:stats"

            # 并行获取所有层的数据
            basic_data = await redis_client.hgetall(basic_key)
            config_data = await redis_client.hgetall(config_key)
            schedule_data = await redis_client.hgetall(schedule_key)
            stats_data = await redis_client.hgetall(stats_key)

            # 如果基本信息不存在，说明任务不存在
            if not basic_data:
                return None

            # 合并所有层的数据
            result = {}

            # 处理基本信息
            for field, value in basic_data.items():
                field_str = field.decode() if isinstance(field, bytes) else field
                value_str = value.decode() if isinstance(value, bytes) else value
                # tags字段需要特殊处理
                if field_str == 'tags':
                    result[field_str] = self._deserialize_tags(value_str)
                else:
                    result[field_str] = value_str

            # 处理配置信息 - 重新组装为JSON
            if config_data:
                config_obj = {}
                for field, value in config_data.items():
                    field_str = field.decode() if isinstance(field, bytes) else field
                    value_str = value.decode() if isinstance(value, bytes) else value
                    config_obj[field_str] = value_str

                if config_obj:
                    # 清理和验证配置数据
                    config_obj = self._clean_config_data(config_obj)
                    result['config'] = config_obj

            # 处理调度信息 - 重新组装为JSON
            if schedule_data:
                schedule_obj = {}
                for field, value in schedule_data.items():
                    field_str = field.decode() if isinstance(field, bytes) else field
                    value_str = value.decode() if isinstance(value, bytes) else value
                    if field_str in ['next_run', 'last_run']:
                        result[field_str] = value_str
                    else:
                        schedule_obj[field_str] = value_str

                if schedule_obj:
                    # 清理和验证调度数据
                    schedule_obj = self._clean_schedule_data(schedule_obj)
                    result['schedule'] = schedule_obj

            # 处理统计信息
            for field, value in stats_data.items():
                field_str = field.decode() if isinstance(field, bytes) else field
                value_str = value.decode() if isinstance(value, bytes) else value
                # 转换布尔值
                if field_str == 'is_running':
                    result[field_str] = value_str.lower() == 'true'
                else:
                    result[field_str] = value_str

            # 处理日期时间字段 - 确保空字符串转换为None
            for datetime_field in ['last_run', 'next_run', 'created_at', 'updated_at']:
                if datetime_field in result:
                    value = result[datetime_field]
                    if value == '' or value is None:
                        result[datetime_field] = None

            # 获取任务关联的URL（统一格式）
            task_urls_key = f"{self.TASK_PREFIX}:{task_id}:urls"
            url_ids = await redis_client.smembers(task_urls_key)
            result['urls'] = list(url_ids) if url_ids else []
            result['total_urls'] = len(result['urls'])

            logger.debug(f"Retrieved task {task_id} from layered Redis structure")
            return result

        except Exception as e:
            logger.error(f"Failed to get task {task_id}: {e}")
            return None
    
    async def save_task(self, task_id: str, task_data: Dict) -> bool:
        """保存任务 - 分层存储"""
        try:
            redis_client = await self.get_redis_client()

            # 分类字段
            basic_fields = {}
            config_fields = {}
            schedule_fields = {}
            stats_fields = {}

            for field, value in task_data.items():
                if field in ['id', 'name', 'description', 'created_at', 'updated_at']:
                    basic_fields[field] = str(value)
                elif field == 'config' and isinstance(value, dict):
                    # 将config字典的值正确转换
                    for k, v in value.items():
                        config_fields[k] = self._serialize_value(v)
                elif field == 'schedule' and isinstance(value, dict):
                    # 将schedule字典的值正确转换
                    for k, v in value.items():
                        schedule_fields[k] = self._serialize_value(v)
                elif field in ['next_run', 'last_run']:
                    schedule_fields[field] = str(value)
                elif field == 'tags':
                    # tags字段需要正确序列化为JSON
                    basic_fields[field] = self._serialize_value(value)
                else:
                    # 其他字段归类为统计信息
                    if field == 'is_running':
                        stats_fields[field] = str(value).lower()
                    else:
                        stats_fields[field] = str(value)

            # 存储到各个层
            if basic_fields:
                basic_key = f"{self.TASK_PREFIX}:{task_id}:basic"
                await redis_client.hset(basic_key, mapping=basic_fields)

            if config_fields:
                config_key = f"{self.TASK_PREFIX}:{task_id}:config"
                await redis_client.hset(config_key, mapping=config_fields)

            if schedule_fields:
                schedule_key = f"{self.TASK_PREFIX}:{task_id}:schedule"
                await redis_client.hset(schedule_key, mapping=schedule_fields)

            if stats_fields:
                stats_key = f"{self.TASK_PREFIX}:{task_id}:stats"
                await redis_client.hset(stats_key, mapping=stats_fields)

            # 更新索引
            await self._update_task_indexes(redis_client, task_id, task_data)

            logger.debug(f"Saved task {task_id} to layered Redis structure")
            return True

        except Exception as e:
            logger.error(f"Failed to save task {task_id}: {e}")
            return False

    async def _update_task_indexes(self, redis_client, task_id: str, task_data: Dict):
        """更新任务索引"""
        # 添加到总索引 - 使用实际的集合而不是元数据键
        await redis_client.sadd(f'{self.INDEX_PREFIX}:all_task_ids', task_id)

        # 状态索引
        status = task_data.get('status', 'draft')
        await redis_client.sadd(f'{self.INDEX_PREFIX}:by_status:{status}', task_id)

        # 活跃任务索引
        if status == 'active':
            await redis_client.sadd(f'{self.INDEX_PREFIX}:active_tasks', task_id)

        # 平台索引
        config = task_data.get('config', {})
        if isinstance(config, dict):
            platform = config.get('platform')
        else:
            platform = task_data.get('platform')

        if platform:
            await redis_client.sadd(f'{self.INDEX_PREFIX}:by_platform:{platform}', task_id)

    async def _get_filtered_task_ids(self, redis_client, filters: Dict = None):
        """根据过滤条件获取任务ID - 使用索引优化"""
        if not filters:
            # 没有过滤条件，返回所有任务
            return await redis_client.smembers(f'{self.INDEX_PREFIX}:all_task_ids')

        # 优先使用索引
        if 'status' in filters and filters['status']:
            status = filters['status']
            task_ids = await redis_client.smembers(f'{self.INDEX_PREFIX}:by_status:{status}')
        elif 'platform' in filters and filters['platform']:
            platform = filters['platform']
            task_ids = await redis_client.smembers(f'{self.INDEX_PREFIX}:by_platform:{platform}')
        else:
            # 使用全量索引
            task_ids = await redis_client.smembers(f'{self.INDEX_PREFIX}:all_task_ids')

        return task_ids

    def _clean_schedule_data(self, schedule_obj: Dict) -> Dict:
        """清理和验证调度数据"""
        cleaned = schedule_obj.copy()

        # 处理调度类型
        if 'type' in cleaned:
            schedule_type = cleaned['type']
            # 处理枚举字符串格式 (如 'ScheduleType.DAILY' -> 'daily')
            if 'ScheduleType.' in schedule_type:
                schedule_type = schedule_type.split('.')[-1].lower()
            # 将manual映射为custom以保持兼容性
            elif schedule_type == 'manual':
                schedule_type = 'custom'
            cleaned['type'] = schedule_type

        # 处理布尔值
        if 'enabled' in cleaned:
            enabled_value = cleaned['enabled']
            if isinstance(enabled_value, str):
                if enabled_value.lower() in ['true', '1', 'yes']:
                    cleaned['enabled'] = True
                elif enabled_value.lower() in ['false', '0', 'no']:
                    cleaned['enabled'] = False
                else:
                    cleaned['enabled'] = True  # 默认启用
            else:
                cleaned['enabled'] = bool(enabled_value)

        # 处理可选的整数字段
        for int_field in ['interval', 'max_runs', 'random_delay_min', 'random_delay_max']:
            if int_field in cleaned:
                value = cleaned[int_field]
                if isinstance(value, str):
                    if value.lower() in ['none', 'null', '']:
                        cleaned[int_field] = None
                    else:
                        try:
                            cleaned[int_field] = int(value)
                        except ValueError:
                            cleaned[int_field] = None

        # 处理可选的字符串字段（将"None"字符串转换为None）
        for str_field in ['time', 'start_time', 'end_time', 'cron_expression']:
            if str_field in cleaned:
                value = cleaned[str_field]
                if isinstance(value, str) and value.lower() in ['none', 'null', '']:
                    cleaned[str_field] = None

        # 处理随机延迟布尔字段
        if 'enable_random_delay' in cleaned:
            value = cleaned['enable_random_delay']
            if isinstance(value, str):
                if value.lower() in ['true', '1', 'yes']:
                    cleaned['enable_random_delay'] = True
                elif value.lower() in ['false', '0', 'no']:
                    cleaned['enable_random_delay'] = False
                else:
                    cleaned['enable_random_delay'] = False  # 默认不启用

        # 处理可选的列表字段
        for list_field in ['days']:
            if list_field in cleaned:
                value = cleaned[list_field]
                if isinstance(value, str):
                    if value.lower() in ['none', 'null', '']:
                        cleaned[list_field] = None
                    else:
                        try:
                            # 尝试解析JSON格式的列表
                            import json
                            cleaned[list_field] = json.loads(value)
                        except:
                            cleaned[list_field] = None

        # 确保必要字段存在
        if 'enabled' not in cleaned:
            cleaned['enabled'] = True

        if 'type' not in cleaned:
            cleaned['type'] = 'custom'

        return cleaned

    def _clean_config_data(self, config_obj: Dict) -> Dict:
        """清理和验证配置数据"""
        cleaned = config_obj.copy()

        # 处理优先级枚举
        if 'priority' in cleaned:
            priority = cleaned['priority']
            # 处理枚举字符串格式 (如 'TaskPriority.MEDIUM' -> 'medium')
            if 'TaskPriority.' in priority:
                priority = priority.split('.')[-1].lower()
            cleaned['priority'] = priority

        # 处理可选的字典字段
        for dict_field in ['notification_config', 'rate_limit']:
            if dict_field in cleaned:
                value = cleaned[dict_field]
                if isinstance(value, str):
                    if value.lower() in ['none', 'null', '']:
                        cleaned[dict_field] = None
                    else:
                        try:
                            # 尝试解析JSON格式的字典
                            import json
                            cleaned[dict_field] = json.loads(value)
                        except:
                            cleaned[dict_field] = None

        # 处理整数字段
        for int_field in ['batch_size', 'max_retries', 'timeout', 'concurrent_limit']:
            if int_field in cleaned:
                value = cleaned[int_field]
                if isinstance(value, str):
                    try:
                        cleaned[int_field] = int(value)
                    except ValueError:
                        # 设置默认值
                        defaults = {
                            'batch_size': 50,
                            'max_retries': 3,
                            'timeout': 300,
                            'concurrent_limit': 2
                        }
                        cleaned[int_field] = defaults.get(int_field, 1)

        # 确保必要字段存在
        if 'platform' not in cleaned:
            cleaned['platform'] = 'mercadolibre'

        if 'priority' not in cleaned:
            cleaned['priority'] = 'medium'

        return cleaned
    
    async def update_task(self, task_id: str, updates: Dict) -> bool:
        """更新任务 - 分层结构"""
        try:
            redis_client = await self.get_redis_client()

            # 检查任务是否存在
            basic_key = f"{self.TASK_PREFIX}:{task_id}:basic"
            exists = await redis_client.exists(basic_key)
            if not exists:
                logger.warning(f"Task {task_id} does not exist")
                return False

            # 获取当前任务数据以便更新索引
            current_task = await self.get_task(task_id)
            if not current_task:
                logger.warning(f"Failed to get current task data for {task_id}")
                return False

            # 分类更新字段
            basic_updates = {}
            config_updates = {}
            schedule_updates = {}
            stats_updates = {}

            for field, value in updates.items():
                if field in ['id', 'name', 'description', 'created_at', 'updated_at']:
                    basic_updates[field] = str(value)
                elif field == 'config' and isinstance(value, dict):
                    for k, v in value.items():
                        config_updates[k] = str(v)
                elif field == 'schedule' and isinstance(value, dict):
                    for k, v in value.items():
                        schedule_updates[k] = str(v)
                elif field in ['next_run', 'last_run']:
                    schedule_updates[field] = str(value)
                else:
                    # 其他字段归类为统计信息
                    if field == 'is_running':
                        stats_updates[field] = str(value).lower()
                    else:
                        stats_updates[field] = str(value)

            # 更新各个层
            if basic_updates:
                basic_key = f"{self.TASK_PREFIX}:{task_id}:basic"
                await redis_client.hset(basic_key, mapping=basic_updates)

            if config_updates:
                config_key = f"{self.TASK_PREFIX}:{task_id}:config"
                await redis_client.hset(config_key, mapping=config_updates)

            if schedule_updates:
                schedule_key = f"{self.TASK_PREFIX}:{task_id}:schedule"
                await redis_client.hset(schedule_key, mapping=schedule_updates)

            if stats_updates:
                stats_key = f"{self.TASK_PREFIX}:{task_id}:stats"
                await redis_client.hset(stats_key, mapping=stats_updates)

            # 更新索引（如果状态或平台发生变化）
            await self._update_indexes_on_change(redis_client, task_id, current_task, updates)

            logger.debug(f"Updated task {task_id} in layered Redis structure")
            return True

        except Exception as e:
            logger.error(f"Failed to update task {task_id}: {e}")
            return False

    async def _update_indexes_on_change(self, redis_client, task_id: str, current_task: Dict, updates: Dict):
        """在任务更新时更新索引"""
        # 状态变化
        if 'status' in updates:
            old_status = current_task.get('status')
            new_status = updates['status']

            if old_status != new_status:
                # 从旧状态索引中移除
                if old_status:
                    await redis_client.srem(f'{self.INDEX_PREFIX}:by_status:{old_status}', task_id)
                    if old_status == 'active':
                        await redis_client.srem(f'{self.INDEX_PREFIX}:active_tasks', task_id)

                # 添加到新状态索引
                await redis_client.sadd(f'{self.INDEX_PREFIX}:by_status:{new_status}', task_id)
                if new_status == 'active':
                    await redis_client.sadd(f'{self.INDEX_PREFIX}:active_tasks', task_id)

        # 平台变化
        if 'config' in updates and isinstance(updates['config'], dict):
            new_platform = updates['config'].get('platform')
            old_config = current_task.get('config', {})
            old_platform = old_config.get('platform') if isinstance(old_config, dict) else None

            if new_platform and old_platform != new_platform:
                # 从旧平台索引中移除
                if old_platform:
                    await redis_client.srem(f'{self.INDEX_PREFIX}:by_platform:{old_platform}', task_id)

                # 添加到新平台索引
                await redis_client.sadd(f'{self.INDEX_PREFIX}:by_platform:{new_platform}', task_id)
    
    async def delete_task(self, task_id: str) -> bool:
        """删除任务 - 分层结构"""
        try:
            redis_client = await self.get_redis_client()

            # 获取任务数据用于清理索引
            task_data = await self.get_task(task_id)
            if not task_data:
                logger.warning(f"Task {task_id} was not found for deletion")
                return False

            # 在删除任务数据之前，先获取URL列表用于清理反向关联
            urls_key = f"{self.TASK_PREFIX}:{task_id}:urls"
            url_ids = await redis_client.smembers(urls_key)

            logger.debug(f"Task {task_id} has {len(url_ids)} URLs to clean up: {list(url_ids)}")

            # 删除各个层的数据
            basic_key = f"{self.TASK_PREFIX}:{task_id}:basic"
            config_key = f"{self.TASK_PREFIX}:{task_id}:config"
            schedule_key = f"{self.TASK_PREFIX}:{task_id}:schedule"
            stats_key = f"{self.TASK_PREFIX}:{task_id}:stats"

            # 批量删除
            keys_to_delete = [basic_key, config_key, schedule_key, stats_key, urls_key]
            deleted_count = await redis_client.delete(*keys_to_delete)

            if deleted_count > 0:
                # 从所有索引中移除
                await self._remove_from_all_indexes(redis_client, task_id, task_data)

                # 清理URL反向关联（使用之前获取的URL列表）
                await self._cleanup_url_associations_with_urls(redis_client, task_id, url_ids)

                logger.info(f"Deleted task {task_id} from layered Redis structure")
                return True
            else:
                logger.warning(f"No data found for task {task_id}")
                return False

        except Exception as e:
            logger.error(f"Failed to delete task {task_id}: {e}")
            return False

    async def _remove_from_all_indexes(self, redis_client, task_id: str, task_data: Dict):
        """从所有索引中移除任务"""
        # 从总索引中移除
        await redis_client.srem(f'{self.INDEX_PREFIX}:all_task_ids', task_id)

        # 从状态索引中移除
        status = task_data.get('status')
        if status:
            await redis_client.srem(f'{self.INDEX_PREFIX}:by_status:{status}', task_id)
            if status == 'active':
                await redis_client.srem(f'{self.INDEX_PREFIX}:active_tasks', task_id)

        # 从平台索引中移除
        config = task_data.get('config', {})
        if isinstance(config, dict):
            platform = config.get('platform')
            if platform:
                await redis_client.srem(f'{self.INDEX_PREFIX}:by_platform:{platform}', task_id)

    async def _cleanup_url_associations(self, redis_client, task_id: str):
        """清理URL关联（旧方法，保留兼容性）"""
        # 获取任务关联的URL
        urls_key = f"{self.TASK_PREFIX}:{task_id}:urls"
        url_ids = await redis_client.smembers(urls_key)

        # 从URL反向关联中移除此任务
        for url_id in url_ids:
            url_id_str = url_id.decode() if isinstance(url_id, bytes) else url_id
            await redis_client.srem(f'{self.URL_TASK_PREFIX}:{url_id_str}', task_id)

    async def _cleanup_url_associations_with_urls(self, redis_client, task_id: str, url_ids):
        """清理URL关联（使用预先获取的URL列表）"""
        logger.debug(f"Cleaning up URL associations for task {task_id} with {len(url_ids)} URLs")

        # 从URL反向关联中移除此任务
        for url_id in url_ids:
            url_id_str = url_id.decode() if isinstance(url_id, bytes) else url_id
            url_tasks_key = f'{self.URL_TASK_PREFIX}:{url_id_str}'

            # 移除任务ID
            removed_count = await redis_client.srem(url_tasks_key, task_id)
            logger.debug(f"Removed task {task_id} from URL {url_id_str}, removed: {removed_count}")

            # 如果URL不再关联任何任务，删除空的反向关联键
            remaining_tasks = await redis_client.scard(url_tasks_key)
            if remaining_tasks == 0:
                await redis_client.delete(url_tasks_key)
                logger.debug(f"Deleted empty URL association key: {url_tasks_key}")

        logger.info(f"Cleaned up URL associations for task {task_id}: {len(url_ids)} URLs processed")
    
    async def list_tasks(self, filters: Dict = None, page: int = 1, page_size: int = 20) -> Dict:
        """列出任务（支持分页和过滤）- 使用索引优化"""
        try:
            redis_client = await self.get_redis_client()

            # 根据过滤条件选择合适的索引
            task_ids = await self._get_filtered_task_ids(redis_client, filters)

            if not task_ids:
                return {"tasks": [], "total": 0, "page": page, "page_size": page_size}

            # 获取任务数据
            tasks = []
            for task_id in task_ids:
                task_id_str = task_id.decode() if isinstance(task_id, bytes) else task_id
                task_data = await self.get_task(task_id_str)
                if task_data:
                    tasks.append(task_data)

            # 应用过滤器
            if filters:
                filtered_tasks = []
                for task in tasks:
                    # 状态过滤
                    if 'status' in filters and filters['status']:
                        if task.get('status') != filters['status']:
                            continue

                    # 平台过滤
                    if 'platform' in filters and filters['platform']:
                        task_config = task.get('config', {})
                        task_platform = task_config.get('platform') if isinstance(task_config, dict) else None
                        if task_platform != filters['platform']:
                            continue

                    # 搜索过滤
                    if 'search' in filters and filters['search']:
                        search_term = filters['search'].lower()
                        task_name = task.get('name', '').lower()
                        task_desc = task.get('description', '').lower()
                        if search_term not in task_name and search_term not in task_desc:
                            continue

                    # 来源过滤
                    if 'source' in filters and filters['source']:
                        if task.get('source') != filters['source']:
                            continue

                    filtered_tasks.append(task)

                tasks = filtered_tasks

            # 排序
            sort_by = filters.get('sort_by', 'created_at') if filters else 'created_at'
            sort_order = filters.get('sort_order', 'desc') if filters else 'desc'

            try:
                reverse = sort_order.lower() == 'desc'
                tasks.sort(key=lambda x: x.get(sort_by, ''), reverse=reverse)
            except Exception as e:
                logger.warning(f"Failed to sort tasks by {sort_by}: {e}")

            # 分页
            total = len(tasks)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            paginated_tasks = tasks[start_idx:end_idx]

            logger.debug(f"Retrieved {len(paginated_tasks)}/{total} tasks from Redis")
            return {
                "tasks": paginated_tasks,
                "total": total,
                "page": page,
                "page_size": page_size
            }

        except Exception as e:
            logger.error(f"Failed to list tasks: {e}")
            return {"tasks": [], "total": 0, "page": page, "page_size": page_size}
    
    async def task_exists(self, task_id: str) -> bool:
        """检查任务是否存在 - 分层结构"""
        try:
            redis_client = await self.get_redis_client()
            # 检查基本信息层是否存在
            basic_key = f"{self.TASK_PREFIX}:{task_id}:basic"
            exists = await redis_client.exists(basic_key)
            return bool(exists)
        except Exception as e:
            logger.error(f"Failed to check task existence {task_id}: {e}")
            return False
    
    async def get_task_urls(self, task_id: str) -> List[str]:
        """获取任务关联的URL列表（统一格式）"""
        try:
            redis_client = await self.get_redis_client()
            url_key = f"{self.TASK_PREFIX}:{task_id}:urls"
            url_ids = await redis_client.smembers(url_key)
            return list(url_ids)
        except Exception as e:
            logger.error(f"Failed to get URLs for task {task_id}: {e}")
            return []
    
    async def add_urls_to_task(self, task_id: str, url_ids: List[str]) -> bool:
        """添加URL到任务（包含反向关联）"""
        try:
            redis_client = await self.get_redis_client()

            if url_ids:
                # 1. 添加到任务的URL集合（正向关联）
                task_urls_key = f"{self.TASK_PREFIX}:{task_id}:urls"
                await redis_client.sadd(task_urls_key, *url_ids)

                # 2. 添加URL反向关联（URL -> 任务列表）
                for url_id in url_ids:
                    url_tasks_key = f"{self.URL_TASK_PREFIX}:{url_id}"
                    await redis_client.sadd(url_tasks_key, task_id)

            logger.debug(f"Added {len(url_ids)} URLs to task {task_id} with reverse associations")
            return True
        except Exception as e:
            logger.error(f"Failed to add URLs to task {task_id}: {e}")
            return False
    
    async def remove_urls_from_task(self, task_id: str, url_ids: List[str]) -> bool:
        """从任务中移除URL（包含反向关联）"""
        try:
            redis_client = await self.get_redis_client()

            if url_ids:
                # 1. 从任务的URL集合中移除（正向关联）
                task_urls_key = f"{self.TASK_PREFIX}:{task_id}:urls"
                await redis_client.srem(task_urls_key, *url_ids)

                # 2. 移除URL反向关联（URL -> 任务列表）
                for url_id in url_ids:
                    url_tasks_key = f"{self.URL_TASK_PREFIX}:{url_id}"
                    await redis_client.srem(url_tasks_key, task_id)

                    # 如果URL不再关联任何任务，删除空的反向关联键
                    remaining_tasks = await redis_client.scard(url_tasks_key)
                    if remaining_tasks == 0:
                        await redis_client.delete(url_tasks_key)

            logger.debug(f"Removed {len(url_ids)} URLs from task {task_id} with reverse associations")
            return True
        except Exception as e:
            logger.error(f"Failed to remove URLs from task {task_id}: {e}")
            return False

    async def get_tasks_by_url(self, url_id: str) -> List[str]:
        """获取使用指定URL的任务列表（反向查询）"""
        try:
            redis_client = await self.get_redis_client()
            url_tasks_key = f"{self.URL_TASK_PREFIX}:{url_id}"
            task_ids = await redis_client.smembers(url_tasks_key)
            return list(task_ids)
        except Exception as e:
            logger.error(f"Failed to get tasks for URL {url_id}: {e}")
            return []

    async def get_urls_usage_count(self, url_ids: List[str]) -> Dict[str, int]:
        """获取URL的使用次数（被多少个任务使用）"""
        try:
            redis_client = await self.get_redis_client()
            usage_count = {}

            for url_id in url_ids:
                url_tasks_key = f"{self.URL_TASK_PREFIX}:{url_id}"
                count = await redis_client.scard(url_tasks_key)
                usage_count[url_id] = count

            return usage_count
        except Exception as e:
            logger.error(f"Failed to get URL usage count: {e}")
            return {}

    async def get_tasks_by_ids(self, task_ids: List[str]) -> List[Dict]:
        """批量获取任务"""
        try:
            tasks = []
            for task_id in task_ids:
                task_data = await self.get_task(task_id)
                if task_data:
                    tasks.append(task_data)

            logger.debug(f"Retrieved {len(tasks)} tasks by IDs")
            return tasks
        except Exception as e:
            logger.error(f"Failed to get tasks by IDs: {e}")
            return []

    async def update_tasks_batch(self, updates: List[Dict]) -> Dict[str, bool]:
        """批量更新任务"""
        try:
            results = {}
            for update in updates:
                task_id = update.get('task_id')
                task_updates = {k: v for k, v in update.items() if k != 'task_id'}

                if task_id and task_updates:
                    success = await self.update_task(task_id, task_updates)
                    results[task_id] = success
                else:
                    results[task_id] = False

            logger.debug(f"Batch updated {len(results)} tasks")
            return results
        except Exception as e:
            logger.error(f"Failed to batch update tasks: {e}")
            return {}

    async def get_task_count_by_status(self) -> Dict[str, int]:
        """获取按状态分组的任务数量"""
        try:
            redis_client = await self.get_redis_client()

            # 获取所有任务ID
            task_ids = await redis_client.smembers('monitoring_tasks:all_ids')

            status_counts = {}
            for task_id in task_ids:
                task_key = f"monitoring_tasks:{task_id}"
                status = await redis_client.hget(task_key, 'status')
                if status:
                    status = status.decode() if isinstance(status, bytes) else status
                    status_counts[status] = status_counts.get(status, 0) + 1

            logger.debug(f"Task count by status: {status_counts}")
            return status_counts
        except Exception as e:
            logger.error(f"Failed to get task count by status: {e}")
            return {}


# 全局实例
_task_storage_service = None


async def get_task_storage() -> TaskStorageService:
    """获取任务存储服务实例"""
    global _task_storage_service
    if _task_storage_service is None:
        _task_storage_service = TaskStorageService()
    return _task_storage_service


async def close_task_storage():
    """关闭任务存储服务"""
    global _task_storage_service
    if _task_storage_service:
        await _task_storage_service.close()
        _task_storage_service = None
