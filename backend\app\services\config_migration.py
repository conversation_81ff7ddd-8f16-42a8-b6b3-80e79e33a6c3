"""
配置迁移工具 - 将单一爬虫配置迁移到爬虫池配置
"""

import logging
from datetime import datetime
from typing import Optional

from ..api.v1.crawler_config import CrawlerFullConfig
from ..schemas.crawler_pool import (
    CrawlerPoolConfig, CrawlerBackend, BackendStatus, AuthType, LoadBalanceStrategy
)
from .crawler_pool_service import crawler_pool_service


logger = logging.getLogger(__name__)


class ConfigMigrationService:
    """配置迁移服务"""
    
    @staticmethod
    def migrate_single_config_to_pool(
        old_config: CrawlerFullConfig,
        pool_name: str = "默认爬虫池",
        backend_name: str = "默认爬虫后端"
    ) -> CrawlerPoolConfig:
        """将单一配置迁移到爬虫池配置"""
        
        now = datetime.now()
        
        # 创建默认后端
        default_backend = CrawlerBackend(
            id="default_backend",
            name=backend_name,
            description="从单一配置迁移的默认后端",
            base_url=old_config.api.base_url,
            timeout=old_config.api.timeout,
            max_retries=old_config.api.max_retries,
            auth_type=AuthType.NONE,  # 原配置中没有认证信息
            max_concurrent=2,  # 默认并发数
            weight=1,
            priority=1,
            status=BackendStatus.ACTIVE,
            health_score=1.0,
            created_at=now,
            updated_at=now
        )
        
        # 创建池配置
        pool_config = CrawlerPoolConfig(
            pool_id="default_pool",
            pool_name=pool_name,
            description="从单一配置迁移的默认爬虫池",
            backends=[default_backend],
            load_balance_strategy=LoadBalanceStrategy.ROUND_ROBIN,
            health_check_interval=60,
            failure_threshold=3,
            recovery_threshold=2,
            browser=old_config.browser,
            crawler=old_config.crawler,
            llm=old_config.llm,
            schema_extraction=old_config.schema_extraction,
            content_processing=old_config.content_processing,
            link_filtering=old_config.link_filtering,
            scheduler=old_config.scheduler,
            monitor=old_config.monitor,
            created_at=now,
            updated_at=now
        )
        
        return pool_config
    
    @staticmethod
    def pool_config_to_single_config(pool_config: CrawlerPoolConfig) -> CrawlerFullConfig:
        """将爬虫池配置转换为单一配置（向后兼容）"""
        
        # 选择第一个活跃的后端作为默认配置
        active_backends = pool_config.active_backends
        if not active_backends:
            # 如果没有活跃后端，使用第一个后端
            if not pool_config.backends:
                raise ValueError("爬虫池中没有可用的后端")
            backend = pool_config.backends[0]
        else:
            backend = active_backends[0]
        
        # 构建单一配置
        from ..api.v1.crawler_config import APIConfig
        
        api_config = APIConfig(
            base_url=backend.base_url,
            timeout=backend.timeout,
            max_retries=backend.max_retries
        )
        
        single_config = CrawlerFullConfig(
            api=api_config,
            browser=pool_config.browser,
            crawler=pool_config.crawler,
            llm=pool_config.llm,
            schema_extraction=pool_config.schema_extraction,
            content_processing=pool_config.content_processing,
            link_filtering=pool_config.link_filtering,
            scheduler=pool_config.scheduler,
            monitor=pool_config.monitor
        )
        
        return single_config
    
    @staticmethod
    async def ensure_default_pool_exists() -> CrawlerPoolConfig:
        """确保默认爬虫池存在，如果不存在则创建"""
        
        # 检查是否已有默认池
        default_pool = await crawler_pool_service.get_default_pool()
        if default_pool:
            return default_pool
        
        # 尝试从现有的单一配置创建默认池
        try:
            from ..api.v1.crawler_config import config_service
            
            # 获取现有配置
            old_config = await config_service.get_config()
            
            # 迁移到池配置
            pool_config = ConfigMigrationService.migrate_single_config_to_pool(old_config)
            
            # 保存池配置
            await crawler_pool_service._save_pool_config(pool_config)
            
            # 添加到池列表
            crawler_pool_service.redis_client.sadd(
                crawler_pool_service.keys['pool_list'], 
                pool_config.pool_id
            )
            
            # 设为默认池
            await crawler_pool_service.set_default_pool(pool_config.pool_id)
            
            logger.info("Successfully migrated single config to default pool")
            return pool_config
            
        except Exception as e:
            logger.error(f"Failed to migrate config to pool: {e}")
            
            # 如果迁移失败，创建一个基本的默认池
            return await ConfigMigrationService._create_basic_default_pool()
    
    @staticmethod
    async def _create_basic_default_pool() -> CrawlerPoolConfig:
        """创建基本的默认爬虫池"""
        
        from ..api.v1.crawler_config import (
            APIConfig, BrowserConfig, CrawlerConfig, LLMConfig,
            SchemaExtractionConfig, ContentProcessingConfig, 
            LinkFilteringConfig, SchedulerConfig, MonitorConfig
        )
        
        now = datetime.now()
        
        # 创建基本后端
        basic_backend = CrawlerBackend(
            id="basic_backend",
            name="基本爬虫后端",
            description="系统默认创建的基本后端",
            base_url="http://localhost:11234",  # 默认地址
            timeout=30000,
            max_retries=3,
            auth_type=AuthType.NONE,
            max_concurrent=2,
            weight=1,
            priority=1,
            status=BackendStatus.ACTIVE,
            health_score=1.0,
            created_at=now,
            updated_at=now
        )
        
        # 创建基本池配置
        pool_config = CrawlerPoolConfig(
            pool_id="basic_default_pool",
            pool_name="基本默认爬虫池",
            description="系统自动创建的基本默认爬虫池",
            backends=[basic_backend],
            load_balance_strategy=LoadBalanceStrategy.ROUND_ROBIN,
            health_check_interval=60,
            failure_threshold=3,
            recovery_threshold=2,
            browser=BrowserConfig(),
            crawler=CrawlerConfig(),
            llm=LLMConfig(api_key="sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi"),
            schema_extraction=SchemaExtractionConfig(),
            content_processing=ContentProcessingConfig(),
            link_filtering=LinkFilteringConfig(),
            scheduler=SchedulerConfig(),
            monitor=MonitorConfig(),
            created_at=now,
            updated_at=now
        )
        
        # 保存配置
        await crawler_pool_service._save_pool_config(pool_config)
        
        # 添加到池列表
        crawler_pool_service.redis_client.sadd(
            crawler_pool_service.keys['pool_list'], 
            pool_config.pool_id
        )
        
        # 设为默认池
        await crawler_pool_service.set_default_pool(pool_config.pool_id)
        
        logger.info("Created basic default crawler pool")
        return pool_config


# 全局迁移服务实例
config_migration_service = ConfigMigrationService()
