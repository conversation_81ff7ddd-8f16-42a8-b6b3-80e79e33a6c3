"""
新架构：爬取配置API接口
提供爬取配置的CRUD操作和管理功能
"""

from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import JSONResponse

from ...schemas.crawler_config_new import (
    CrawlerConfig, CrawlerConfigCreate, CrawlerConfigUpdate,
    CrawlerConfigSummary, CrawlerConfigStats, CrawlerConfigTemplate,
    CrawlerConfigValidationResult, ConfigStatus
)
from ...services.crawler_config_service import crawler_config_service
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/crawler-configs", tags=["爬取配置管理"])


@router.post("/", response_model=CrawlerConfig, summary="创建爬取配置")
async def create_crawler_config(config_data: CrawlerConfigCreate):
    """
    创建新的爬取配置
    
    - **config_name**: 配置名称（必须唯一）
    - **description**: 配置描述
    - **browser**: 浏览器配置
    - **crawler**: 爬虫配置
    - **llm**: LLM配置
    - **schema_extraction**: 模式提取配置
    - **content_processing**: 内容处理配置
    - **link_filtering**: 链接过滤配置
    - **monitor**: 监控配置
    """
    try:
        config = await crawler_config_service.create_config(config_data)
        return config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create crawler config: {e}")
        raise HTTPException(status_code=500, detail=f"创建配置失败: {str(e)}")


@router.get("/", response_model=List[CrawlerConfigSummary], summary="获取配置列表")
async def list_crawler_configs(
    status: Optional[ConfigStatus] = Query(None, description="配置状态过滤"),
    tags: Optional[List[str]] = Query(None, description="标签过滤"),
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """
    获取爬取配置列表
    
    支持按状态和标签过滤，支持分页
    """
    try:
        configs = await crawler_config_service.list_configs(
            status=status,
            tags=tags,
            limit=limit,
            offset=offset
        )
        return configs
    except Exception as e:
        logger.error(f"Failed to list crawler configs: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置列表失败: {str(e)}")


@router.get("/stats", response_model=CrawlerConfigStats, summary="获取配置统计")
async def get_crawler_config_stats():
    """
    获取爬取配置统计信息
    
    包括总数、状态分布、使用情况等
    """
    try:
        stats = await crawler_config_service.get_config_stats()
        return stats
    except Exception as e:
        logger.error(f"Failed to get crawler config stats: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/{config_id}", response_model=CrawlerConfig, summary="获取配置详情")
async def get_crawler_config(config_id: str):
    """
    根据ID获取爬取配置详情
    """
    try:
        config = await crawler_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        return config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get crawler config {config_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")


@router.get("/by-name/{config_name}", response_model=CrawlerConfig, summary="根据名称获取配置")
async def get_crawler_config_by_name(config_name: str):
    """
    根据名称获取爬取配置
    """
    try:
        config = await crawler_config_service.get_config_by_name(config_name)
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        return config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get crawler config by name {config_name}: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")


@router.put("/{config_id}", response_model=CrawlerConfig, summary="更新配置")
async def update_crawler_config(config_id: str, update_data: CrawlerConfigUpdate):
    """
    更新爬取配置
    
    只更新提供的字段，其他字段保持不变
    """
    try:
        config = await crawler_config_service.update_config(config_id, update_data)
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        return config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update crawler config {config_id}: {e}")
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")


@router.delete("/{config_id}", summary="删除配置")
async def delete_crawler_config(config_id: str):
    """
    删除爬取配置
    
    注意：如果配置正在被Worker使用，删除会失败
    """
    try:
        success = await crawler_config_service.delete_config(config_id)
        if not success:
            raise HTTPException(status_code=400, detail="删除配置失败")
        return {"message": "配置删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete crawler config {config_id}: {e}")
        raise HTTPException(status_code=500, detail=f"删除配置失败: {str(e)}")


@router.post("/{config_id}/validate", response_model=CrawlerConfigValidationResult, summary="验证配置")
async def validate_crawler_config(config_id: str):
    """
    验证爬取配置的有效性
    
    检查配置的完整性和合理性
    """
    try:
        config = await crawler_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        validation_result = await crawler_config_service.validate_config(config)
        return validation_result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to validate crawler config {config_id}: {e}")
        raise HTTPException(status_code=500, detail=f"验证配置失败: {str(e)}")


@router.post("/{config_id}/duplicate", response_model=CrawlerConfig, summary="复制配置")
async def duplicate_crawler_config(
    config_id: str, 
    new_name: str = Query(..., description="新配置名称")
):
    """
    复制现有配置创建新配置
    """
    try:
        # 获取原配置
        original_config = await crawler_config_service.get_config(config_id)
        if not original_config:
            raise HTTPException(status_code=404, detail="原配置不存在")
        
        # 创建复制的配置数据
        duplicate_data = CrawlerConfigCreate(
            config_name=new_name,
            description=f"复制自 {original_config.config_name}",
            browser=original_config.browser,
            crawler=original_config.crawler,
            llm=original_config.llm,
            schema_extraction=original_config.schema_extraction,
            content_processing=original_config.content_processing,
            link_filtering=original_config.link_filtering,
            monitor=original_config.monitor,
            version=original_config.version,
            tags=original_config.tags.copy()
        )
        
        # 创建新配置
        new_config = await crawler_config_service.create_config(duplicate_data)
        return new_config
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to duplicate crawler config {config_id}: {e}")
        raise HTTPException(status_code=500, detail=f"复制配置失败: {str(e)}")


@router.patch("/{config_id}/status", response_model=CrawlerConfig, summary="更新配置状态")
async def update_config_status(config_id: str, status: ConfigStatus):
    """
    更新配置状态
    
    - **active**: 活跃状态，可以被Worker使用
    - **inactive**: 非活跃状态，暂停使用
    - **draft**: 草稿状态，开发中
    - **archived**: 归档状态，不再使用
    """
    try:
        update_data = CrawlerConfigUpdate(status=status)
        config = await crawler_config_service.update_config(config_id, update_data)
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        return config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update config status {config_id}: {e}")
        raise HTTPException(status_code=500, detail=f"更新状态失败: {str(e)}")


@router.post("/{config_id}/increment-usage", response_model=CrawlerConfig, summary="增加使用次数")
async def increment_config_usage(config_id: str):
    """
    增加配置使用次数
    
    当Worker使用此配置时调用
    """
    try:
        config = await crawler_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        config.increment_usage()
        updated_config = await crawler_config_service.update_config(
            config_id, 
            CrawlerConfigUpdate(
                usage_count=config.usage_count,
                last_used=config.last_used
            )
        )
        
        return updated_config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to increment config usage {config_id}: {e}")
        raise HTTPException(status_code=500, detail=f"更新使用次数失败: {str(e)}")


@router.get("/search/by-tags", response_model=List[CrawlerConfigSummary], summary="按标签搜索配置")
async def search_configs_by_tags(
    tags: List[str] = Query(..., description="搜索标签"),
    match_all: bool = Query(False, description="是否匹配所有标签")
):
    """
    按标签搜索配置
    
    - **match_all=false**: 匹配任意一个标签
    - **match_all=true**: 匹配所有标签
    """
    try:
        all_configs = await crawler_config_service.list_configs(limit=1000)
        
        if match_all:
            # 匹配所有标签
            filtered_configs = [
                config for config in all_configs 
                if all(tag in config.tags for tag in tags)
            ]
        else:
            # 匹配任意标签
            filtered_configs = [
                config for config in all_configs 
                if any(tag in config.tags for tag in tags)
            ]
        
        return filtered_configs
    except Exception as e:
        logger.error(f"Failed to search configs by tags: {e}")
        raise HTTPException(status_code=500, detail=f"搜索配置失败: {str(e)}")


@router.get("/export/{config_id}", summary="导出配置")
async def export_crawler_config(config_id: str):
    """
    导出配置为JSON格式
    
    用于备份或迁移配置
    """
    try:
        config = await crawler_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        export_data = {
            "export_time": datetime.now().isoformat(),
            "config": config.dict()
        }
        
        return JSONResponse(
            content=export_data,
            headers={
                "Content-Disposition": f"attachment; filename=crawler_config_{config_id}.json"
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to export crawler config {config_id}: {e}")
        raise HTTPException(status_code=500, detail=f"导出配置失败: {str(e)}")
