import React from 'react';
import {
  Card,
  Row,
  Col,
  Typography,
  Tag,
  Descriptions,
  Alert,
  Space,
  Divider
} from 'antd';
import {
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

import type { MonitoringTask } from '../../services/monitoringTaskApi';
import type { TaskConfig, ScheduleConfig } from '../../types/taskCreate';
import UrlChangesDetail from './UrlChangesDetail';

const { Title, Text } = Typography;

interface EditConfirmStepProps {
  originalTask: MonitoringTask | null;
  originalUrls: string[];
  wizardData: {
    selectedUrls: string[];
    basicConfig: {
      name: string;
      description: string;
      platform: string;
      priority: string;
      tags: string[];
    };
    taskConfig: TaskConfig;
    scheduleConfig: ScheduleConfig;
  };
}

const EditConfirmStep: React.FC<EditConfirmStepProps> = ({
  originalTask,
  originalUrls,
  wizardData
}) => {
  if (!originalTask) {
    return <div>加载中...</div>;
  }

  // 检查是否有变更
  const hasBasicChanges =
    originalTask.name !== wizardData.basicConfig.name ||
    (originalTask.description || '') !== wizardData.basicConfig.description;

  // 检查URL变更
  const hasUrlChanges =
    originalUrls.length !== wizardData.selectedUrls.length ||
    !originalUrls.every(url => wizardData.selectedUrls.includes(url)) ||
    !wizardData.selectedUrls.every(url => originalUrls.includes(url));

  const hasConfigChanges = !originalTask.is_running && (
    originalTask.config.platform !== wizardData.taskConfig.platform ||
    originalTask.config.priority !== wizardData.taskConfig.priority ||
    originalTask.config.retry_count !== wizardData.taskConfig.retry_count ||
    originalTask.config.timeout !== wizardData.taskConfig.timeout ||
    originalTask.config.batch_size !== wizardData.taskConfig.batch_size
  );

  const hasScheduleChanges = !originalTask.is_running && (() => {
    // 检查调度类型变更
    if (originalTask.schedule.type !== wizardData.scheduleConfig.type) {
      return true;
    }

    // 检查执行时间变更 - 根据调度类型选择正确的字段进行比较
    const originalTime = originalTask.schedule.time || originalTask.schedule.start_time;
    const currentTime = wizardData.scheduleConfig.type === 'daily'
      ? wizardData.scheduleConfig.time
      : wizardData.scheduleConfig.start_time;

    if (originalTime !== currentTime) {
      return true;
    }

    // 检查其他字段变更
    const otherChanges = {
      // interval字段只对hourly类型任务有意义
      interval: wizardData.scheduleConfig.type === 'hourly'
        ? originalTask.schedule.interval !== wizardData.scheduleConfig.interval
        : false,
      timezone: originalTask.schedule.timezone !== wizardData.scheduleConfig.timezone,
      enabled: originalTask.schedule.enabled !== wizardData.scheduleConfig.enabled,
      end_time: originalTask.schedule.end_time !== wizardData.scheduleConfig.end_time,
      // 检查随机延迟相关字段
      enable_random_delay: originalTask.schedule.enable_random_delay !== wizardData.scheduleConfig.enable_random_delay,
      random_delay_min: originalTask.schedule.random_delay_min !== wizardData.scheduleConfig.random_delay_min,
      random_delay_max: originalTask.schedule.random_delay_max !== wizardData.scheduleConfig.random_delay_max
    };

    if (otherChanges.interval || otherChanges.timezone || otherChanges.enabled || otherChanges.end_time ||
        otherChanges.enable_random_delay || otherChanges.random_delay_min || otherChanges.random_delay_max) {
      return true;
    }

    return false;
  })();

  const hasAnyChanges = hasBasicChanges || hasConfigChanges || hasScheduleChanges || hasUrlChanges;

  // 格式化调度类型显示
  const formatScheduleType = (type: string) => {
    const typeMap: Record<string, string> = {
      'once': '单次执行',
      'daily': '每天',
      'weekly': '每周',
      'hourly': '每小时'
    };
    return typeMap[type] || type;
  };

  // 格式化优先级显示
  const formatPriority = (priority: string) => {
    const priorityMap: Record<string, { text: string; color: string }> = {
      'high': { text: '高', color: 'red' },
      'normal': { text: '中', color: 'blue' },
      'low': { text: '低', color: 'green' }
    };
    const config = priorityMap[priority] || { text: priority, color: 'default' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 格式化平台显示
  const formatPlatform = (platform: string) => {
    const platformMap: Record<string, string> = {
      'mercadolibre': 'MercadoLibre',
      'amazon': 'Amazon',
      'ebay': 'eBay',
      'aliexpress': 'AliExpress',
      'shopee': 'Shopee'
    };
    return platformMap[platform] || platform;
  };

  return (
    <div>
      <Title level={4}>确认修改</Title>
      <Text type="secondary">请确认以下修改内容，点击"保存修改"完成任务编辑</Text>

      {/* 运行状态提示 */}
      {originalTask.is_running && (
        <Alert
          message="任务运行中"
          description="任务正在运行中，只能修改基本信息，不能修改调度配置和任务配置。"
          type="warning"
          icon={<ExclamationCircleOutlined />}
          style={{ marginTop: 16, marginBottom: 24 }}
        />
      )}

      {/* 无变更提示 */}
      {!hasAnyChanges && (
        <Alert
          message="无修改内容"
          description="您没有修改任何配置项。"
          type="info"
          icon={<InfoCircleOutlined />}
          style={{ marginTop: 16, marginBottom: 24 }}
        />
      )}

      {/* URL管理对比 */}
      <Card title="URL管理" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={24}>
          <Col span={12}>
            <Title level={5}>修改前</Title>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="URL数量">
                {originalUrls.length}个
              </Descriptions.Item>
            </Descriptions>
          </Col>
          <Col span={12}>
            <Title level={5}>修改后</Title>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="URL数量">
                <Text strong={hasUrlChanges}>
                  {wizardData.selectedUrls.length}个
                </Text>
              </Descriptions.Item>
            </Descriptions>
          </Col>
        </Row>
      </Card>

      {/* URL变更详情 */}
      {hasUrlChanges && (
        <div style={{ marginBottom: 16 }}>
          <UrlChangesDetail
            originalUrls={originalUrls}
            currentUrls={wizardData.selectedUrls}
          />
        </div>
      )}

      {/* 基本信息对比 */}
      <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={24}>
          <Col span={12}>
            <Title level={5}>修改前</Title>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="任务名称">
                {originalTask.name}
              </Descriptions.Item>
              <Descriptions.Item label="任务描述">
                {originalTask.description || '无'}
              </Descriptions.Item>
            </Descriptions>
          </Col>
          <Col span={12}>
            <Title level={5}>修改后</Title>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="任务名称">
                <Text strong={hasBasicChanges && originalTask.name !== wizardData.basicConfig.name}>
                  {wizardData.basicConfig.name}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="任务描述">
                <Text strong={hasBasicChanges && (originalTask.description || '') !== wizardData.basicConfig.description}>
                  {wizardData.basicConfig.description || '无'}
                </Text>
              </Descriptions.Item>
            </Descriptions>
          </Col>
        </Row>
      </Card>

      {/* 任务配置对比 */}
      {!originalTask.is_running && (
        <Card title="任务配置" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={24}>
            <Col span={12}>
              <Title level={5}>修改前</Title>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="目标平台">
                  {formatPlatform(originalTask.config.platform)}
                </Descriptions.Item>
                <Descriptions.Item label="优先级">
                  {formatPriority(originalTask.config.priority)}
                </Descriptions.Item>
                <Descriptions.Item label="重试次数">
                  {originalTask.config.retry_count}
                </Descriptions.Item>
                <Descriptions.Item label="超时时间">
                  {originalTask.config.timeout}秒
                </Descriptions.Item>
                <Descriptions.Item label="批次大小">
                  {originalTask.config.batch_size}
                </Descriptions.Item>
              </Descriptions>
            </Col>
            <Col span={12}>
              <Title level={5}>修改后</Title>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="目标平台">
                  <Text strong={hasConfigChanges && originalTask.config.platform !== wizardData.taskConfig.platform}>
                    {formatPlatform(wizardData.taskConfig.platform)}
                  </Text>
                </Descriptions.Item>
                <Descriptions.Item label="优先级">
                  <Text strong={hasConfigChanges && originalTask.config.priority !== wizardData.taskConfig.priority}>
                    {formatPriority(wizardData.taskConfig.priority)}
                  </Text>
                </Descriptions.Item>
                <Descriptions.Item label="重试次数">
                  <Text strong={hasConfigChanges && originalTask.config.retry_count !== wizardData.taskConfig.retry_count}>
                    {wizardData.taskConfig.retry_count}
                  </Text>
                </Descriptions.Item>
                <Descriptions.Item label="超时时间">
                  <Text strong={hasConfigChanges && originalTask.config.timeout !== wizardData.taskConfig.timeout}>
                    {wizardData.taskConfig.timeout}秒
                  </Text>
                </Descriptions.Item>
                <Descriptions.Item label="批次大小">
                  <Text strong={hasConfigChanges && originalTask.config.batch_size !== wizardData.taskConfig.batch_size}>
                    {wizardData.taskConfig.batch_size}
                  </Text>
                </Descriptions.Item>
              </Descriptions>
            </Col>
          </Row>
        </Card>
      )}

      {/* 调度配置对比 */}
      {!originalTask.is_running && (
        <Card title="调度配置" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={24}>
            <Col span={12}>
              <Title level={5}>修改前</Title>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="调度类型">
                  {formatScheduleType(originalTask.schedule.type)}
                </Descriptions.Item>
                <Descriptions.Item label="执行时间">
                  {originalTask.schedule.time || '未设置'}
                </Descriptions.Item>
                {originalTask.schedule.type === 'hourly' && (
                  <Descriptions.Item label="执行间隔">
                    {originalTask.schedule.interval || 60}分钟
                  </Descriptions.Item>
                )}
                <Descriptions.Item label="时区">
                  {originalTask.schedule.timezone}
                </Descriptions.Item>
              </Descriptions>
            </Col>
            <Col span={12}>
              <Title level={5}>修改后</Title>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="调度类型">
                  <Text strong={hasScheduleChanges && originalTask.schedule.type !== wizardData.scheduleConfig.type}>
                    {formatScheduleType(wizardData.scheduleConfig.type)}
                  </Text>
                </Descriptions.Item>
                <Descriptions.Item label="执行时间">
                  <Text strong={(() => {
                    // 检查执行时间是否真的有变更
                    const originalTime = originalTask.schedule.time || originalTask.schedule.start_time;
                    const currentTime = wizardData.scheduleConfig.type === 'daily'
                      ? wizardData.scheduleConfig.time
                      : wizardData.scheduleConfig.start_time;
                    return hasScheduleChanges && originalTime !== currentTime;
                  })()}>
                    {(() => {
                      // 根据调度类型选择正确的时间字段
                      const executionTime = wizardData.scheduleConfig.type === 'daily'
                        ? wizardData.scheduleConfig.time
                        : wizardData.scheduleConfig.start_time;

                      if (!executionTime) return '未设置';

                      // 如果是HH:mm格式，直接显示
                      if (typeof executionTime === 'string' && /^\d{2}:\d{2}$/.test(executionTime)) {
                        return executionTime;
                      }

                      // 如果是完整的日期时间，格式化显示
                      if (!isNaN(new Date(executionTime).getTime())) {
                        return new Date(executionTime).toLocaleString();
                      }

                      // 其他情况直接显示
                      return executionTime;
                    })()}
                  </Text>
                </Descriptions.Item>
                {wizardData.scheduleConfig.type === 'hourly' && (
                  <Descriptions.Item label="执行间隔">
                    <Text strong={hasScheduleChanges && originalTask.schedule.interval !== wizardData.scheduleConfig.interval}>
                      {wizardData.scheduleConfig.interval}分钟
                    </Text>
                  </Descriptions.Item>
                )}
                <Descriptions.Item label="时区">
                  <Text strong={hasScheduleChanges && originalTask.schedule.timezone !== wizardData.scheduleConfig.timezone}>
                    {wizardData.scheduleConfig.timezone}
                  </Text>
                </Descriptions.Item>
              </Descriptions>
            </Col>
          </Row>
        </Card>
      )}

      {/* 变更摘要 */}
      {hasAnyChanges && (
        <Card title="变更摘要" size="small">
          <Space direction="vertical" style={{ width: '100%' }}>
            {hasUrlChanges && (
              <Text>
                <Tag color="purple">URL管理</Tag>
                监控URL列表已修改 (原{originalUrls.length}个 → 现{wizardData.selectedUrls.length}个)
              </Text>
            )}
            {hasBasicChanges && (
              <Text>
                <Tag color="blue">基本信息</Tag>
                任务名称或描述已修改
              </Text>
            )}
            {hasConfigChanges && (
              <Text>
                <Tag color="orange">任务配置</Tag>
                平台、优先级或执行参数已修改
              </Text>
            )}
            {hasScheduleChanges && (
              <Text>
                <Tag color="green">调度配置</Tag>
                执行时间或调度规则已修改
              </Text>
            )}
          </Space>
        </Card>
      )}
    </div>
  );
};

export default EditConfirmStep;
