/**
 * 重构版爬虫池创建页面 - 从配置列表中选择和组合
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  InputNumber,
  Button,
  Space,
  message,
  Steps,
  Row,
  Col,
  Divider,
  Table,
  Checkbox,
  Tag,
  Typography,
  Alert,
  Progress,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  ArrowLeftOutlined,
  SaveOutlined,
  ClusterOutlined,
  ApiOutlined,
  CheckCircleOutlined,
  HeartOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

import {
  crawlerPoolApi,
  CrawlerPoolCreateNew
} from '../../services/crawlerPoolApi';
import { 
  crawlerInstanceApi, 
  CrawlerInstanceConfig 
} from '../../services/crawlerInstanceApi';

const { Step } = Steps;
const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

const CrawlerPoolCreateNewPage: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [availableConfigs, setAvailableConfigs] = useState<CrawlerInstanceConfig[]>([]);
  const [selectedConfigIds, setSelectedConfigIds] = useState<string[]>([]);

  // 加载可用的爬虫配置
  const loadAvailableConfigs = async () => {
    try {
      const configs = await crawlerInstanceApi.getActiveConfigs();
      setAvailableConfigs(configs);
    } catch (error) {
      message.error('加载爬虫配置列表失败');
      console.error('Load configs error:', error);
    }
  };

  // 创建爬虫池
  const handleCreatePool = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      if (selectedConfigIds.length === 0) {
        message.error('请至少选择一个爬虫配置');
        return;
      }

      const poolData: CrawlerPoolCreateNew = {
        pool_name: values.pool_name,
        description: values.description,
        crawler_config_ids: selectedConfigIds,
        load_balance_strategy: values.load_balance_strategy,
        health_check_interval: values.health_check_interval,
        failure_threshold: values.failure_threshold,
        recovery_threshold: values.recovery_threshold,
      };

      await crawlerPoolApi.createPoolNew(poolData);
      message.success('爬虫池创建成功');
      navigate('/crawler-pool');
      
    } catch (error: any) {
      message.error(error.response?.data?.detail || '创建爬虫池失败');
    } finally {
      setLoading(false);
    }
  };

  // 配置选择处理
  const handleConfigSelection = (configId: string, checked: boolean) => {
    if (checked) {
      setSelectedConfigIds([...selectedConfigIds, configId]);
    } else {
      setSelectedConfigIds(selectedConfigIds.filter(id => id !== configId));
    }
  };

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedConfigIds(availableConfigs.map(config => config.config_id));
    } else {
      setSelectedConfigIds([]);
    }
  };

  useEffect(() => {
    loadAvailableConfigs();
  }, []);

  // 配置选择表格列
  const configColumns = [
    {
      title: (
        <Checkbox
          checked={selectedConfigIds.length === availableConfigs.length && availableConfigs.length > 0}
          indeterminate={selectedConfigIds.length > 0 && selectedConfigIds.length < availableConfigs.length}
          onChange={(e) => handleSelectAll(e.target.checked)}
        >
          全选
        </Checkbox>
      ),
      key: 'select',
      render: (record: CrawlerInstanceConfig) => (
        <Checkbox
          checked={selectedConfigIds.includes(record.config_id)}
          onChange={(e) => handleConfigSelection(record.config_id, e.target.checked)}
        />
      ),
    },
    {
      title: '配置名称',
      dataIndex: 'config_name',
      key: 'config_name',
      render: (text: string, record: CrawlerInstanceConfig) => (
        <Space>
          <ApiOutlined />
          <strong>{text}</strong>
          {record.config_id === 'default_config' && (
            <Tag color="gold">默认</Tag>
          )}
        </Space>
      ),
    },
    {
      title: 'API端点',
      dataIndex: 'api_endpoint',
      key: 'api_endpoint',
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <Text code>{text}</Text>
        </Tooltip>
      ),
    },
    {
      title: '性能配置',
      key: 'performance',
      render: (record: CrawlerInstanceConfig) => (
        <Space direction="vertical" size="small">
          <Text type="secondary">并发: {record.max_concurrent}</Text>
          <Text type="secondary">权重: {record.weight}</Text>
          <Text type="secondary">优先级: {record.priority}</Text>
        </Space>
      ),
    },
    {
      title: '健康状态',
      key: 'health',
      render: (record: CrawlerInstanceConfig) => (
        <Space direction="vertical" size="small">
          <Progress 
            percent={Math.round(record.health_score * 100)} 
            size="small" 
            status={record.health_score >= 0.8 ? 'success' : record.health_score >= 0.5 ? 'active' : 'exception'}
          />
          <Tag color={crawlerInstanceApi.getStatusColor(record.status)}>
            {crawlerInstanceApi.getStatusText(record.status)}
          </Tag>
        </Space>
      ),
    },
    {
      title: '认证类型',
      dataIndex: 'auth_config',
      key: 'auth_type',
      render: (authConfig: any) => (
        <Tag color="blue">
          {crawlerInstanceApi.getAuthTypeText(authConfig.auth_type)}
        </Tag>
      ),
    },
  ];

  const steps = [
    {
      title: '基本信息',
      content: (
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="池名称"
              name="pool_name"
              rules={[{ required: true, message: '请输入池名称' }]}
            >
              <Input placeholder="输入爬虫池名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="负载均衡策略"
              name="load_balance_strategy"
              initialValue="weighted_round_robin"
            >
              <Select>
                <Option value="round_robin">轮询</Option>
                <Option value="weighted_round_robin">加权轮询</Option>
                <Option value="least_connections">最少连接</Option>
                <Option value="health_based">基于健康评分</Option>
                <Option value="response_time">基于响应时间</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="描述"
              name="description"
            >
              <TextArea rows={3} placeholder="输入爬虫池描述（可选）" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="健康检查间隔(秒)"
              name="health_check_interval"
              initialValue={60}
            >
              <InputNumber min={10} max={3600} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="故障阈值"
              name="failure_threshold"
              initialValue={3}
            >
              <InputNumber min={1} max={10} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="恢复阈值"
              name="recovery_threshold"
              initialValue={2}
            >
              <InputNumber min={1} max={10} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      ),
    },
    {
      title: '选择爬虫配置',
      content: (
        <div>
          <Alert
            message="选择爬虫配置"
            description="从下面的列表中选择要加入爬虫池的配置。每个配置都包含完整的爬虫设置（浏览器、LLM、认证等），确保池中所有任务使用一致的配置。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <div style={{ marginBottom: 16 }}>
            <Space>
              <Text strong>已选择: {selectedConfigIds.length} / {availableConfigs.length}</Text>
              {selectedConfigIds.length === 0 && (
                <Text type="danger">请至少选择一个配置</Text>
              )}
            </Space>
          </div>
          
          <Table
            columns={configColumns}
            dataSource={availableConfigs}
            rowKey="config_id"
            pagination={false}
            locale={{ emptyText: '暂无可用的爬虫配置，请先创建爬虫配置' }}
            size="middle"
          />
          
          {availableConfigs.length === 0 && (
            <div style={{ textAlign: 'center', marginTop: 16 }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => navigate('/crawler-settings/instance/create')}
              >
                创建爬虫配置
              </Button>
            </div>
          )}
        </div>
      ),
    },
    {
      title: '确认创建',
      content: (
        <div>
          <Alert
            message="创建确认"
            description="请确认以下信息无误后创建爬虫池。"
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Row gutter={16}>
            <Col span={12}>
              <Card title="池基本信息" size="small">
                <p><strong>池名称:</strong> {form.getFieldValue('pool_name')}</p>
                <p><strong>负载均衡:</strong> {form.getFieldValue('load_balance_strategy')}</p>
                <p><strong>健康检查间隔:</strong> {form.getFieldValue('health_check_interval')}秒</p>
              </Card>
            </Col>
            <Col span={12}>
              <Card title="选中的配置" size="small">
                <p><strong>配置数量:</strong> {selectedConfigIds.length}</p>
                <div>
                  <strong>配置列表:</strong>
                  <div style={{ marginTop: 8 }}>
                    {selectedConfigIds.map(configId => {
                      const config = availableConfigs.find(c => c.config_id === configId);
                      return config ? (
                        <Tag key={configId} color="blue" style={{ marginBottom: 4 }}>
                          {config.config_name}
                        </Tag>
                      ) : null;
                    })}
                  </div>
                </div>
              </Card>
            </Col>
          </Row>
        </div>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <Space>
            <ClusterOutlined />
            创建爬虫池
            <Tag color="green">New Architecture</Tag>
          </Space>
        }
        extra={
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/crawler-pool')}
          >
            返回
          </Button>
        }
      >
        <Steps current={currentStep} style={{ marginBottom: 24 }}>
          {steps.map(item => (
            <Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <Form
          form={form}
          layout="vertical"
          style={{ marginTop: 24 }}
        >
          {steps[currentStep].content}
        </Form>

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Space>
            {currentStep > 0 && (
              <Button onClick={() => setCurrentStep(currentStep - 1)}>
                上一步
              </Button>
            )}
            
            {currentStep < steps.length - 1 && (
              <Button 
                type="primary" 
                onClick={() => setCurrentStep(currentStep + 1)}
                disabled={currentStep === 1 && selectedConfigIds.length === 0}
              >
                下一步
              </Button>
            )}
            
            {currentStep === steps.length - 1 && (
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={loading}
                onClick={handleCreatePool}
                disabled={selectedConfigIds.length === 0}
              >
                创建爬虫池
              </Button>
            )}
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default CrawlerPoolCreateNewPage;
