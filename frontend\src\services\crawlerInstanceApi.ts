/**
 * 爬虫实例配置API服务
 */

import axios from 'axios';

// 创建专用的axios实例
const crawlerInstanceClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 类型定义
export interface AuthConfig {
  auth_type: 'none' | 'api_key' | 'bearer_token' | 'basic_auth';
  api_key?: string;
  username?: string;
  password?: string;
  token?: string;
}

export interface CrawlerInstanceConfig {
  config_id: string;
  config_name: string;
  description?: string;
  api_endpoint: string;
  timeout: number;
  max_retries: number;
  auth_config: AuthConfig;
  browser: any;
  crawler: any;
  llm: any;
  schema_extraction: any;
  content_processing: any;
  link_filtering: any;
  scheduler: any;
  monitor: any;
  max_concurrent: number;
  weight: number;
  priority: number;
  status: 'active' | 'inactive' | 'maintenance' | 'error';
  health_score: number;
  last_check?: string;
  total_requests: number;
  success_requests: number;
  failed_requests: number;
  avg_response_time: number;
  created_at?: string;
  updated_at?: string;
}

export interface CrawlerInstanceConfigCreate {
  config_name: string;
  description?: string;
  api_endpoint: string;
  timeout?: number;
  max_retries?: number;
  auth_config: AuthConfig;
  browser: any;
  crawler: any;
  llm: any;
  schema_extraction: any;
  content_processing: any;
  link_filtering: any;
  scheduler: any;
  monitor: any;
  max_concurrent?: number;
  weight?: number;
  priority?: number;
}

export interface CrawlerInstanceConfigUpdate {
  config_name?: string;
  description?: string;
  api_endpoint?: string;
  timeout?: number;
  max_retries?: number;
  auth_config?: AuthConfig;
  browser?: any;
  crawler?: any;
  llm?: any;
  schema_extraction?: any;
  content_processing?: any;
  link_filtering?: any;
  scheduler?: any;
  monitor?: any;
  max_concurrent?: number;
  weight?: number;
  priority?: number;
  status?: 'active' | 'inactive' | 'maintenance' | 'error';
}

export interface ConnectionTestResult {
  config_id: string;
  is_connected: boolean;
  response_time: number;
  error_message?: string;
  test_time: string;
  api_version?: string;
  server_info?: any;
}

export interface ConfigStats {
  total_configs: number;
  active_configs: number;
  healthy_configs: number;
  inactive_configs: number;
  unhealthy_configs: number;
  default_config_id?: string;
  avg_health_score: number;
  avg_response_time: number;
  health_percentage: number;
}

class CrawlerInstanceApiService {
  private baseUrl = '/api/v1/crawler/instances';

  // 配置管理
  async getAllConfigs(): Promise<CrawlerInstanceConfig[]> {
    const response = await crawlerInstanceClient.get(this.baseUrl);
    return response.data;
  }

  async getConfig(configId: string): Promise<CrawlerInstanceConfig> {
    const response = await crawlerInstanceClient.get(`${this.baseUrl}/${configId}`);
    return response.data;
  }

  async createConfig(configData: CrawlerInstanceConfigCreate): Promise<CrawlerInstanceConfig> {
    const response = await crawlerInstanceClient.post(this.baseUrl, configData);
    return response.data;
  }

  async updateConfig(configId: string, updateData: CrawlerInstanceConfigUpdate): Promise<CrawlerInstanceConfig> {
    const response = await crawlerInstanceClient.put(`${this.baseUrl}/${configId}`, updateData);
    return response.data;
  }

  async deleteConfig(configId: string): Promise<void> {
    await crawlerInstanceClient.delete(`${this.baseUrl}/${configId}`);
  }

  // 默认配置管理
  async getDefaultConfig(): Promise<CrawlerInstanceConfig> {
    const response = await crawlerInstanceClient.get(`${this.baseUrl}/default/config`);
    return response.data;
  }

  async setDefaultConfig(configId: string): Promise<void> {
    await crawlerInstanceClient.post(`${this.baseUrl}/${configId}/set-default`);
  }

  // 连接测试
  async testConnection(configId: string): Promise<ConnectionTestResult> {
    const response = await crawlerInstanceClient.post(`${this.baseUrl}/${configId}/test-connection`);
    return response.data;
  }

  async batchTestConnections(): Promise<any> {
    const response = await crawlerInstanceClient.post(`${this.baseUrl}/batch/test-connections`);
    return response.data;
  }

  // 配置列表
  async getActiveConfigs(): Promise<CrawlerInstanceConfig[]> {
    const response = await crawlerInstanceClient.get(`${this.baseUrl}/active/list`);
    return response.data;
  }

  async getHealthyConfigs(): Promise<CrawlerInstanceConfig[]> {
    const response = await crawlerInstanceClient.get(`${this.baseUrl}/healthy/list`);
    return response.data;
  }

  // 统计信息
  async getConfigStats(): Promise<ConfigStats> {
    const response = await crawlerInstanceClient.get(`${this.baseUrl}/stats/summary`);
    return response.data;
  }

  // 工具方法
  getStatusColor(status: string): string {
    switch (status) {
      case 'active':
        return 'green';
      case 'inactive':
        return 'orange';
      case 'maintenance':
        return 'blue';
      case 'error':
        return 'red';
      default:
        return 'gray';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'active':
        return '活跃';
      case 'inactive':
        return '非活跃';
      case 'maintenance':
        return '维护中';
      case 'error':
        return '错误';
      default:
        return '未知';
    }
  }

  getAuthTypeText(authType: string): string {
    switch (authType) {
      case 'none':
        return '无认证';
      case 'api_key':
        return 'API密钥';
      case 'bearer_token':
        return 'Bearer Token';
      case 'basic_auth':
        return '基础认证';
      default:
        return '未知';
    }
  }

  formatResponseTime(responseTime: number): string {
    if (responseTime < 1000) {
      return `${Math.round(responseTime)}ms`;
    } else {
      return `${(responseTime / 1000).toFixed(2)}s`;
    }
  }

  calculateSuccessRate(config: CrawlerInstanceConfig): number {
    if (config.total_requests === 0) {
      return 0;
    }
    return (config.success_requests / config.total_requests) * 100;
  }

  getHealthScoreColor(score: number): string {
    if (score >= 0.8) return 'green';
    if (score >= 0.5) return 'orange';
    return 'red';
  }

  getHealthScoreText(score: number): string {
    if (score >= 0.8) return '健康';
    if (score >= 0.5) return '一般';
    return '不健康';
  }
}

export const crawlerInstanceApi = new CrawlerInstanceApiService();
