/**
 * 创建爬虫实例配置页面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  InputNumber,
  Button,
  Space,
  message,
  Steps,
  Row,
  Col,
  Divider,
  Typography,
  Alert
} from 'antd';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  ApiOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

import { 
  crawlerInstanceApi, 
  CrawlerInstanceConfigCreate,
  AuthConfig
} from '../../services/crawlerInstanceApi';
import { crawlerConfigService } from '../../services/crawlerConfigService';

const { Step } = Steps;
const { TextArea } = Input;
const { Option } = Select;
const { Title } = Typography;

const CrawlerInstanceCreatePage: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [defaultConfig, setDefaultConfig] = useState<any>(null);

  // 加载默认配置作为模板
  const loadDefaultConfig = async () => {
    try {
      const config = await crawlerConfigService.getDefaultConfig();
      setDefaultConfig(config);
      
      // 设置表单默认值
      form.setFieldsValue({
        timeout: 30000,
        max_retries: 3,
        max_concurrent: 2,
        weight: 1,
        priority: 1,
        auth_type: 'none',
      });
    } catch (error) {
      console.error('Load default config error:', error);
    }
  };

  // 创建配置
  const handleCreateConfig = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      if (!defaultConfig) {
        message.error('无法获取默认配置模板');
        return;
      }

      // 构建认证配置
      const authConfig: AuthConfig = {
        auth_type: values.auth_type,
        api_key: values.api_key,
        username: values.username,
        password: values.password,
      };

      const configData: CrawlerInstanceConfigCreate = {
        config_name: values.config_name,
        description: values.description,
        api_endpoint: values.api_endpoint,
        timeout: values.timeout,
        max_retries: values.max_retries,
        auth_config: authConfig,
        max_concurrent: values.max_concurrent,
        weight: values.weight,
        priority: values.priority,
        // 使用默认配置作为模板
        browser: defaultConfig.browser,
        crawler: defaultConfig.crawler,
        llm: defaultConfig.llm,
        schema_extraction: defaultConfig.schema_extraction,
        content_processing: defaultConfig.content_processing,
        link_filtering: defaultConfig.link_filtering,
        scheduler: defaultConfig.scheduler,
        monitor: defaultConfig.monitor,
      };

      await crawlerInstanceApi.createConfig(configData);
      message.success('爬虫配置创建成功');
      navigate('/crawler-settings/instance');
      
    } catch (error: any) {
      message.error(error.response?.data?.detail || '创建爬虫配置失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDefaultConfig();
  }, []);

  const steps = [
    {
      title: '基本信息',
      content: (
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="配置名称"
              name="config_name"
              rules={[{ required: true, message: '请输入配置名称' }]}
            >
              <Input placeholder="输入爬虫配置名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="API端点"
              name="api_endpoint"
              rules={[
                { required: true, message: '请输入API端点' },
                { type: 'url', message: '请输入有效的URL' }
              ]}
            >
              <Input placeholder="http://localhost:11234" />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="描述"
              name="description"
            >
              <TextArea rows={3} placeholder="输入配置描述（可选）" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="超时时间(ms)"
              name="timeout"
            >
              <InputNumber min={1000} max={300000} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="最大重试次数"
              name="max_retries"
            >
              <InputNumber min={0} max={10} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="最大并发数"
              name="max_concurrent"
            >
              <InputNumber min={1} max={20} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      ),
    },
    {
      title: '认证配置',
      content: (
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="认证类型"
              name="auth_type"
            >
              <Select>
                <Option value="none">无认证</Option>
                <Option value="api_key">API密钥</Option>
                <Option value="bearer_token">Bearer Token</Option>
                <Option value="basic_auth">基础认证</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) => 
                prevValues.auth_type !== currentValues.auth_type
              }
            >
              {({ getFieldValue }) => {
                const authType = getFieldValue('auth_type');
                
                if (authType === 'api_key' || authType === 'bearer_token') {
                  return (
                    <Form.Item
                      label="API密钥"
                      name="api_key"
                      rules={[{ required: true, message: '请输入API密钥' }]}
                    >
                      <Input.Password placeholder="输入API密钥" />
                    </Form.Item>
                  );
                }
                
                return null;
              }}
            </Form.Item>
          </Col>
          
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => 
              prevValues.auth_type !== currentValues.auth_type
            }
          >
            {({ getFieldValue }) => {
              const authType = getFieldValue('auth_type');
              
              if (authType === 'basic_auth') {
                return (
                  <>
                    <Col span={12}>
                      <Form.Item
                        label="用户名"
                        name="username"
                        rules={[{ required: true, message: '请输入用户名' }]}
                      >
                        <Input placeholder="输入用户名" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="密码"
                        name="password"
                        rules={[{ required: true, message: '请输入密码' }]}
                      >
                        <Input.Password placeholder="输入密码" />
                      </Form.Item>
                    </Col>
                  </>
                );
              }
              
              return null;
            }}
          </Form.Item>
        </Row>
      ),
    },
    {
      title: '性能配置',
      content: (
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              label="权重"
              name="weight"
              tooltip="负载均衡时的权重，数值越大分配的任务越多"
            >
              <InputNumber min={1} max={100} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="优先级"
              name="priority"
              tooltip="任务分配优先级，数值越小优先级越高"
            >
              <InputNumber min={1} max={10} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="最大并发数"
              name="max_concurrent"
              tooltip="该配置同时处理的最大任务数"
            >
              <InputNumber min={1} max={20} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Alert
              message="配置说明"
              description={
                <div>
                  <p><strong>权重</strong>：在加权轮询策略中，权重越高的配置会分配到更多任务。</p>
                  <p><strong>优先级</strong>：在基于优先级的策略中，优先级高的配置会优先分配任务。</p>
                  <p><strong>并发数</strong>：限制该配置同时处理的任务数量，避免过载。</p>
                </div>
              }
              type="info"
              showIcon
            />
          </Col>
        </Row>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <Space>
            <ApiOutlined />
            创建爬虫实例配置
          </Space>
        }
        extra={
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/crawler-settings/instance')}
          >
            返回
          </Button>
        }
      >
        <Steps current={currentStep} style={{ marginBottom: 24 }}>
          {steps.map(item => (
            <Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <Form
          form={form}
          layout="vertical"
          style={{ marginTop: 24 }}
        >
          {steps[currentStep].content}
        </Form>

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Space>
            {currentStep > 0 && (
              <Button onClick={() => setCurrentStep(currentStep - 1)}>
                上一步
              </Button>
            )}
            
            {currentStep < steps.length - 1 && (
              <Button 
                type="primary" 
                onClick={() => setCurrentStep(currentStep + 1)}
              >
                下一步
              </Button>
            )}
            
            {currentStep === steps.length - 1 && (
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={loading}
                onClick={handleCreateConfig}
              >
                创建配置
              </Button>
            )}
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default CrawlerInstanceCreatePage;
