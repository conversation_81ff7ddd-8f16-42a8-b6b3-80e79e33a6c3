/**
 * 爬虫配置监控仪表板
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Button,
  Space,
  Tag,
  Alert,
  Typography,
  Divider,
  Tooltip,
  message,
  Badge,
  Timeline
} from 'antd';
import {
  ReloadOutlined,
  HeartOutlined,
  <PERSON>boltOutlined,
  ApiOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  WarningOutlined
} from '@ant-design/icons';

import { 
  crawlerInstanceApi, 
  CrawlerInstanceConfig, 
  ConnectionTestResult,
  ConfigStats
} from '../../services/crawlerInstanceApi';

const { Title, Text } = Typography;

const ConfigMonitorDashboard: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [configs, setConfigs] = useState<CrawlerInstanceConfig[]>([]);
  const [stats, setStats] = useState<ConfigStats | null>(null);
  const [testResults, setTestResults] = useState<Record<string, ConnectionTestResult>>({});
  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date());

  // 加载数据
  const loadData = async () => {
    try {
      setLoading(true);
      const [configsData, statsData] = await Promise.all([
        crawlerInstanceApi.getAllConfigs(),
        crawlerInstanceApi.getConfigStats()
      ]);
      setConfigs(configsData);
      setStats(statsData);
      setLastUpdateTime(new Date());
    } catch (error) {
      message.error('加载监控数据失败');
      console.error('Load monitor data error:', error);
    } finally {
      setLoading(false);
    }
  };

  // 批量测试连接
  const handleBatchTest = async () => {
    try {
      setLoading(true);
      const result = await crawlerInstanceApi.batchTestConnections();
      
      // 更新测试结果
      const newTestResults: Record<string, ConnectionTestResult> = {};
      result.results.forEach((testResult: ConnectionTestResult) => {
        newTestResults[testResult.config_id] = testResult;
      });
      setTestResults(newTestResults);
      
      message.success(
        `批量测试完成: ${result.successful_connections}/${result.total_configs} 连接成功`
      );
      
      // 重新加载数据
      await loadData();
    } catch (error) {
      message.error('批量测试失败');
    } finally {
      setLoading(false);
    }
  };

  // 自动刷新
  useEffect(() => {
    loadData();
    
    // 设置定时刷新
    const interval = setInterval(loadData, 30000); // 30秒刷新一次
    
    return () => clearInterval(interval);
  }, []);

  // 健康状态统计
  const getHealthStats = () => {
    if (!configs.length) return { healthy: 0, warning: 0, error: 0 };
    
    const healthy = configs.filter(c => c.health_score >= 0.8).length;
    const warning = configs.filter(c => c.health_score >= 0.5 && c.health_score < 0.8).length;
    const error = configs.filter(c => c.health_score < 0.5).length;
    
    return { healthy, warning, error };
  };

  // 连接状态统计
  const getConnectionStats = () => {
    const testResultsArray = Object.values(testResults);
    if (!testResultsArray.length) return { connected: 0, disconnected: 0, untested: configs.length };
    
    const connected = testResultsArray.filter(r => r.is_connected).length;
    const disconnected = testResultsArray.filter(r => !r.is_connected).length;
    const untested = configs.length - testResultsArray.length;
    
    return { connected, disconnected, untested };
  };

  const healthStats = getHealthStats();
  const connectionStats = getConnectionStats();

  // 监控表格列
  const monitorColumns = [
    {
      title: '配置名称',
      dataIndex: 'config_name',
      key: 'config_name',
      render: (text: string, record: CrawlerInstanceConfig) => (
        <Space>
          <ApiOutlined />
          <strong>{text}</strong>
          {stats?.default_config_id === record.config_id && (
            <Tag color="gold">默认</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '健康状态',
      key: 'health',
      render: (record: CrawlerInstanceConfig) => (
        <Space direction="vertical" size="small">
          <Progress 
            percent={Math.round(record.health_score * 100)} 
            size="small" 
            status={record.health_score >= 0.8 ? 'success' : record.health_score >= 0.5 ? 'active' : 'exception'}
            format={(percent) => `${percent}%`}
          />
          <Tag color={crawlerInstanceApi.getHealthScoreColor(record.health_score)}>
            {crawlerInstanceApi.getHealthScoreText(record.health_score)}
          </Tag>
        </Space>
      ),
    },
    {
      title: '连接状态',
      key: 'connection',
      render: (record: CrawlerInstanceConfig) => {
        const testResult = testResults[record.config_id];
        
        if (!testResult) {
          return <Tag color="gray">未测试</Tag>;
        }
        
        return (
          <Space direction="vertical" size="small">
            <Tag color={testResult.is_connected ? 'green' : 'red'}>
              {testResult.is_connected ? '连接正常' : '连接失败'}
            </Tag>
            <Text type="secondary">
              {crawlerInstanceApi.formatResponseTime(testResult.response_time)}
            </Text>
          </Space>
        );
      },
    },
    {
      title: '性能指标',
      key: 'performance',
      render: (record: CrawlerInstanceConfig) => (
        <Space direction="vertical" size="small">
          <Text type="secondary">
            成功率: {crawlerInstanceApi.calculateSuccessRate(record).toFixed(1)}%
          </Text>
          <Text type="secondary">
            平均响应: {crawlerInstanceApi.formatResponseTime(record.avg_response_time)}
          </Text>
          <Text type="secondary">
            总请求: {record.total_requests}
          </Text>
        </Space>
      ),
    },
    {
      title: '配置信息',
      key: 'config',
      render: (record: CrawlerInstanceConfig) => (
        <Space direction="vertical" size="small">
          <Text type="secondary">并发: {record.max_concurrent}</Text>
          <Text type="secondary">权重: {record.weight}</Text>
          <Text type="secondary">优先级: {record.priority}</Text>
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={crawlerInstanceApi.getStatusColor(status)}>
          {crawlerInstanceApi.getStatusText(status)}
        </Tag>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <Space>
            <ThunderboltOutlined />
            爬虫配置监控仪表板
            <Badge status="processing" text="实时监控" />
          </Space>
        }
        extra={
          <Space>
            <Text type="secondary">
              最后更新: {lastUpdateTime.toLocaleTimeString()}
            </Text>
            <Button
              icon={<HeartOutlined />}
              onClick={handleBatchTest}
              loading={loading}
            >
              批量测试
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadData}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        }
      >
        {/* 总体统计 */}
        {stats && (
          <>
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={6}>
                <Statistic
                  title="总配置数"
                  value={stats.total_configs}
                  prefix={<ApiOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="活跃配置"
                  value={stats.active_configs}
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="平均健康度"
                  value={stats.health_percentage}
                  suffix="%"
                  prefix={<HeartOutlined />}
                  valueStyle={{ 
                    color: stats.health_percentage >= 80 ? '#3f8600' : '#cf1322' 
                  }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="平均响应时间"
                  value={stats.avg_response_time.toFixed(0)}
                  suffix="ms"
                  prefix={<ClockCircleOutlined />}
                  valueStyle={{ 
                    color: stats.avg_response_time <= 1000 ? '#3f8600' : '#cf1322' 
                  }}
                />
              </Col>
            </Row>

            {/* 健康状态分布 */}
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={12}>
                <Card title="健康状态分布" size="small">
                  <Row gutter={16}>
                    <Col span={8}>
                      <Statistic
                        title="健康"
                        value={healthStats.healthy}
                        valueStyle={{ color: '#3f8600' }}
                        prefix={<CheckCircleOutlined />}
                      />
                    </Col>
                    <Col span={8}>
                      <Statistic
                        title="警告"
                        value={healthStats.warning}
                        valueStyle={{ color: '#faad14' }}
                        prefix={<WarningOutlined />}
                      />
                    </Col>
                    <Col span={8}>
                      <Statistic
                        title="错误"
                        value={healthStats.error}
                        valueStyle={{ color: '#cf1322' }}
                        prefix={<ExclamationCircleOutlined />}
                      />
                    </Col>
                  </Row>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="连接状态分布" size="small">
                  <Row gutter={16}>
                    <Col span={8}>
                      <Statistic
                        title="已连接"
                        value={connectionStats.connected}
                        valueStyle={{ color: '#3f8600' }}
                        prefix={<CheckCircleOutlined />}
                      />
                    </Col>
                    <Col span={8}>
                      <Statistic
                        title="连接失败"
                        value={connectionStats.disconnected}
                        valueStyle={{ color: '#cf1322' }}
                        prefix={<ExclamationCircleOutlined />}
                      />
                    </Col>
                    <Col span={8}>
                      <Statistic
                        title="未测试"
                        value={connectionStats.untested}
                        valueStyle={{ color: '#8c8c8c' }}
                        prefix={<ClockCircleOutlined />}
                      />
                    </Col>
                  </Row>
                </Card>
              </Col>
            </Row>

            <Divider />
          </>
        )}

        {/* 配置监控表格 */}
        <Table
          columns={monitorColumns}
          dataSource={configs}
          rowKey="config_id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个配置`,
          }}
          size="middle"
        />

        {/* 告警信息 */}
        {healthStats.error > 0 && (
          <Alert
            message="健康状态告警"
            description={`发现 ${healthStats.error} 个配置健康状态异常，请及时检查和处理。`}
            type="error"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}

        {connectionStats.disconnected > 0 && (
          <Alert
            message="连接状态告警"
            description={`发现 ${connectionStats.disconnected} 个配置连接失败，请检查网络和服务状态。`}
            type="warning"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}
      </Card>
    </div>
  );
};

export default ConfigMonitorDashboard;
