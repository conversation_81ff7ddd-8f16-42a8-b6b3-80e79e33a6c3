"""
爬虫后端健康检查服务
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional

import httpx
from fastapi import HTTPException

from ..schemas.crawler_pool import (
    CrawlerBackend, HealthCheckResult, PoolHealthStatus, BackendStatus
)
from ..services.crawler_pool_service import crawler_pool_service


logger = logging.getLogger(__name__)


class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        self.check_timeout = 10.0  # 健康检查超时时间
        self.running_checks: Dict[str, asyncio.Task] = {}
        
    async def check_backend_health(self, backend: CrawlerBackend) -> HealthCheckResult:
        """检查单个后端的健康状态"""
        
        start_time = time.time()
        check_time = datetime.now()
        
        try:
            # 构建健康检查URL
            health_url = f"{backend.base_url.rstrip('/')}/health"
            
            # 准备请求头
            headers = {"User-Agent": "MonIt-HealthChecker/1.0"}
            if backend.auth_type.value == "api_key" and backend.api_key:
                headers["X-API-Key"] = backend.api_key
            elif backend.auth_type.value == "bearer_token" and backend.api_key:
                headers["Authorization"] = f"Bearer {backend.api_key}"
            
            # 发送健康检查请求
            async with httpx.AsyncClient(timeout=self.check_timeout) as client:
                response = await client.get(health_url, headers=headers)
                
                response_time = (time.time() - start_time) * 1000  # 转换为毫秒
                
                # 检查响应状态
                if response.status_code == 200:
                    # 尝试解析响应内容
                    try:
                        data = response.json()
                        status = data.get("status", "unknown")
                        
                        if status in ["healthy", "ok", "running"]:
                            return HealthCheckResult(
                                backend_id=backend.id,
                                is_healthy=True,
                                response_time=response_time,
                                error_message=None,
                                check_time=check_time
                            )
                        else:
                            return HealthCheckResult(
                                backend_id=backend.id,
                                is_healthy=False,
                                response_time=response_time,
                                error_message=f"Backend status: {status}",
                                check_time=check_time
                            )
                    except Exception as parse_error:
                        # 如果无法解析JSON，但状态码是200，认为是健康的
                        return HealthCheckResult(
                            backend_id=backend.id,
                            is_healthy=True,
                            response_time=response_time,
                            error_message=f"Response parse warning: {str(parse_error)}",
                            check_time=check_time
                        )
                else:
                    return HealthCheckResult(
                        backend_id=backend.id,
                        is_healthy=False,
                        response_time=response_time,
                        error_message=f"HTTP {response.status_code}: {response.text[:100]}",
                        check_time=check_time
                    )
                    
        except asyncio.TimeoutError:
            response_time = self.check_timeout * 1000
            return HealthCheckResult(
                backend_id=backend.id,
                is_healthy=False,
                response_time=response_time,
                error_message="Health check timeout",
                check_time=check_time
            )
            
        except httpx.ConnectError:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                backend_id=backend.id,
                is_healthy=False,
                response_time=response_time,
                error_message="Connection failed",
                check_time=check_time
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                backend_id=backend.id,
                is_healthy=False,
                response_time=response_time,
                error_message=f"Health check error: {str(e)}",
                check_time=check_time
            )
    
    async def check_pool_health(self, pool_id: str) -> Optional[PoolHealthStatus]:
        """检查整个爬虫池的健康状态"""
        
        try:
            pool_config = await crawler_pool_service.get_pool(pool_id)
            if not pool_config:
                return None
            
            # 并发检查所有后端
            check_tasks = [
                self.check_backend_health(backend) 
                for backend in pool_config.backends
            ]
            
            backend_results = await asyncio.gather(*check_tasks, return_exceptions=True)
            
            # 处理检查结果
            valid_results = []
            for result in backend_results:
                if isinstance(result, HealthCheckResult):
                    valid_results.append(result)
                else:
                    # 处理异常情况
                    logger.error(f"Health check exception: {result}")
            
            # 计算统计信息
            total_backends = len(pool_config.backends)
            healthy_backends = sum(1 for r in valid_results if r.is_healthy)
            unhealthy_backends = total_backends - healthy_backends
            
            # 计算整体健康评分
            if total_backends > 0:
                overall_health_score = healthy_backends / total_backends
            else:
                overall_health_score = 0.0
            
            # 更新后端状态和统计信息
            await self._update_backend_stats(pool_config, valid_results)
            
            return PoolHealthStatus(
                pool_id=pool_id,
                total_backends=total_backends,
                healthy_backends=healthy_backends,
                unhealthy_backends=unhealthy_backends,
                overall_health_score=overall_health_score,
                backend_results=valid_results,
                last_check=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Failed to check pool health {pool_id}: {e}")
            return None
    
    async def _update_backend_stats(self, pool_config, health_results: List[HealthCheckResult]):
        """更新后端统计信息"""
        
        try:
            # 创建结果映射
            results_map = {result.backend_id: result for result in health_results}
            
            # 更新每个后端的状态
            config_updated = False
            for backend in pool_config.backends:
                result = results_map.get(backend.id)
                if result:
                    # 更新健康评分
                    if result.is_healthy:
                        # 健康时逐渐恢复评分
                        backend.health_score = min(1.0, backend.health_score + 0.1)
                        if backend.status == BackendStatus.ERROR:
                            backend.status = BackendStatus.ACTIVE
                    else:
                        # 不健康时降低评分
                        backend.health_score = max(0.0, backend.health_score - 0.2)
                        if backend.health_score < 0.3:
                            backend.status = BackendStatus.ERROR
                    
                    # 更新响应时间（使用移动平均）
                    if backend.avg_response_time == 0:
                        backend.avg_response_time = result.response_time
                    else:
                        # 使用指数移动平均
                        alpha = 0.3
                        backend.avg_response_time = (
                            alpha * result.response_time + 
                            (1 - alpha) * backend.avg_response_time
                        )
                    
                    # 更新检查时间
                    backend.last_check = result.check_time
                    backend.updated_at = datetime.now()
                    
                    config_updated = True
            
            # 保存更新后的配置
            if config_updated:
                await crawler_pool_service._save_pool_config(pool_config)
                
        except Exception as e:
            logger.error(f"Failed to update backend stats: {e}")
    
    def calculate_health_score(self, backend: CrawlerBackend) -> float:
        """计算后端健康评分"""
        
        # 基础评分基于成功率
        if backend.total_requests > 0:
            success_rate = backend.success_requests / backend.total_requests
        else:
            success_rate = 1.0  # 新后端默认满分
        
        # 响应时间因子（响应时间越短评分越高）
        if backend.avg_response_time > 0:
            # 假设1000ms为基准，超过则降分
            response_factor = min(1.0, 1000 / backend.avg_response_time)
        else:
            response_factor = 1.0
        
        # 状态因子
        if backend.status == BackendStatus.ACTIVE:
            status_factor = 1.0
        elif backend.status == BackendStatus.INACTIVE:
            status_factor = 0.5
        elif backend.status == BackendStatus.MAINTENANCE:
            status_factor = 0.3
        else:  # ERROR
            status_factor = 0.1
        
        # 综合评分
        health_score = (
            success_rate * 0.4 +      # 成功率权重40%
            response_factor * 0.3 +   # 响应时间权重30%
            status_factor * 0.3       # 状态权重30%
        )
        
        return max(0.0, min(1.0, health_score))


class HealthCheckScheduler:
    """健康检查调度器"""
    
    def __init__(self):
        self.health_checker = HealthChecker()
        self.running = False
        self.check_tasks: Dict[str, asyncio.Task] = {}
        
    async def start_periodic_checks(self):
        """启动定期健康检查"""
        
        if self.running:
            return
        
        self.running = True
        logger.info("Started health check scheduler")
        
        while self.running:
            try:
                # 获取所有爬虫池
                pools = await crawler_pool_service.get_all_pools()
                
                # 为每个池启动健康检查
                for pool in pools:
                    if pool.pool_id not in self.check_tasks:
                        task = asyncio.create_task(
                            self._periodic_pool_check(pool.pool_id, pool.health_check_interval)
                        )
                        self.check_tasks[pool.pool_id] = task
                
                # 清理已完成的任务
                completed_tasks = [
                    pool_id for pool_id, task in self.check_tasks.items() 
                    if task.done()
                ]
                for pool_id in completed_tasks:
                    del self.check_tasks[pool_id]
                
                # 等待一段时间再检查
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"Health check scheduler error: {e}")
                await asyncio.sleep(60)  # 出错时等待更长时间
    
    async def _periodic_pool_check(self, pool_id: str, interval: int):
        """定期检查单个池的健康状态"""
        
        while self.running:
            try:
                await self.health_checker.check_pool_health(pool_id)
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"Periodic health check error for pool {pool_id}: {e}")
                await asyncio.sleep(interval)
    
    async def stop(self):
        """停止健康检查调度器"""
        
        self.running = False
        
        # 取消所有检查任务
        for task in self.check_tasks.values():
            task.cancel()
        
        # 等待任务完成
        if self.check_tasks:
            await asyncio.gather(*self.check_tasks.values(), return_exceptions=True)
        
        self.check_tasks.clear()
        logger.info("Stopped health check scheduler")


# 全局健康检查器实例
health_checker = HealthChecker()
health_check_scheduler = HealthCheckScheduler()
