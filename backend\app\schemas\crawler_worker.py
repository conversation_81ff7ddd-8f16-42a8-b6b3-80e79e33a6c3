"""
新架构：爬虫Worker数据模型
Worker = CrawlerConfig + BackendConfig 的组合
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Set
from pydantic import BaseModel, Field, validator
from enum import Enum

from .crawler_config_new import CrawlerConfig, CrawlerConfigSummary
from .backend_config import BackendConfig, BackendConfigSummary


class WorkerStatus(str, Enum):
    """Worker状态枚举"""
    ACTIVE = "active"           # 活跃
    INACTIVE = "inactive"       # 非活跃
    BUSY = "busy"              # 忙碌中
    ERROR = "error"            # 错误状态
    MAINTENANCE = "maintenance" # 维护中


class WorkerPriority(str, Enum):
    """Worker优先级枚举"""
    LOW = "low"                # 低优先级
    NORMAL = "normal"          # 普通优先级
    HIGH = "high"              # 高优先级
    URGENT = "urgent"          # 紧急优先级


class CrawlerWorker(BaseModel):
    """爬虫Worker（配置组合）"""
    
    # 基本信息
    worker_id: str = Field(description="Worker唯一标识")
    worker_name: str = Field(description="Worker名称", max_length=100)
    description: Optional[str] = Field(None, description="Worker描述", max_length=500)
    
    # 配置组合
    crawler_config_id: str = Field(description="爬取配置ID")
    backend_config_id: str = Field(description="后端配置ID")
    
    # Worker状态
    status: WorkerStatus = Field(default=WorkerStatus.ACTIVE, description="Worker状态")
    priority: WorkerPriority = Field(default=WorkerPriority.NORMAL, description="Worker优先级")
    
    # 资源分配
    allocated_concurrent: int = Field(default=1, ge=1, le=50, description="分配的并发数")
    max_tasks_per_hour: Optional[int] = Field(None, ge=1, description="每小时最大任务数")
    
    # 使用统计
    total_tasks: int = Field(default=0, ge=0, description="总任务数")
    completed_tasks: int = Field(default=0, ge=0, description="完成任务数")
    failed_tasks: int = Field(default=0, ge=0, description="失败任务数")
    current_tasks: int = Field(default=0, ge=0, description="当前任务数")
    
    # 性能统计
    avg_task_duration: float = Field(default=0.0, ge=0.0, description="平均任务时长(秒)")
    success_rate: float = Field(default=0.0, ge=0.0, le=1.0, description="成功率")
    last_task_time: Optional[datetime] = Field(None, description="最后任务时间")
    
    # 健康状态
    health_score: float = Field(default=1.0, ge=0.0, le=1.0, description="健康评分")
    last_health_check: Optional[datetime] = Field(None, description="最后健康检查时间")
    error_count: int = Field(default=0, ge=0, description="错误计数")
    
    # 时间戳
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    last_used: Optional[datetime] = Field(None, description="最后使用时间")
    created_by: Optional[str] = Field(None, description="创建者")
    
    @validator('worker_name')
    def validate_worker_name(cls, v):
        """验证Worker名称"""
        if not v or not v.strip():
            raise ValueError("Worker名称不能为空")
        return v.strip()
    
    @validator('completed_tasks')
    def validate_completed_tasks(cls, v, values):
        """验证完成任务数"""
        if 'total_tasks' in values and v > values['total_tasks']:
            raise ValueError("完成任务数不能超过总任务数")
        return v
    
    @validator('failed_tasks')
    def validate_failed_tasks(cls, v, values):
        """验证失败任务数"""
        if 'total_tasks' in values and v > values['total_tasks']:
            raise ValueError("失败任务数不能超过总任务数")
        return v
    
    @property
    def resource_key(self) -> str:
        """资源键，用于互斥检查"""
        return f"backend:{self.backend_config_id}"
    
    @property
    def is_available(self) -> bool:
        """判断Worker是否可用"""
        return (
            self.status == WorkerStatus.ACTIVE and
            self.current_tasks < self.allocated_concurrent and
            self.health_score >= 0.5
        )
    
    @property
    def available_capacity(self) -> int:
        """获取可用容量"""
        return max(0, self.allocated_concurrent - self.current_tasks)
    
    @property
    def utilization_rate(self) -> float:
        """计算利用率"""
        if self.allocated_concurrent == 0:
            return 0.0
        return self.current_tasks / self.allocated_concurrent
    
    def is_conflicted_with(self, other: 'CrawlerWorker') -> bool:
        """检查是否与另一个Worker冲突"""
        return self.resource_key == other.resource_key
    
    def can_handle_task(self) -> bool:
        """判断是否可以处理新任务"""
        return (
            self.is_available and
            (self.max_tasks_per_hour is None or self.current_tasks < self.max_tasks_per_hour)
        )


class CrawlerWorkerCreate(BaseModel):
    """创建Worker的数据模型"""
    
    worker_name: str = Field(description="Worker名称", max_length=100)
    description: Optional[str] = Field(None, description="Worker描述", max_length=500)
    
    # 配置组合
    crawler_config_id: str = Field(description="爬取配置ID")
    backend_config_id: str = Field(description="后端配置ID")
    
    # 可选配置
    priority: WorkerPriority = Field(default=WorkerPriority.NORMAL)
    allocated_concurrent: int = Field(default=1, ge=1, le=50)
    max_tasks_per_hour: Optional[int] = Field(None, ge=1)
    created_by: Optional[str] = None


class CrawlerWorkerUpdate(BaseModel):
    """更新Worker的数据模型"""
    
    worker_name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    
    # 状态更新
    status: Optional[WorkerStatus] = None
    priority: Optional[WorkerPriority] = None
    
    # 资源配置更新
    allocated_concurrent: Optional[int] = Field(None, ge=1, le=50)
    max_tasks_per_hour: Optional[int] = Field(None, ge=1)
    
    # 配置更新（谨慎操作）
    crawler_config_id: Optional[str] = None
    backend_config_id: Optional[str] = None


class CrawlerWorkerSummary(BaseModel):
    """Worker摘要（用于列表显示）"""
    
    worker_id: str
    worker_name: str
    description: Optional[str]
    status: WorkerStatus
    priority: WorkerPriority
    
    # 配置信息
    crawler_config_name: str
    backend_config_name: str
    backend_endpoint: str
    
    # 性能指标
    allocated_concurrent: int
    current_tasks: int
    available_capacity: int
    utilization_rate: float
    success_rate: float
    health_score: float
    
    # 时间信息
    created_at: Optional[datetime]
    last_used: Optional[datetime]
    
    @property
    def is_available(self) -> bool:
        """判断是否可用"""
        return (
            self.status == WorkerStatus.ACTIVE and
            self.available_capacity > 0 and
            self.health_score >= 0.5
        )


class CrawlerWorkerDetail(BaseModel):
    """Worker详细信息（包含关联配置）"""
    
    # Worker基本信息
    worker: CrawlerWorker
    
    # 关联的配置
    crawler_config: Optional[CrawlerConfig] = None
    backend_config: Optional[BackendConfig] = None
    
    # 兼容性检查结果
    compatibility_check: Optional[Dict[str, Any]] = None
    
    @property
    def is_valid(self) -> bool:
        """判断Worker是否有效"""
        return (
            self.crawler_config is not None and
            self.backend_config is not None and
            self.backend_config.is_available
        )


class WorkerCompatibilityCheck(BaseModel):
    """Worker兼容性检查结果"""
    
    worker_id: str = Field(description="Worker ID")
    is_compatible: bool = Field(description="是否兼容")
    
    # 检查结果
    config_exists: bool = Field(description="配置是否存在")
    backend_available: bool = Field(description="后端是否可用")
    resource_conflict: bool = Field(description="是否有资源冲突")
    
    # 详细信息
    errors: List[str] = Field(default_factory=list, description="错误列表")
    warnings: List[str] = Field(default_factory=list, description="警告列表")
    suggestions: List[str] = Field(default_factory=list, description="建议列表")
    
    # 冲突的Worker列表
    conflicted_workers: List[str] = Field(default_factory=list, description="冲突的Worker ID列表")


class WorkerGroupCompatibilityCheck(BaseModel):
    """Worker组兼容性检查结果"""
    
    worker_ids: List[str] = Field(description="Worker ID列表")
    is_compatible: bool = Field(description="组是否兼容")
    
    # 检查结果
    individual_checks: List[WorkerCompatibilityCheck] = Field(description="单个Worker检查结果")
    resource_conflicts: List[Dict[str, Any]] = Field(description="资源冲突详情")
    
    # 统计信息
    total_workers: int = Field(description="总Worker数")
    valid_workers: int = Field(description="有效Worker数")
    conflicted_workers: int = Field(description="冲突Worker数")
    
    # 建议
    recommended_workers: List[str] = Field(description="推荐的Worker列表")
    alternative_workers: List[str] = Field(description="替代Worker列表")


class CrawlerWorkerStats(BaseModel):
    """Worker统计信息"""
    
    total_workers: int = Field(description="总Worker数")
    active_workers: int = Field(description="活跃Worker数")
    available_workers: int = Field(description="可用Worker数")
    busy_workers: int = Field(description="忙碌Worker数")
    
    total_capacity: int = Field(description="总容量")
    used_capacity: int = Field(description="已用容量")
    available_capacity: int = Field(description="可用容量")
    
    avg_utilization: float = Field(description="平均利用率")
    avg_success_rate: float = Field(description="平均成功率")
    avg_health_score: float = Field(description="平均健康评分")
    
    # 优先级分布
    priority_distribution: Dict[str, int] = Field(description="优先级分布")
    
    # 性能指标
    top_performers: List[CrawlerWorkerSummary] = Field(description="表现最佳的Worker")
    underperformers: List[CrawlerWorkerSummary] = Field(description="表现不佳的Worker")
    
    # 资源使用情况
    resource_usage: Dict[str, int] = Field(description="资源使用情况")
    backend_distribution: Dict[str, int] = Field(description="后端分布")
    config_distribution: Dict[str, int] = Field(description="配置分布")
