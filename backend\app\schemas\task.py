"""
爬虫任务数据模式定义
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, ConfigDict, validator

from .common import BaseSchema


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"
    CANCELLED = "cancelled"


class TaskPriority(str, Enum):
    """任务优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class CrawlTaskBase(BaseModel):
    """爬虫任务基础模式"""
    
    task_name: str = Field(
        description="任务名称",
        max_length=200
    )
    description: Optional[str] = Field(
        None,
        description="任务描述",
        max_length=500
    )
    target_urls: List[str] = Field(
        description="目标URL列表"
    )
    config_id: int = Field(
        description="配置ID",
        gt=0
    )
    priority: TaskPriority = Field(
        TaskPriority.NORMAL,
        description="任务优先级"
    )
    max_retry_count: int = Field(
        3,
        description="最大重试次数",
        ge=0,
        le=10
    )
    timeout_seconds: int = Field(
        300,
        description="超时时间（秒）",
        ge=30,
        le=3600
    )
    schedule_time: Optional[datetime] = Field(
        None,
        description="计划执行时间"
    )
    
    @validator('target_urls')
    def validate_target_urls(cls, v):
        """验证目标URL列表"""
        if not v:
            raise ValueError("目标URL列表不能为空")
        if len(v) > 1000:
            raise ValueError("目标URL数量不能超过1000个")
        return v

    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class CrawlTaskCreate(CrawlTaskBase):
    """创建爬虫任务的数据模式"""
    pass


class CrawlTaskUpdate(BaseModel):
    """更新爬虫任务的数据模式"""
    
    task_name: Optional[str] = Field(
        None,
        max_length=200
    )
    description: Optional[str] = Field(
        None,
        max_length=500
    )
    target_urls: Optional[List[str]] = None
    config_id: Optional[int] = Field(
        None,
        gt=0
    )
    priority: Optional[TaskPriority] = None
    max_retry_count: Optional[int] = Field(
        None,
        ge=0,
        le=10
    )
    timeout_seconds: Optional[int] = Field(
        None,
        ge=30,
        le=3600
    )
    schedule_time: Optional[datetime] = None
    status: Optional[TaskStatus] = None

    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class CrawlTaskResponse(BaseSchema):
    """爬虫任务响应数据模式"""
    
    id: int = Field(description="任务ID")
    task_name: str = Field(description="任务名称")
    description: Optional[str] = Field(description="任务描述")
    target_urls: List[str] = Field(description="目标URL列表")
    config_id: int = Field(description="配置ID")
    priority: TaskPriority = Field(description="任务优先级")
    status: TaskStatus = Field(description="任务状态")
    
    # 执行统计
    total_urls: int = Field(description="总URL数量")
    processed_urls: int = Field(description="已处理URL数量")
    success_count: int = Field(description="成功数量")
    failed_count: int = Field(description="失败数量")
    retry_count: int = Field(description="重试次数")
    max_retry_count: int = Field(description="最大重试次数")
    
    # 时间信息
    timeout_seconds: int = Field(description="超时时间")
    schedule_time: Optional[datetime] = Field(description="计划执行时间")
    start_time: Optional[datetime] = Field(description="开始时间")
    end_time: Optional[datetime] = Field(description="结束时间")
    
    # 执行结果
    result_data: Optional[Dict[str, Any]] = Field(description="执行结果数据")
    error_message: Optional[str] = Field(description="错误信息")

    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class CrawlConfigBase(BaseModel):
    """爬虫配置基础模式"""
    
    config_name: str = Field(
        description="配置名称",
        max_length=200
    )
    description: Optional[str] = Field(
        None,
        description="配置描述",
        max_length=500
    )
    target_site: str = Field(
        description="目标站点",
        max_length=100
    )
    
    # 请求配置
    request_headers: Optional[Dict[str, str]] = Field(
        None,
        description="请求头配置"
    )
    request_delay_min: float = Field(
        1.0,
        description="最小延迟时间（秒）",
        ge=0.1,
        le=60
    )
    request_delay_max: float = Field(
        3.0,
        description="最大延迟时间（秒）",
        ge=0.1,
        le=60
    )
    
    # 并发配置
    max_concurrent: int = Field(
        1,
        description="最大并发数",
        ge=1,
        le=10
    )
    
    # 代理配置
    enable_proxy: bool = Field(
        False,
        description="是否启用代理"
    )
    proxy_pool_id: Optional[int] = Field(
        None,
        description="代理池ID"
    )
    
    # 反爬配置
    enable_user_agent_rotation: bool = Field(
        True,
        description="是否启用User-Agent轮换"
    )
    enable_cookie_handling: bool = Field(
        True,
        description="是否启用Cookie处理"
    )
    
    # 其他配置
    extra_config: Optional[Dict[str, Any]] = Field(
        None,
        description="额外配置"
    )
    
    @validator('request_delay_max')
    def validate_delay_range(cls, v, values):
        """验证延迟时间范围"""
        min_delay = values.get('request_delay_min', 0)
        if v <= min_delay:
            raise ValueError("最大延迟时间必须大于最小延迟时间")
        return v

    model_config = ConfigDict(from_attributes=True)


class CrawlConfigCreate(CrawlConfigBase):
    """创建爬虫配置的数据模式"""
    pass


class CrawlConfigUpdate(BaseModel):
    """更新爬虫配置的数据模式"""
    
    config_name: Optional[str] = Field(
        None,
        max_length=200
    )
    description: Optional[str] = Field(
        None,
        max_length=500
    )
    target_site: Optional[str] = Field(
        None,
        max_length=100
    )
    request_headers: Optional[Dict[str, str]] = None
    request_delay_min: Optional[float] = Field(
        None,
        ge=0.1,
        le=60
    )
    request_delay_max: Optional[float] = Field(
        None,
        ge=0.1,
        le=60
    )
    max_concurrent: Optional[int] = Field(
        None,
        ge=1,
        le=10
    )
    enable_proxy: Optional[bool] = None
    proxy_pool_id: Optional[int] = None
    enable_user_agent_rotation: Optional[bool] = None
    enable_cookie_handling: Optional[bool] = None
    extra_config: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(from_attributes=True)


class CrawlConfigResponse(BaseSchema):
    """爬虫配置响应数据模式"""
    
    id: int = Field(description="配置ID")
    config_name: str = Field(description="配置名称")
    description: Optional[str] = Field(description="配置描述")
    target_site: str = Field(description="目标站点")
    
    # 请求配置
    request_headers: Optional[Dict[str, str]] = Field(description="请求头配置")
    request_delay_min: float = Field(description="最小延迟时间")
    request_delay_max: float = Field(description="最大延迟时间")
    
    # 并发配置
    max_concurrent: int = Field(description="最大并发数")
    
    # 代理配置
    enable_proxy: bool = Field(description="是否启用代理")
    proxy_pool_id: Optional[int] = Field(description="代理池ID")
    
    # 反爬配置
    enable_user_agent_rotation: bool = Field(description="是否启用User-Agent轮换")
    enable_cookie_handling: bool = Field(description="是否启用Cookie处理")
    
    # 其他配置
    extra_config: Optional[Dict[str, Any]] = Field(description="额外配置")
    
    # 状态信息
    is_active: bool = Field(description="是否激活")

    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    ) 