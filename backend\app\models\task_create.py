"""
任务创建相关的数据模型

定义从URL池创建监控任务的数据结构和验证规则
"""

from datetime import datetime, time
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, validator
from enum import Enum


class ScheduleType(str, Enum):
    """调度类型枚举"""
    ONCE = "once"           # 一次性执行
    DAILY = "daily"         # 每日执行
    WEEKLY = "weekly"       # 每周执行
    HOURLY = "hourly"       # 每小时执行
    CUSTOM = "custom"       # 自定义cron表达式


class TaskPriority(str, Enum):
    """任务优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class ScheduleConfig(BaseModel):
    """调度配置"""
    type: ScheduleType = Field(..., description="调度类型")
    enabled: bool = Field(default=True, description="是否启用调度")
    time: Optional[str] = Field(None, description="执行时间(HH:MM格式)")
    start_time: Optional[str] = Field(None, description="开始时间，可以是ISO格式或时间格式")
    days: Optional[List[int]] = Field(None, description="周几执行，1-7表示周一到周日")
    interval: Optional[int] = Field(None, description="间隔时间(分钟)")
    cron_expression: Optional[str] = Field(None, description="Cron表达式")
    timezone: str = Field(default="Asia/Shanghai", description="时区")
    end_time: Optional[str] = Field(None, description="结束时间，格式：HH:MM")
    max_runs: Optional[int] = Field(None, description="最大执行次数")

    # 随机启动范围配置
    enable_random_delay: bool = Field(default=False, description="是否启用随机延迟")
    random_delay_min: Optional[int] = Field(default=0, description="最小随机延迟(分钟)")
    random_delay_max: Optional[int] = Field(default=180, description="最大随机延迟(分钟)")
    
    @validator('time')
    def validate_time(cls, v, values):
        """验证时间格式"""
        if v is not None:
            try:
                # 验证时间格式 HH:MM
                time_parts = v.split(':')
                if len(time_parts) != 2:
                    raise ValueError('时间格式必须为HH:MM')

                hour = int(time_parts[0])
                minute = int(time_parts[1])

                if not (0 <= hour <= 23):
                    raise ValueError('小时必须在0-23之间')
                if not (0 <= minute <= 59):
                    raise ValueError('分钟必须在0-59之间')

            except (ValueError, IndexError) as e:
                raise ValueError(f'无效的时间格式: {v}，应为HH:MM格式')
        return v

    @validator('cron_expression')
    def validate_cron_expression(cls, v, values):
        """验证cron表达式"""
        if values.get('type') == ScheduleType.CUSTOM and not v:
            raise ValueError('自定义调度类型必须提供cron表达式')
        return v
    
    @validator('interval')
    def validate_interval(cls, v, values):
        """验证间隔时间"""
        schedule_type = values.get('type')
        if schedule_type == ScheduleType.HOURLY and v and v < 1:
            raise ValueError('小时间隔不能小于1分钟')
        return v

    @validator('random_delay_max')
    def validate_random_delay_range(cls, v, values):
        """验证随机延迟范围"""
        # 如果没有启用随机延迟，确保返回默认值
        if not values.get('enable_random_delay', False):
            return 180 if v is None else v

        # 如果启用了随机延迟，进行范围验证
        min_delay = values.get('random_delay_min', 0)
        if v is None:
            v = 180  # 默认3小时
        if min_delay is None:
            min_delay = 0

        if v <= min_delay:
            raise ValueError('最大随机延迟必须大于最小随机延迟')
        if v > 1440:  # 最大24小时
            raise ValueError('最大随机延迟不能超过1440分钟(24小时)')
        return v

    @validator('random_delay_min')
    def validate_random_delay_min(cls, v, values):
        """验证最小随机延迟"""
        if v is None:
            return 0
        if v < 0:
            raise ValueError('最小随机延迟不能小于0')
        if v > 1440:
            raise ValueError('最小随机延迟不能超过1440分钟(24小时)')
        return v


class TaskConfig(BaseModel):
    """任务配置"""
    platform: str = Field(..., description="目标平台")
    priority: TaskPriority = Field(default=TaskPriority.NORMAL, description="任务优先级")
    retry_count: int = Field(default=3, description="重试次数")
    timeout: int = Field(default=300, description="超时时间(秒)")
    concurrent_limit: int = Field(default=5, description="并发限制")
    batch_size: int = Field(default=10, description="批次大小")
    enable_notifications: bool = Field(default=True, description="启用通知")
    notification_config: Optional[Dict[str, Any]] = Field(None, description="通知配置")
    
    @validator('retry_count')
    def validate_retry_count(cls, v):
        if v < 0 or v > 10:
            raise ValueError('重试次数必须在0-10之间')
        return v
    
    @validator('timeout')
    def validate_timeout(cls, v):
        if v < 30 or v > 3600:
            raise ValueError('超时时间必须在30-3600秒之间')
        return v
    
    @validator('concurrent_limit')
    def validate_concurrent_limit(cls, v):
        if v < 1 or v > 50:
            raise ValueError('并发限制必须在1-50之间')
        return v


class TaskCreateFromUrlsRequest(BaseModel):
    """从URL池创建任务请求"""
    name: str = Field(..., min_length=1, max_length=100, description="任务名称")
    description: Optional[str] = Field(None, max_length=500, description="任务描述")
    url_ids: List[str] = Field(..., min_items=1, max_items=1000, description="URL ID列表")
    schedule: ScheduleConfig = Field(..., description="调度配置")
    config: TaskConfig = Field(..., description="任务配置")
    tags: Optional[List[str]] = Field(None, description="任务标签")
    
    @validator('url_ids')
    def validate_url_ids(cls, v):
        if not v:
            raise ValueError('必须选择至少一个URL')
        if len(set(v)) != len(v):
            raise ValueError('URL ID列表中不能有重复项')
        return v


class TaskCreateResponse(BaseModel):
    """任务创建响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    task_id: str = Field(..., description="创建的任务ID")
    task_name: str = Field(..., description="任务名称")
    url_count: int = Field(..., description="关联的URL数量")
    schedule_info: str = Field(..., description="调度信息描述")
    next_run: Optional[datetime] = Field(None, description="下次执行时间")


class UrlPreviewRequest(BaseModel):
    """URL预览请求"""
    url_ids: List[str] = Field(..., min_items=1, max_items=1000, description="URL ID列表")


class UrlPreviewResponse(BaseModel):
    """URL预览响应"""
    success: bool = Field(..., description="是否成功")
    total_count: int = Field(..., description="总URL数量")
    valid_count: int = Field(..., description="有效URL数量")
    invalid_count: int = Field(..., description="无效URL数量")
    platform_distribution: Dict[str, int] = Field(..., description="平台分布")
    urls: List[Dict[str, Any]] = Field(..., description="URL详细信息")


class TaskValidationRequest(BaseModel):
    """任务验证请求"""
    name: str = Field(..., description="任务名称")
    url_ids: List[str] = Field(..., description="URL ID列表")
    schedule: ScheduleConfig = Field(..., description="调度配置")


class TaskValidationResponse(BaseModel):
    """任务验证响应"""
    success: bool = Field(..., description="验证是否通过")
    errors: List[str] = Field(default_factory=list, description="错误列表")
    warnings: List[str] = Field(default_factory=list, description="警告列表")
    suggestions: List[str] = Field(default_factory=list, description="建议列表")


class TaskTemplate(BaseModel):
    """任务模板"""
    id: str = Field(..., description="模板ID")
    name: str = Field(..., description="模板名称")
    description: Optional[str] = Field(None, description="模板描述")
    platform: str = Field(..., description="目标平台")
    default_schedule: ScheduleConfig = Field(..., description="默认调度配置")
    default_config: TaskConfig = Field(..., description="默认任务配置")
    created_at: datetime = Field(..., description="创建时间")
    usage_count: int = Field(default=0, description="使用次数")


class TaskCreateStats(BaseModel):
    """任务创建统计"""
    total_tasks: int = Field(..., description="总任务数")
    active_tasks: int = Field(..., description="活跃任务数")
    scheduled_tasks: int = Field(..., description="已调度任务数")
    platform_distribution: Dict[str, int] = Field(..., description="平台分布")
    recent_tasks: List[Dict[str, Any]] = Field(..., description="最近创建的任务")


# 调度类型显示名称映射
SCHEDULE_TYPE_DISPLAY_NAMES = {
    ScheduleType.ONCE: "一次性执行",
    ScheduleType.DAILY: "每日执行",
    ScheduleType.WEEKLY: "每周执行", 
    ScheduleType.HOURLY: "每小时执行",
    ScheduleType.CUSTOM: "自定义调度"
}

# 任务优先级显示名称映射
TASK_PRIORITY_DISPLAY_NAMES = {
    TaskPriority.LOW: "低优先级",
    TaskPriority.NORMAL: "普通优先级",
    TaskPriority.HIGH: "高优先级",
    TaskPriority.URGENT: "紧急优先级"
}


def get_schedule_display_name(schedule_type: ScheduleType) -> str:
    """获取调度类型显示名称"""
    return SCHEDULE_TYPE_DISPLAY_NAMES.get(schedule_type, schedule_type.value)


def get_priority_display_name(priority: TaskPriority) -> str:
    """获取优先级显示名称"""
    return TASK_PRIORITY_DISPLAY_NAMES.get(priority, priority.value)


def generate_schedule_description(schedule: ScheduleConfig) -> str:
    """生成调度描述"""
    if not schedule.enabled:
        return "调度已禁用"
    
    if schedule.type == ScheduleType.ONCE:
        if schedule.start_time:
            return f"一次性执行，时间：{schedule.start_time.strftime('%Y-%m-%d %H:%M')}"
        return "一次性执行"
    
    elif schedule.type == ScheduleType.DAILY:
        if schedule.start_time:
            return f"每日执行，时间：{schedule.start_time.strftime('%H:%M')}"
        return "每日执行"
    
    elif schedule.type == ScheduleType.WEEKLY:
        if schedule.start_time:
            weekday = schedule.start_time.strftime('%A')
            time_str = schedule.start_time.strftime('%H:%M')
            return f"每周执行，时间：{weekday} {time_str}"
        return "每周执行"
    
    elif schedule.type == ScheduleType.HOURLY:
        if schedule.interval:
            return f"每{schedule.interval}分钟执行一次"
        return "每小时执行"
    
    elif schedule.type == ScheduleType.CUSTOM:
        if schedule.cron_expression:
            return f"自定义调度：{schedule.cron_expression}"
        return "自定义调度"
    
    return "未知调度类型"
