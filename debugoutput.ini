index.tsx:195 === 表单验证调试信息 ===
index.tsx:197 验证前的所有表单字段值: {
  "api": {
    "base_url": "http://localhost:11234",
    "timeout": 30000,
    "max_retries": 3
  }
}
index.tsx:201 获取的所有表单字段值: {
  "api": {
    "base_url": "http://localhost:11234",
    "timeout": 30000,
    "max_retries": 3
  }
}
index.tsx:205 必需字段验证通过
index.tsx:206 === 表单验证调试信息结束 ===
index.tsx:271 === 传统爬虫设置页面 - 保存配置调试信息 ===
index.tsx:272 1. 原始表单数据 (values): {
  "api": {
    "base_url": "http://localhost:11234",
    "timeout": 30000,
    "max_retries": 3
  }
}
index.tsx:273 2. 处理后的配置数据 (configToSave): {
  "api": {
    "base_url": "http://localhost:11234",
    "timeout": 30000,
    "max_retries": 3
  },
  "llm": {
    "api_key": "sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi"
  },
  "schema_extraction": {
    "extraction_schema": {}
  },
  "browser": {},
  "crawler": {},
  "content_processing": {},
  "link_filtering": {},
  "scheduler": {},
  "monitor": {}
}
index.tsx:274 3. 配置数据大小: 292 字符
index.tsx:276 4. 详细配置节点分析:
index.tsx:277 - API配置: {
  "base_url": "http://localhost:11234",
  "timeout": 30000,
  "max_retries": 3
}
index.tsx:278 - 浏览器配置: {}
index.tsx:279 - 爬虫配置: {}
index.tsx:280 - LLM配置 (API密钥已隐藏): {
  "api_key": "***HUhi"
}
index.tsx:284 - Schema提取配置: {
  "extraction_schema": {}
}
index.tsx:285 - 内容处理配置: {}
index.tsx:286 - 链接过滤配置: {}
index.tsx:287 - 调度器配置: {}
index.tsx:288 - 监控配置: {}
index.tsx:290 5. 配置节点是否为空对象检查:
index.tsx:291 - API配置是否为空: false
index.tsx:292 - 浏览器配置是否为空: true
index.tsx:293 - 爬虫配置是否为空: true
index.tsx:294 - LLM配置是否为空: false
index.tsx:295 - Schema提取配置是否为空: false
index.tsx:296 - 内容处理配置是否为空: true
index.tsx:297 - 链接过滤配置是否为空: true
index.tsx:298 - 调度器配置是否为空: true
index.tsx:299 - 监控配置是否为空: true
index.tsx:300 === 调试信息结束 ===
