react-dom-client.development.js:13848 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
dynamicCSS.js:74  [Deprecation]-ms-high-contrast is in the process of being deprecated. Please see https://blogs.windows.com/msedgedev/2024/04/29/deprecating-ms-high-contrast/ for tips on updating to the new Forced Colors Mode standard.
injectCSS @ dynamicCSS.js:74
dynamicCSS.js:74  [Deprecation]-ms-high-contrast is in the process of being deprecated. Please see https://blogs.windows.com/msedgedev/2024/04/29/deprecating-ms-high-contrast/ for tips on updating to the new Forced Colors Mode standard.
injectCSS @ dynamicCSS.js:74
websocket.ts:42 WebSocket connected
index.tsx:95 === 配置加载调试信息 ===
index.tsx:96 从后端加载的原始配置: {
  "api": {
    "base_url": "http://localhost:11235",
    "timeout": 30000,
    "max_retries": 3
  },
  "browser": {
    "headless": true,
    "verbose": false,
    "viewport_width": 1920,
    "viewport_height": 1080,
    "wait_for": 2,
    "timeout": 30,
    "ignore_https_errors": true,
    "extra_args": [
      "--no-sandbox",
      "--disable-dev-shm-usage",
      "--disable-blink-features=AutomationControlled",
      "--exclude-switches=enable-automation"
    ]
  },
  "crawler": {
    "method": "arun_many",
    "verbose": true,
    "check_robots_txt": false,
    "fetch_ssl_certificate": false,
    "simulate_user": true,
    "magic": true,
    "override_navigator": true,
    "remove_overlay_elements": true,
    "ignore_body_visibility": true,
    "adjust_viewport_to_content": true,
    "wait_until": "domcontentloaded",
    "wait_for_images": false,
    "page_timeout": 60000,
    "delay_before_return_html": 0.1,
    "js_only": false,
    "scan_full_page": true,
    "process_iframes": true,
    "scroll_delay": 0.2,
    "cache_mode": "BYPASS",
    "screenshot": false,
    "pdf": false,
    "capture_mhtml": false,
    "exclude_external_images": false,
    "exclude_all_images": false,
    "image_score_threshold": 50,
    "image_description_min_word_threshold": 50,
    "table_score_threshold": 7,
    "capture_network_requests": false,
    "capture_console_messages": false,
    "log_console": false,
    "extraction_strategy": "LLMExtractionStrategy",
    "chunking_strategy": "IdentityChunking",
    "markdown_generator": "DefaultMarkdownGenerator",
    "bypass_cache": true
  },
  "llm": {
    "query": "提取页面中所有文章的标题、作者和发布时间",
    "provider": "openai",
    "model": "deepseek-v3-0324",
    "api_key": "***HUhi",
    "base_url": "https://api.lkeap.cloud.tencent.com/v1",
    "temperature": 0,
    "max_tokens": 16384,
    "top_p": 0.9
  },
  "schema_extraction": {
    "extraction_schema": {
      "type": "object",
      "description": "电商商品完整信息提取结构",
      "properties": {
        "task_info": {
          "type": "object",
          "description": "任务执行信息",
          "properties": {
            "start_timestamp": {
              "type": "string",
              "pattern": "^[0-9]{14}$",
              "description": "任务开始的时间戳，格式：YYYYMMDDHHMMSS"
            }
          },
          "required": [
            "start_timestamp"
          ]
        },
        "product_basic_info": {
          "type": "object",
          "description": "商品基础信息",
          "properties": {
            "product_url": {
              "type": "string",
              "format": "uri",
              "description": "商品链接"
            },
            "product_name": {
              "type": "string",
              "description": "商品名称"
            },
            "mlm_id": {
              "type": "number",
              "description": "商品MLM-ID中的数值部分"
            }
          },
          "required": [
            "product_url",
            "product_name",
            "mlm_id"
          ]
        },
        "pricing_info": {
          "type": "object",
          "description": "价格和库存信息",
          "properties": {
            "sales_count": {
              "type": "number",
              "description": "商品销量（格式：+xxx vendidos中的数值）"
            },
            "current_price": {
              "type": "number",
              "description": "商品现价（数值）"
            },
            "original_price": {
              "type": "number",
              "description": "商品原价（数值），如果只有一个价格，或者没打折，商品现价（数值）就是商品原价"
            },
            "discount_rate": {
              "type": "number",
              "description": "商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100"
            },
            "stock_quantity": {
              "type": "number",
              "description": "商品库存数量，(+50 disponibles) 中的数值，如果页面显示Publicación pausada或者没有获取到(+50 disponibles)中的数值，那库存数量就是0"
            },
            "stocktype_IsFull": {
              "type": "number",
              "description": "商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\":\"Full\"，商品有就是1，没有就是0"
            }
          },
          "required": [
            "current_price"
          ]
        },
        "category_info": {
          "type": "object",
          "description": "商品目录分类信息",
          "properties": {
            "category_breadcrumb": {
              "type": "string",
              "description": "商品各级目录文本（例如：Herramientas > Cajas y Organizadores > Bolsas Portaherramientas）"
            },
            "category_hierarchy": {
              "type": "array",
              "description": "商品各级目录详细信息",
              "items": {
                "type": "object",
                "properties": {
                  "category_name": {
                    "type": "string",
                    "description": "目录名称"
                  },
                  "category_url": {
                    "type": "string",
                    "format": "uri",
                  
index.tsx:97 配置节点检查:
index.tsx:98 - API配置: Object
index.tsx:99 - 浏览器配置: Object
index.tsx:100 - 爬虫配置: Object
index.tsx:101 - LLM配置: Object
index.tsx:102 - Schema提取配置: Object
index.tsx:103 - 内容处理配置: Object
index.tsx:104 - 链接过滤配置: Object
index.tsx:105 - 调度器配置: Object
index.tsx:106 - 监控配置: Object
index.tsx:117 处理后的表单值: {
  "api": {
    "base_url": "http://localhost:11235",
    "timeout": 30000,
    "max_retries": 3
  },
  "browser": {
    "headless": true,
    "verbose": false,
    "viewport_width": 1920,
    "viewport_height": 1080,
    "wait_for": 2,
    "timeout": 30,
    "ignore_https_errors": true,
    "extra_args": [
      "--no-sandbox",
      "--disable-dev-shm-usage",
      "--disable-blink-features=AutomationControlled",
      "--exclude-switches=enable-automation"
    ]
  },
  "crawler": {
    "method": "arun_many",
    "verbose": true,
    "check_robots_txt": false,
    "fetch_ssl_certificate": false,
    "simulate_user": true,
    "magic": true,
    "override_navigator": true,
    "remove_overlay_elements": true,
    "ignore_body_visibility": true,
    "adjust_viewport_to_content": true,
    "wait_until": "domcontentloaded",
    "wait_for_images": false,
    "page_timeout": 60000,
    "delay_before_return_html": 0.1,
    "js_only": false,
    "scan_full_page": true,
    "process_iframes": true,
    "scroll_delay": 0.2,
    "cache_mode": "BYPASS",
    "screenshot": false,
    "pdf": false,
    "capture_mhtml": false,
    "exclude_external_images": false,
    "exclude_all_images": false,
    "image_score_threshold": 50,
    "image_description_min_word_threshold": 50,
    "table_score_threshold": 7,
    "capture_network_requests": false,
    "capture_console_messages": false,
    "log_console": false,
    "extraction_strategy": "LLMExtractionStrategy",
    "chunking_strategy": "IdentityChunking",
    "markdown_generator": "DefaultMarkdownGenerator",
    "bypass_cache": true
  },
  "llm": {
    "query": "提取页面中所有文章的标题、作者和发布时间",
    "provider": "openai",
    "model": "deepseek-v3-0324",
    "api_key": "***HUhi",
    "base_url": "https://api.lkeap.cloud.tencent.com/v1",
    "temperature": 0,
    "max_tokens": 16384,
    "top_p": 0.9
  },
  "schema_extraction": {
    "extraction_schema": "{\n  \"type\": \"object\",\n  \"description\": \"电商商品完整信息提取结构\",\n  \"properties\": {\n    \"task_info\": {\n      \"type\": \"object\",\n      \"description\": \"任务执行信息\",\n      \"properties\": {\n        \"start_timestamp\": {\n          \"type\": \"string\",\n          \"pattern\": \"^[0-9]{14}$\",\n          \"description\": \"任务开始的时间戳，格式：YYYYMMDDHHMMSS\"\n        }\n      },\n      \"required\": [\n        \"start_timestamp\"\n      ]\n    },\n    \"product_basic_info\": {\n      \"type\": \"object\",\n      \"description\": \"商品基础信息\",\n      \"properties\": {\n        \"product_url\": {\n          \"type\": \"string\",\n          \"format\": \"uri\",\n          \"description\": \"商品链接\"\n        },\n        \"product_name\": {\n          \"type\": \"string\",\n          \"description\": \"商品名称\"\n        },\n        \"mlm_id\": {\n          \"type\": \"number\",\n          \"description\": \"商品MLM-ID中的数值部分\"\n        }\n      },\n      \"required\": [\n        \"product_url\",\n        \"product_name\",\n        \"mlm_id\"\n      ]\n    },\n    \"pricing_info\": {\n      \"type\": \"object\",\n      \"description\": \"价格和库存信息\",\n      \"properties\": {\n        \"sales_count\": {\n          \"type\": \"number\",\n          \"description\": \"商品销量（格式：+xxx vendidos中的数值）\"\n        },\n        \"current_price\": {\n          \"type\": \"number\",\n          \"description\": \"商品现价（数值）\"\n        },\n        \"original_price\": {\n          \"type\": \"number\",\n          \"description\": \"商品原价（数值），如果只有一个价格，或者没打折，商品现价（数值）就是商品原价\"\n        },\n        \"discount_rate\": {\n          \"type\": \"number\",\n          \"description\": \"商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100\"\n        },\n        \"stock_quantity\": {\n          \"type\": \"number\",\n          \"description\": \"商品库存数量，(+50 disponibles) 中的数值，如果页面显示Publicación pausada或者没有获取到(+50 disponibles)中的数值，那库存数量就是0\"\n        },\n        \"stocktype_IsFull\": {\n          \"type\": \"number\",\n          \"description\": \"商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\\":\\\"Full\\\"，商品有就是1，没有就是0\"\n        }\n      },\n      \"required\": [\n        \"current_price\"\n      ]\n    },\n    \"category_info\": {\n      \"type\": \"object\",\n      \"description\": \"商品目录分类信息\",\n      \"properties\": {\n        \"category_breadcrumb\": {\n          \"type\": \"string\",\n          \"description\": \"商品各级目录文本（例如：Herramientas > Cajas y Organizadores > Bolsas Portaherramientas）\"\n        },\n        \"category_hierarchy\": {\n          \"type\": \"array\",\n          \"description\": \"商品各级目录详细信息\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"category_name\": {\n                \"type\": \"string\",\n                \"description\": \"目录名称\"\n              },\n              \"category_url\": {\n                \"type\": \"string\",\n                \"format\": \"uri\",\n                \"description\": \"目录链接\"\n              },\n    
index.tsx:121 表单值设置完成
index.tsx:125 === 表单值检查 ===
index.tsx:126 检查结果: Objectapi_exists: trueapi_keys: 3browser_exists: falsebrowser_keys: 0crawler_exists: falsecrawler_keys: 0[[Prototype]]: Object
index.tsx:134 === 配置加载调试信息结束 ===
crawler-settings:1  [Deprecation]-ms-high-contrast is in the process of being deprecated. Please see https://blogs.windows.com/msedgedev/2024/04/29/deprecating-ms-high-contrast/ for tips on updating to the new Forced Colors Mode standard.
crawler-settings:1  [Deprecation]-ms-high-contrast is in the process of being deprecated. Please see https://blogs.windows.com/msedgedev/2024/04/29/deprecating-ms-high-contrast/ for tips on updating to the new Forced Colors Mode standard.
[新] 使用 Edge 中的 Copilot 来解释控制台错误: 单击
         
         以说明错误。
        了解更多信息
        不再显示
