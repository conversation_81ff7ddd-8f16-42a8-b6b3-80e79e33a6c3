index.tsx:195 === 表单验证调试信息 ===
index.tsx:197 验证前的所有表单字段值: {
  "api": {
    "base_url": "http://localhost:11234",
    "timeout": 30000,
    "max_retries": 3
  },
  "browser": {
    "headless": true,
    "verbose": false,
    "ignore_https_errors": true,
    "viewport_width": 1920,
    "viewport_height": 1080,
    "wait_for": 2,
    "timeout": 30,
    "extra_args": [
      "--no-sandbox",
      "--disable-dev-shm-usage",
      "--disable-blink-features=AutomationControlled",
      "--exclude-switches=enable-automation"
    ]
  },
  "llm": {
    "query": "提取页面中所有文章的标题、作者和发布时间",
    "provider": "openai",
    "model": "deepseek-v3-0324",
    "base_url": "https://api.lkeap.cloud.tencent.com/v1",
    "api_key": "sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi",
    "temperature": 0,
    "max_tokens": 16384,
    "top_p": 0.9
  },
  "scheduler": {
    "semaphore_count": 2,
    "mean_delay": 0.1,
    "max_range": 3,
    "pool_size": 10,
    "memory_threshold": 4096
  },
  "monitor": {
    "display_mode": "detailed",
    "show_progress": true,
    "log_errors": true
  },
  "crawler": {
    "simulate_user": true,
    "magic": true,
    "override_navigator": true,
    "remove_overlay_elements": true,
    "scan_full_page": true,
    "process_iframes": true,
    "page_timeout": 60000,
    "delay_before_return_html": 0.1,
    "scroll_delay": 0.2,
    "wait_until": "domcontentloaded",
    "image_score_threshold": 50,
    "table_score_threshold": 7,
    "cache_mode": "BYPASS",
    "check_robots_txt": false,
    "fetch_ssl_certificate": false,
    "bypass_cache": true,
    "extraction_strategy": "LLMExtractionStrategy",
    "chunking_strategy": "IdentityChunking",
    "markdown_generator": "DefaultMarkdownGenerator"
  },
  "schema_extraction": {
    "instructions": "请严格按照以下条件提取商品信息：\n1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品\n2. 如果页面有多个商品，只提取最突出显示的主商品\n3. 重点关注页面标题中提到的商品\n4. 忽略广告推荐和次要商品信息\n5. 确保提取的商品名称与页面URL或页面标题相匹配\n6. 确保提取的商品mlmid与页面URL的mlmid相匹配\n\n需要提取的信息如下：\n商品链接、商品名称、商品MLM-ID；\n商品销量（格式  +xxx vendidos）、商品原价、商品现价、商品折扣率、商品库存数量、商品是否属于FULL仓配送商品(商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\\":\\\"Full\\\"，商品有就是1，没有就是0)；\n获取商品的各级目录文本（例如 Herramientas> Cajas y Organizadores > Bolsas Portaherramientas）、获取商品各级目录的链接；\n商品销售商名称、商品销售商链接；\n商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）；\n商品页面的问题（Preguntas y respuestas）相关的内容、问题时间、问题回答；\n商品所有问题页面的链接（Ver todas las preguntas，类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接）；\n商品评分数、商品评分数量、\n商品的评论内容、评论评分、评论时间；\n商品所有评论页面的链接（Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接）；\n任务开始的时间戳YYYYMMDDHHMMSS",
    "validate_schema": true,
    "return_raw": false,
    "extraction_schema": "{\n  \"type\": \"object\",\n  \"description\": \"电商商品完整信息提取结构\",\n  \"properties\": {\n    \"task_info\": {\n      \"type\": \"object\",\n      \"description\": \"任务执行信息\",\n      \"properties\": {\n        \"start_timestamp\": {\n          \"type\": \"string\",\n          \"pattern\": \"^[0-9]{14}$\",\n          \"description\": \"任务开始的时间戳，格式：YYYYMMDDHHMMSS\"\n        }\n      },\n      \"required\": [\n        \"start_timestamp\"\n      ]\n    },\n    \"product_basic_info\": {\n      \"type\": \"object\",\n      \"description\": \"商品基础信息\",\n      \"properties\": {\n        \"product_url\": {\n          \"type\": \"string\",\n          \"format\": \"uri\",\n          \"description\": \"商品链接\"\n        },\n        \"product_name\": {\n          \"type\": \"string\",\n          \"description\": \"商品名称\"\n        },\n        \"mlm_id\": {\n          \"type\": \"number\",\n          \"description\": \"商品MLM-ID中的数值部分\"\n        }\n      },\n      \"required\": [\n        \"product_url\",\n        \"product_name\",\n        \"mlm_id\"\n      ]\n    },\n    \"pricing_info\": {\n      \"type\": \"object\",\n      \"description\": \"价格和库存信息\",\n      \"properties\": {\n        \"sales_count\": {\n          \"type\": \"number\",\n          \"description\": \"商品销量（格式：+xxx vendidos中的数值）\"\n        },\n        \"current_price\": {\n          \"type\": \"number\",\n          \"description\": \"商品现价（数值）\"\n        },\n        \"original_price\": {\n          \"type\": \"number\",\n          \"description\": \"商品原价（数值），如果只有一个价格，或者没打折，商品现价（数值）就是商品原价\"\n        },\n        \"discount_rate\": {\n          \"type\": \"number\",\n          \"description\": \"商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100\"\n        },\n        \"stock_quantity\": {\n          \"type\": \"number\",\n          \"description\": \"商品库存数量，(+50 disponibles) 中的数值，如果页面显示Publicación pausada或者没有获取到(+50 disponibles)中的数值，那库存数量就是0\"\n        },\n        \"stocktype_IsFull\": {\n          \"type\": \"nu
index.tsx:200 验证后的表单字段值: {
  "api": {
    "base_url": "http://localhost:11234",
    "timeout": 30000,
    "max_retries": 3
  },
  "browser": {
    "headless": true,
    "verbose": false,
    "ignore_https_errors": true,
    "viewport_width": 1920,
    "viewport_height": 1080,
    "wait_for": 2,
    "timeout": 30,
    "extra_args": [
      "--no-sandbox",
      "--disable-dev-shm-usage",
      "--disable-blink-features=AutomationControlled",
      "--exclude-switches=enable-automation"
    ]
  },
  "llm": {
    "query": "提取页面中所有文章的标题、作者和发布时间",
    "provider": "openai",
    "model": "deepseek-v3-0324",
    "base_url": "https://api.lkeap.cloud.tencent.com/v1",
    "api_key": "sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi",
    "temperature": 0,
    "max_tokens": 16384,
    "top_p": 0.9
  },
  "scheduler": {
    "semaphore_count": 2,
    "mean_delay": 0.1,
    "max_range": 3,
    "pool_size": 10,
    "memory_threshold": 4096
  },
  "monitor": {
    "display_mode": "detailed",
    "show_progress": true,
    "log_errors": true
  },
  "crawler": {
    "simulate_user": true,
    "magic": true,
    "override_navigator": true,
    "remove_overlay_elements": true,
    "scan_full_page": true,
    "process_iframes": true,
    "page_timeout": 60000,
    "delay_before_return_html": 0.1,
    "scroll_delay": 0.2,
    "wait_until": "domcontentloaded",
    "image_score_threshold": 50,
    "table_score_threshold": 7,
    "cache_mode": "BYPASS",
    "check_robots_txt": false,
    "fetch_ssl_certificate": false,
    "bypass_cache": true,
    "extraction_strategy": "LLMExtractionStrategy",
    "chunking_strategy": "IdentityChunking",
    "markdown_generator": "DefaultMarkdownGenerator"
  },
  "schema_extraction": {
    "instructions": "请严格按照以下条件提取商品信息：\n1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品\n2. 如果页面有多个商品，只提取最突出显示的主商品\n3. 重点关注页面标题中提到的商品\n4. 忽略广告推荐和次要商品信息\n5. 确保提取的商品名称与页面URL或页面标题相匹配\n6. 确保提取的商品mlmid与页面URL的mlmid相匹配\n\n需要提取的信息如下：\n商品链接、商品名称、商品MLM-ID；\n商品销量（格式  +xxx vendidos）、商品原价、商品现价、商品折扣率、商品库存数量、商品是否属于FULL仓配送商品(商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\\":\\\"Full\\\"，商品有就是1，没有就是0)；\n获取商品的各级目录文本（例如 Herramientas> Cajas y Organizadores > Bolsas Portaherramientas）、获取商品各级目录的链接；\n商品销售商名称、商品销售商链接；\n商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）；\n商品页面的问题（Preguntas y respuestas）相关的内容、问题时间、问题回答；\n商品所有问题页面的链接（Ver todas las preguntas，类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接）；\n商品评分数、商品评分数量、\n商品的评论内容、评论评分、评论时间；\n商品所有评论页面的链接（Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接）；\n任务开始的时间戳YYYYMMDDHHMMSS",
    "validate_schema": true,
    "return_raw": false,
    "extraction_schema": "{\n  \"type\": \"object\",\n  \"description\": \"电商商品完整信息提取结构\",\n  \"properties\": {\n    \"task_info\": {\n      \"type\": \"object\",\n      \"description\": \"任务执行信息\",\n      \"properties\": {\n        \"start_timestamp\": {\n          \"type\": \"string\",\n          \"pattern\": \"^[0-9]{14}$\",\n          \"description\": \"任务开始的时间戳，格式：YYYYMMDDHHMMSS\"\n        }\n      },\n      \"required\": [\n        \"start_timestamp\"\n      ]\n    },\n    \"product_basic_info\": {\n      \"type\": \"object\",\n      \"description\": \"商品基础信息\",\n      \"properties\": {\n        \"product_url\": {\n          \"type\": \"string\",\n          \"format\": \"uri\",\n          \"description\": \"商品链接\"\n        },\n        \"product_name\": {\n          \"type\": \"string\",\n          \"description\": \"商品名称\"\n        },\n        \"mlm_id\": {\n          \"type\": \"number\",\n          \"description\": \"商品MLM-ID中的数值部分\"\n        }\n      },\n      \"required\": [\n        \"product_url\",\n        \"product_name\",\n        \"mlm_id\"\n      ]\n    },\n    \"pricing_info\": {\n      \"type\": \"object\",\n      \"description\": \"价格和库存信息\",\n      \"properties\": {\n        \"sales_count\": {\n          \"type\": \"number\",\n          \"description\": \"商品销量（格式：+xxx vendidos中的数值）\"\n        },\n        \"current_price\": {\n          \"type\": \"number\",\n          \"description\": \"商品现价（数值）\"\n        },\n        \"original_price\": {\n          \"type\": \"number\",\n          \"description\": \"商品原价（数值），如果只有一个价格，或者没打折，商品现价（数值）就是商品原价\"\n        },\n        \"discount_rate\": {\n          \"type\": \"number\",\n          \"description\": \"商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100\"\n        },\n        \"stock_quantity\": {\n          \"type\": \"number\",\n          \"description\": \"商品库存数量，(+50 disponibles) 中的数值，如果页面显示Publicación pausada或者没有获取到(+50 disponibles)中的数值，那库存数量就是0\"\n        },\n        \"stocktype_IsFull\": {\n          \"type\": \"nu
index.tsx:201 === 表单验证调试信息结束 ===
index.tsx:266 === 传统爬虫设置页面 - 保存配置调试信息 ===
index.tsx:267 1. 原始表单数据 (values): {
  "api": {
    "base_url": "http://localhost:11234",
    "timeout": 30000,
    "max_retries": 3
  },
  "browser": {
    "headless": true,
    "verbose": false,
    "ignore_https_errors": true,
    "viewport_width": 1920,
    "viewport_height": 1080,
    "wait_for": 2,
    "timeout": 30,
    "extra_args": [
      "--no-sandbox",
      "--disable-dev-shm-usage",
      "--disable-blink-features=AutomationControlled",
      "--exclude-switches=enable-automation"
    ]
  },
  "llm": {
    "query": "提取页面中所有文章的标题、作者和发布时间",
    "provider": "openai",
    "model": "deepseek-v3-0324",
    "base_url": "https://api.lkeap.cloud.tencent.com/v1",
    "api_key": "sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi",
    "temperature": 0,
    "max_tokens": 16384,
    "top_p": 0.9
  },
  "scheduler": {
    "semaphore_count": 2,
    "mean_delay": 0.1,
    "max_range": 3,
    "pool_size": 10,
    "memory_threshold": 4096
  },
  "monitor": {
    "display_mode": "detailed",
    "show_progress": true,
    "log_errors": true
  },
  "crawler": {
    "simulate_user": true,
    "magic": true,
    "override_navigator": true,
    "remove_overlay_elements": true,
    "scan_full_page": true,
    "process_iframes": true,
    "page_timeout": 60000,
    "delay_before_return_html": 0.1,
    "scroll_delay": 0.2,
    "wait_until": "domcontentloaded",
    "image_score_threshold": 50,
    "table_score_threshold": 7,
    "cache_mode": "BYPASS",
    "check_robots_txt": false,
    "fetch_ssl_certificate": false,
    "bypass_cache": true,
    "extraction_strategy": "LLMExtractionStrategy",
    "chunking_strategy": "IdentityChunking",
    "markdown_generator": "DefaultMarkdownGenerator"
  },
  "schema_extraction": {
    "instructions": "请严格按照以下条件提取商品信息：\n1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品\n2. 如果页面有多个商品，只提取最突出显示的主商品\n3. 重点关注页面标题中提到的商品\n4. 忽略广告推荐和次要商品信息\n5. 确保提取的商品名称与页面URL或页面标题相匹配\n6. 确保提取的商品mlmid与页面URL的mlmid相匹配\n\n需要提取的信息如下：\n商品链接、商品名称、商品MLM-ID；\n商品销量（格式  +xxx vendidos）、商品原价、商品现价、商品折扣率、商品库存数量、商品是否属于FULL仓配送商品(商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\\":\\\"Full\\\"，商品有就是1，没有就是0)；\n获取商品的各级目录文本（例如 Herramientas> Cajas y Organizadores > Bolsas Portaherramientas）、获取商品各级目录的链接；\n商品销售商名称、商品销售商链接；\n商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）；\n商品页面的问题（Preguntas y respuestas）相关的内容、问题时间、问题回答；\n商品所有问题页面的链接（Ver todas las preguntas，类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接）；\n商品评分数、商品评分数量、\n商品的评论内容、评论评分、评论时间；\n商品所有评论页面的链接（Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接）；\n任务开始的时间戳YYYYMMDDHHMMSS",
    "validate_schema": true,
    "return_raw": false,
    "extraction_schema": "{\n  \"type\": \"object\",\n  \"description\": \"电商商品完整信息提取结构\",\n  \"properties\": {\n    \"task_info\": {\n      \"type\": \"object\",\n      \"description\": \"任务执行信息\",\n      \"properties\": {\n        \"start_timestamp\": {\n          \"type\": \"string\",\n          \"pattern\": \"^[0-9]{14}$\",\n          \"description\": \"任务开始的时间戳，格式：YYYYMMDDHHMMSS\"\n        }\n      },\n      \"required\": [\n        \"start_timestamp\"\n      ]\n    },\n    \"product_basic_info\": {\n      \"type\": \"object\",\n      \"description\": \"商品基础信息\",\n      \"properties\": {\n        \"product_url\": {\n          \"type\": \"string\",\n          \"format\": \"uri\",\n          \"description\": \"商品链接\"\n        },\n        \"product_name\": {\n          \"type\": \"string\",\n          \"description\": \"商品名称\"\n        },\n        \"mlm_id\": {\n          \"type\": \"number\",\n          \"description\": \"商品MLM-ID中的数值部分\"\n        }\n      },\n      \"required\": [\n        \"product_url\",\n        \"product_name\",\n        \"mlm_id\"\n      ]\n    },\n    \"pricing_info\": {\n      \"type\": \"object\",\n      \"description\": \"价格和库存信息\",\n      \"properties\": {\n        \"sales_count\": {\n          \"type\": \"number\",\n          \"description\": \"商品销量（格式：+xxx vendidos中的数值）\"\n        },\n        \"current_price\": {\n          \"type\": \"number\",\n          \"description\": \"商品现价（数值）\"\n        },\n        \"original_price\": {\n          \"type\": \"number\",\n          \"description\": \"商品原价（数值），如果只有一个价格，或者没打折，商品现价（数值）就是商品原价\"\n        },\n        \"discount_rate\": {\n          \"type\": \"number\",\n          \"description\": \"商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100\"\n        },\n        \"stock_quantity\": {\n          \"type\": \"number\",\n          \"description\": \"商品库存数量，(+50 disponibles) 中的数值，如果页面显示Publicación pausada或者没有获取到(+50 disponibles)中的数值，那库存数量就是0\"\n        },\n        \"stocktype_IsFull\": {\n          \"type\": \"nu
index.tsx:268 2. 处理后的配置数据 (configToSave): {
  "api": {
    "base_url": "http://localhost:11234",
    "timeout": 30000,
    "max_retries": 3
  },
  "browser": {
    "headless": true,
    "verbose": false,
    "ignore_https_errors": true,
    "viewport_width": 1920,
    "viewport_height": 1080,
    "wait_for": 2,
    "timeout": 30,
    "extra_args": [
      "--no-sandbox",
      "--disable-dev-shm-usage",
      "--disable-blink-features=AutomationControlled",
      "--exclude-switches=enable-automation"
    ]
  },
  "llm": {
    "query": "提取页面中所有文章的标题、作者和发布时间",
    "provider": "openai",
    "model": "deepseek-v3-0324",
    "base_url": "https://api.lkeap.cloud.tencent.com/v1",
    "api_key": "sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi",
    "temperature": 0,
    "max_tokens": 16384,
    "top_p": 0.9
  },
  "scheduler": {
    "semaphore_count": 2,
    "mean_delay": 0.1,
    "max_range": 3,
    "pool_size": 10,
    "memory_threshold": 4096
  },
  "monitor": {
    "display_mode": "detailed",
    "show_progress": true,
    "log_errors": true
  },
  "crawler": {
    "simulate_user": true,
    "magic": true,
    "override_navigator": true,
    "remove_overlay_elements": true,
    "scan_full_page": true,
    "process_iframes": true,
    "page_timeout": 60000,
    "delay_before_return_html": 0.1,
    "scroll_delay": 0.2,
    "wait_until": "domcontentloaded",
    "image_score_threshold": 50,
    "table_score_threshold": 7,
    "cache_mode": "BYPASS",
    "check_robots_txt": false,
    "fetch_ssl_certificate": false,
    "bypass_cache": true,
    "extraction_strategy": "LLMExtractionStrategy",
    "chunking_strategy": "IdentityChunking",
    "markdown_generator": "DefaultMarkdownGenerator"
  },
  "schema_extraction": {
    "instructions": "请严格按照以下条件提取商品信息：\n1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品\n2. 如果页面有多个商品，只提取最突出显示的主商品\n3. 重点关注页面标题中提到的商品\n4. 忽略广告推荐和次要商品信息\n5. 确保提取的商品名称与页面URL或页面标题相匹配\n6. 确保提取的商品mlmid与页面URL的mlmid相匹配\n\n需要提取的信息如下：\n商品链接、商品名称、商品MLM-ID；\n商品销量（格式  +xxx vendidos）、商品原价、商品现价、商品折扣率、商品库存数量、商品是否属于FULL仓配送商品(商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\\":\\\"Full\\\"，商品有就是1，没有就是0)；\n获取商品的各级目录文本（例如 Herramientas> Cajas y Organizadores > Bolsas Portaherramientas）、获取商品各级目录的链接；\n商品销售商名称、商品销售商链接；\n商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）；\n商品页面的问题（Preguntas y respuestas）相关的内容、问题时间、问题回答；\n商品所有问题页面的链接（Ver todas las preguntas，类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接）；\n商品评分数、商品评分数量、\n商品的评论内容、评论评分、评论时间；\n商品所有评论页面的链接（Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接）；\n任务开始的时间戳YYYYMMDDHHMMSS",
    "validate_schema": true,
    "return_raw": false,
    "extraction_schema": {
      "type": "object",
      "description": "电商商品完整信息提取结构",
      "properties": {
        "task_info": {
          "type": "object",
          "description": "任务执行信息",
          "properties": {
            "start_timestamp": {
              "type": "string",
              "pattern": "^[0-9]{14}$",
              "description": "任务开始的时间戳，格式：YYYYMMDDHHMMSS"
            }
          },
          "required": [
            "start_timestamp"
          ]
        },
        "product_basic_info": {
          "type": "object",
          "description": "商品基础信息",
          "properties": {
            "product_url": {
              "type": "string",
              "format": "uri",
              "description": "商品链接"
            },
            "product_name": {
              "type": "string",
              "description": "商品名称"
            },
            "mlm_id": {
              "type": "number",
              "description": "商品MLM-ID中的数值部分"
            }
          },
          "required": [
            "product_url",
            "product_name",
            "mlm_id"
          ]
        },
        "pricing_info": {
          "type": "object",
          "description": "价格和库存信息",
          "properties": {
            "sales_count": {
              "type": "number",
              "description": "商品销量（格式：+xxx vendidos中的数值）"
            },
            "current_price": {
              "type": "number",
              "description": "商品现价（数值）"
            },
            "original_price": {
              "type": "number",
              "description": "商品原价（数值），如果只有一个价格，或者没打折，商品现价（数值）就是商品原价"
            },
            "discount_rate": {
              "type": "number",
              "description": "商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100"
            },
            "stock_quantity": {
              "type": "number",
              "description": "商品库存数量，(+50 disponibles) 中的数值，如果页面显示Publicación pausada或者没有获取到(+50 disponibles)中的数值，那库存数量就是0"
            },
            "stockty
index.tsx:269 3. 配置数据大小: 6797 字符
index.tsx:271 4. 详细配置节点分析:
index.tsx:272 - API配置: {
  "base_url": "http://localhost:11234",
  "timeout": 30000,
  "max_retries": 3
}
index.tsx:273 - 浏览器配置: {
  "headless": true,
  "verbose": false,
  "ignore_https_errors": true,
  "viewport_width": 1920,
  "viewport_height": 1080,
  "wait_for": 2,
  "timeout": 30,
  "extra_args": [
    "--no-sandbox",
    "--disable-dev-shm-usage",
    "--disable-blink-features=AutomationControlled",
    "--exclude-switches=enable-automation"
  ]
}
index.tsx:274 - 爬虫配置: {
  "simulate_user": true,
  "magic": true,
  "override_navigator": true,
  "remove_overlay_elements": true,
  "scan_full_page": true,
  "process_iframes": true,
  "page_timeout": 60000,
  "delay_before_return_html": 0.1,
  "scroll_delay": 0.2,
  "wait_until": "domcontentloaded",
  "image_score_threshold": 50,
  "table_score_threshold": 7,
  "cache_mode": "BYPASS",
  "check_robots_txt": false,
  "fetch_ssl_certificate": false,
  "bypass_cache": true,
  "extraction_strategy": "LLMExtractionStrategy",
  "chunking_strategy": "IdentityChunking",
  "markdown_generator": "DefaultMarkdownGenerator"
}
index.tsx:275 - LLM配置 (API密钥已隐藏): {
  "query": "提取页面中所有文章的标题、作者和发布时间",
  "provider": "openai",
  "model": "deepseek-v3-0324",
  "base_url": "https://api.lkeap.cloud.tencent.com/v1",
  "api_key": "***HUhi",
  "temperature": 0,
  "max_tokens": 16384,
  "top_p": 0.9
}
index.tsx:279 - Schema提取配置: {
  "instructions": "请严格按照以下条件提取商品信息：\n1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品\n2. 如果页面有多个商品，只提取最突出显示的主商品\n3. 重点关注页面标题中提到的商品\n4. 忽略广告推荐和次要商品信息\n5. 确保提取的商品名称与页面URL或页面标题相匹配\n6. 确保提取的商品mlmid与页面URL的mlmid相匹配\n\n需要提取的信息如下：\n商品链接、商品名称、商品MLM-ID；\n商品销量（格式  +xxx vendidos）、商品原价、商品现价、商品折扣率、商品库存数量、商品是否属于FULL仓配送商品(商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\\":\\\"Full\\\"，商品有就是1，没有就是0)；\n获取商品的各级目录文本（例如 Herramientas> Cajas y Organizadores > Bolsas Portaherramientas）、获取商品各级目录的链接；\n商品销售商名称、商品销售商链接；\n商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）；\n商品页面的问题（Preguntas y respuestas）相关的内容、问题时间、问题回答；\n商品所有问题页面的链接（Ver todas las preguntas，类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接）；\n商品评分数、商品评分数量、\n商品的评论内容、评论评分、评论时间；\n商品所有评论页面的链接（Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接）；\n任务开始的时间戳YYYYMMDDHHMMSS",
  "validate_schema": true,
  "return_raw": false,
  "extraction_schema": {
    "type": "object",
    "description": "电商商品完整信息提取结构",
    "properties": {
      "task_info": {
        "type": "object",
        "description": "任务执行信息",
        "properties": {
          "start_timestamp": {
            "type": "string",
            "pattern": "^[0-9]{14}$",
            "description": "任务开始的时间戳，格式：YYYYMMDDHHMMSS"
          }
        },
        "required": [
          "start_timestamp"
        ]
      },
      "product_basic_info": {
        "type": "object",
        "description": "商品基础信息",
        "properties": {
          "product_url": {
            "type": "string",
            "format": "uri",
            "description": "商品链接"
          },
          "product_name": {
            "type": "string",
            "description": "商品名称"
          },
          "mlm_id": {
            "type": "number",
            "description": "商品MLM-ID中的数值部分"
          }
        },
        "required": [
          "product_url",
          "product_name",
          "mlm_id"
        ]
      },
      "pricing_info": {
        "type": "object",
        "description": "价格和库存信息",
        "properties": {
          "sales_count": {
            "type": "number",
            "description": "商品销量（格式：+xxx vendidos中的数值）"
          },
          "current_price": {
            "type": "number",
            "description": "商品现价（数值）"
          },
          "original_price": {
            "type": "number",
            "description": "商品原价（数值），如果只有一个价格，或者没打折，商品现价（数值）就是商品原价"
          },
          "discount_rate": {
            "type": "number",
            "description": "商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100"
          },
          "stock_quantity": {
            "type": "number",
            "description": "商品库存数量，(+50 disponibles) 中的数值，如果页面显示Publicación pausada或者没有获取到(+50 disponibles)中的数值，那库存数量就是0"
          },
          "stocktype_IsFull": {
            "type": "number",
            "description": "商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\":\"Full\"，商品有就是1，没有就是0"
          }
        },
        "required": [
          "current_price"
        ]
      },
      "category_info": {
        "type": "object",
        "description": "商品目录分类信息",
        "properties": {
          "category_breadcrumb": {
            "type": "string",
            "description": "商品各级目录文本（例如：Herramientas > Cajas y Organizadores > Bolsas Portaherramientas）"
          },
          "category_hierarchy": {
            "type": "array",
            "description": "商品各级目录详细信息",
            "items": {
              "type": "object",
              "properties": {
                "category_name": {
                  "type": "string",
                  "description": "目录名称"
                },
                "category_url": {
                  "type": "string",
                  "format": "uri",
                  "description": "目录链接"
                },
                "level": {
                  "type": "integer",
                  "description": "目录层级（1为顶级，2为二级，以此类推）"
                }
              },
              "required": [
                "category_name",
                "level"
              ]
            }
          }
        },
        "required": [
          "category_breadcrumb"
        ]
      },
      "seller_info": {
        "type": "object",
        "description": "销售商信息",
        "properties": {
          "seller_name": {
            "type": "string",
            "description": "商品销售商名称"
          },
          "seller_url": {
            "type": "string",
            "format": "uri",
            "description": "商品销售商链接"
          }
        },
        "required": [
          "seller_name"
        ]
      },
      "media_info": {
        "type": "object",
        "description": "商品媒体信息",
        "properties": {
          "main_image_url": {
            "type": "string",
            "format": "uri",
            "description": "商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）"
          }
        },
        "required": [
          "main_image_url"
        ]
      },
      "qa_section": {
        "type": "object",
        "description": "问答区域信息",
        "properties": {
          "questions": {
            "type": "array",
            "description": "商品页面的问题列表",
            "items": {
              "type": "object",
              "properties": {
                "question_content": {
                  "type": "string",
                  "description": "问题内容"
                },
                "question_time": {
                  "type": "string",
                  "description": "问题时间"
                },
                "answer_content": {
                  "type": "string",
                  "description": "问题回答"
                }
              },
              "required": [
                "question_content",
                "answer_content"
              ]
            }
          },
          "all_questions_url": {
            "type": "string",
            "format": "uri",
            "description": "商品所有问题页面的链接（Ver todas las preguntas），类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接"
          }
        }
      },
      "rating_info": {
        "type": "object",
        "description": "评分信息",
        "properties": {
          "rating_score": {
            "type": "number",
            "minimum": 0,
            "maximum": 5,
            "description": "商品评分数（0-5分）"
          },
          "rating_count": {
            "type": "integer",
            "description": "商品评分数量"
          }
        },
        "required": [
          "rating_score",
          "rating_count"
        ]
      },
      "reviews_section": {
        "type": "object",
        "description": "评论区域信息",
        "properties": {
          "reviews": {
            "type": "array",
            "description": "商品评论列表",
            "items": {
              "type": "object",
              "properties": {
                "review_content": {
                  "type": "string",
                  "description": "评论内容"
                },
                "review_rating": {
                  "type": "number",
                  "minimum": 0,
                  "maximum": 5,
                  "description": "评论评分（0-5分）"
                },
                "review_time": {
                  "type": "string",
                  "description": "评论时间"
                }
              },
              "required": [
                "review_content",
                "review_time"
              ]
            }
          },
          "all_reviews_url": {
            "type": "string",
            "format": "uri",
            "description": "Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接"
          }
        }
      }
    },
    "required": [
      "product_basic_info",
      "category_info",
      "pricing_info",
      "media_info",
      "rating_info"
    ]
  }
}
index.tsx:280 - 内容处理配置: {}
index.tsx:281 - 链接过滤配置: {}
index.tsx:282 - 调度器配置: {
  "semaphore_count": 2,
  "mean_delay": 0.1,
  "max_range": 3,
  "pool_size": 10,
  "memory_threshold": 4096
}
index.tsx:283 - 监控配置: {
  "display_mode": "detailed",
  "show_progress": true,
  "log_errors": true
}
index.tsx:285 5. 配置节点是否为空对象检查:
index.tsx:286 - API配置是否为空: false
index.tsx:287 - 浏览器配置是否为空: false
index.tsx:288 - 爬虫配置是否为空: false
index.tsx:289 - LLM配置是否为空: false
index.tsx:290 - Schema提取配置是否为空: false
index.tsx:291 - 内容处理配置是否为空: true
index.tsx:292 - 链接过滤配置是否为空: true
index.tsx:293 - 调度器配置是否为空: false
index.tsx:294 - 监控配置是否为空: false
index.tsx:295 === 调试信息结束 ===
