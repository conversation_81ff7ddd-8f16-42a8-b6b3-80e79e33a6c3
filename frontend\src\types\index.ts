// 基础类型定义
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

// 任务相关类型
export interface Task extends BaseEntity {
  name: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  progress: number;
  start_time?: string;
  end_time?: string;
  error_message?: string;
  config: TaskConfig;
  results?: TaskResult[];
}

export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

export interface TaskConfig {
  urls: string[];
  crawl_rules: CrawlRule[];
  schedule?: ScheduleConfig;
  retry_config?: RetryConfig;
}

export interface ScheduleConfig {
  type: 'once' | 'daily' | 'weekly' | 'hourly' | 'custom';
  enabled: boolean;
  time?: string;
  days?: number[];
  interval?: number;
  timezone: string;
  cron_expression?: string;
  enable_random_delay?: boolean;
  random_delay_min?: number;
  random_delay_max?: number;
}

export interface RetryConfig {
  max_retries: number;
  retry_delay: number;
  backoff_factor: number;
}

// 爬虫规则类型
export interface CrawlRule extends BaseEntity {
  name: string;
  domain: string;
  selectors: SelectorConfig;
  is_active: boolean;
}

export interface SelectorConfig {
  title: string;
  price: string;
  image: string;
  description?: string;
  rating?: string;
  reviews_count?: string;
  availability?: string;
  discount?: string;
}

// 产品数据类型
export interface ProductSnapshot extends BaseEntity {
  task_id: string;
  url: string;
  title: string;
  price: number;
  original_price?: number;
  currency: string;
  image_url?: string;
  description?: string;
  rating?: number;
  reviews_count?: number;
  availability: string;
  discount_percentage?: number;
  metadata: Record<string, any>;
}

// 任务结果类型
export interface TaskResult extends BaseEntity {
  task_id: string;
  url: string;
  status: 'success' | 'failed';
  data?: ProductSnapshot;
  error_message?: string;
  execution_time: number;
}

// 系统监控类型
export interface SystemMetrics {
  timestamp: string;
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  active_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  queue_size: number;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: PaginationInfo;
}

export interface PaginationInfo {
  page: number;
  page_size: number;
  total: number;
  total_pages: number;
}

// 表格相关类型
export interface TableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: any) => React.ReactNode;
}

export interface TableProps {
  columns: TableColumn[];
  data: any[];
  loading?: boolean;
  pagination?: PaginationInfo;
  onPageChange?: (page: number, pageSize: number) => void;
  onSort?: (field: string, order: 'asc' | 'desc') => void;
  onFilter?: (filters: Record<string, any>) => void;
}

// 图表相关类型
export interface ChartData {
  name: string;
  value: number;
  timestamp?: string;
}

export interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'area';
  title: string;
  xAxis?: string;
  yAxis?: string;
  data: ChartData[];
  colors?: string[];
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: 'task_update' | 'system_metrics' | 'log_message';
  data: any;
  timestamp: string;
}

// 用户界面状态类型
export interface UIState {
  theme: 'light' | 'dark';
  sidebarCollapsed: boolean;
  loading: boolean;
  notifications: Notification[];
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

// 表单相关类型
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'number' | 'select' | 'textarea' | 'file' | 'switch' | 'date';
  required?: boolean;
  placeholder?: string;
  options?: { label: string; value: any }[];
  validation?: ValidationRule[];
}

export interface ValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom';
  value?: any;
  message: string;
}

// 文件上传类型
export interface UploadFile {
  uid: string;
  name: string;
  status: 'uploading' | 'done' | 'error';
  url?: string;
  response?: any;
  error?: any;
}

// 配置相关类型
export interface AppConfig {
  api_base_url: string;
  websocket_url: string;
  upload_max_size: number;
  supported_file_types: string[];
  default_page_size: number;
  refresh_interval: number;
}

// 日志相关类型
export interface LogEntry {
  timestamp: string;
  level: 'DEBUG' | 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';
  message: string;
  module: string;
  task_id?: string;
  extra?: Record<string, any>;
}

export interface LogFilter {
  level?: string;
  module?: string;
  task_id?: string;
  start_time?: string;
  end_time?: string;
  keyword?: string;
}
