/**
 * 爬虫池管理页面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Progress,
  Tooltip,
  message,
  Modal,
  Descriptions,
  Row,
  Col,
  Statistic,
  Badge,
  Popconfirm
} from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
  SettingOutlined,
  DeleteOutlined,
  EyeOutlined,
  HeartOutlined,
  ThunderboltOutlined,
  ClusterOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

import {
  crawlerPoolApi,
  CrawlerPoolConfig,
  PoolHealthStatus
} from '../../services';

const CrawlerPoolPage: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [pools, setPools] = useState<CrawlerPoolConfig[]>([]);
  const [healthStatuses, setHealthStatuses] = useState<Record<string, PoolHealthStatus>>({});
  const [selectedPool, setSelectedPool] = useState<CrawlerPoolConfig | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  // 加载爬虫池列表
  const loadPools = async () => {
    try {
      setLoading(true);
      const poolsData = await crawlerPoolApi.getAllPools();
      setPools(poolsData);
      
      // 加载健康状态
      await loadHealthStatuses(poolsData);
    } catch (error) {
      message.error('加载爬虫池列表失败');
      console.error('Load pools error:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载健康状态
  const loadHealthStatuses = async (poolsData: CrawlerPoolConfig[]) => {
    const statuses: Record<string, PoolHealthStatus> = {};
    
    for (const pool of poolsData) {
      try {
        const healthStatus = await crawlerPoolApi.checkPoolHealth(pool.pool_id);
        statuses[pool.pool_id] = healthStatus;
      } catch (error) {
        console.error(`Failed to load health status for pool ${pool.pool_id}:`, error);
      }
    }
    
    setHealthStatuses(statuses);
  };

  // 删除爬虫池
  const handleDeletePool = async (poolId: string) => {
    try {
      await crawlerPoolApi.deletePool(poolId);
      message.success('爬虫池删除成功');
      loadPools();
    } catch (error: any) {
      message.error(error.response?.data?.detail || '删除爬虫池失败');
    }
  };

  // 手动健康检查
  const handleManualHealthCheck = async (poolId: string) => {
    try {
      setLoading(true);
      await crawlerPoolApi.manualHealthCheck(poolId);
      message.success('健康检查完成');
      
      // 重新加载健康状态
      const healthStatus = await crawlerPoolApi.checkPoolHealth(poolId);
      setHealthStatuses(prev => ({
        ...prev,
        [poolId]: healthStatus
      }));
    } catch (error) {
      message.error('健康检查失败');
    } finally {
      setLoading(false);
    }
  };

  // 查看池详情
  const handleViewDetails = (pool: CrawlerPoolConfig) => {
    setSelectedPool(pool);
    setDetailModalVisible(true);
  };

  useEffect(() => {
    loadPools();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 表格列定义
  const columns = [
    {
      title: '池名称',
      dataIndex: 'pool_name',
      key: 'pool_name',
      render: (text: string, record: CrawlerPoolConfig) => (
        <Space>
          <ClusterOutlined />
          <strong>{text}</strong>
          {record.pool_id === 'default_pool' && <Tag color="blue">默认</Tag>}
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '配置数量',
      key: 'config_count',
      render: (record: CrawlerPoolConfig) => (
        <Space>
          <Badge count={record.crawler_config_ids?.length || record.backends?.length || 0} showZero color="blue" />
          <span>个配置</span>
        </Space>
      ),
    },
    {
      title: '负载均衡策略',
      dataIndex: 'load_balance_strategy',
      key: 'load_balance_strategy',
      render: (strategy: string) => (
        <Tag color="purple">
          {crawlerPoolApi.getLoadBalanceStrategyText(strategy)}
        </Tag>
      ),
    },
    {
      title: '健康状态',
      key: 'health_status',
      render: (record: CrawlerPoolConfig) => {
        const healthStatus = healthStatuses[record.pool_id];
        if (!healthStatus) {
          return <Tag color="gray">检查中...</Tag>;
        }

        const healthPercent = Math.round(healthStatus.overall_health_score * 100);
        // const color = healthPercent >= 80 ? 'green' : healthPercent >= 50 ? 'orange' : 'red';
        
        return (
          <Space direction="vertical" size="small">
            <Progress 
              percent={healthPercent} 
              size="small" 
              status={healthPercent >= 80 ? 'success' : healthPercent >= 50 ? 'active' : 'exception'}
            />
            <span style={{ fontSize: '12px', color: '#666' }}>
              {healthStatus.healthy_backends}/{healthStatus.total_backends} 健康
            </span>
          </Space>
        );
      },
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: CrawlerPoolConfig) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          
          <Tooltip title="健康检查">
            <Button
              type="text"
              icon={<HeartOutlined />}
              onClick={() => handleManualHealthCheck(record.pool_id)}
            />
          </Tooltip>
          
          <Tooltip title="配置管理">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => {
                // 根据架构类型选择编辑页面
                if (record.crawler_config_ids) {
                  navigate(`/crawler-pool/${record.pool_id}/edit-new`);
                } else {
                  navigate(`/crawler-pool/${record.pool_id}/edit`);
                }
              }}
            />
          </Tooltip>
          
          {record.pool_id !== 'default_pool' && (
            <Popconfirm
              title="确定要删除这个爬虫池吗？"
              description="删除后无法恢复，请谨慎操作。"
              onConfirm={() => handleDeletePool(record.pool_id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button 
                  type="text" 
                  danger 
                  icon={<DeleteOutlined />} 
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <Space>
            <ClusterOutlined />
            爬虫池管理
            <Tag color="blue">Pool Management</Tag>
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadPools}
              loading={loading}
            >
              刷新
            </Button>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => navigate('/crawler-pool/create-new')}
              >
                创建爬虫池 (新架构)
              </Button>
              <Button
                icon={<PlusOutlined />}
                onClick={() => navigate('/crawler-pool/create')}
              >
                创建爬虫池 (传统)
              </Button>
            </Space>
          </Space>
        }
      >
        {/* 统计信息 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Statistic
              title="总池数"
              value={pools?.length || 0}
              prefix={<ClusterOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="总配置数"
              value={pools?.reduce((sum, pool) => sum + (pool.crawler_config_ids?.length || pool.backends?.length || 0), 0) || 0}
              prefix={<ThunderboltOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="健康配置"
              value={Object.values(healthStatuses).reduce((sum, status) => sum + (status.healthy_backends || 0), 0)}
              prefix={<HeartOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="平均健康度"
              value={Object.values(healthStatuses).length > 0
                ? Math.round(Object.values(healthStatuses).reduce((sum, status) => sum + (status.overall_health_score || 0), 0) / Object.values(healthStatuses).length * 100)
                : 0
              }
              suffix="%"
              prefix={<HeartOutlined />}
              valueStyle={{
                color: Object.values(healthStatuses).length > 0 &&
                       Object.values(healthStatuses).reduce((sum, status) => sum + (status.overall_health_score || 0), 0) / Object.values(healthStatuses).length >= 0.8
                  ? '#3f8600' : '#cf1322'
              }}
            />
          </Col>
        </Row>

        {/* 爬虫池列表 */}
        <Table
          columns={columns}
          dataSource={pools}
          rowKey="pool_id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个爬虫池`,
          }}
        />
      </Card>

      {/* 详情模态框 */}
      <Modal
        title={
          <Space>
            <ClusterOutlined />
            爬虫池详情
          </Space>
        }
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
          <Button 
            key="edit" 
            type="primary" 
            onClick={() => {
              setDetailModalVisible(false);
              // 根据架构类型选择编辑页面
              if (selectedPool?.crawler_config_ids) {
                navigate(`/crawler-pool/${selectedPool.pool_id}/edit-new`);
              } else {
                navigate(`/crawler-pool/${selectedPool?.pool_id}/edit`);
              }
            }}
          >
            编辑配置
          </Button>,
        ]}
        width={800}
      >
        {selectedPool && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="池名称">{selectedPool.pool_name}</Descriptions.Item>
            <Descriptions.Item label="池ID">{selectedPool.pool_id}</Descriptions.Item>
            <Descriptions.Item label="描述" span={2}>{selectedPool.description || '无'}</Descriptions.Item>
            <Descriptions.Item label="负载均衡策略">
              {crawlerPoolApi.getLoadBalanceStrategyText(selectedPool.load_balance_strategy)}
            </Descriptions.Item>
            <Descriptions.Item label="健康检查间隔">{selectedPool.health_check_interval}秒</Descriptions.Item>
            <Descriptions.Item label="故障阈值">{selectedPool.failure_threshold}</Descriptions.Item>
            <Descriptions.Item label="恢复阈值">{selectedPool.recovery_threshold}</Descriptions.Item>
            <Descriptions.Item label="创建时间" span={2}>
              {selectedPool.created_at ? new Date(selectedPool.created_at).toLocaleString() : '未知'}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default CrawlerPoolPage;

// 导出其他组件供路由使用
export { default as CrawlerPoolCreate } from './CrawlerPoolCreate';
